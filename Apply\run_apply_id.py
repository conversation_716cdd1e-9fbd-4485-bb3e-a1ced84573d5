"""
申请编号程序运行脚本
使用说明：
1. 请先在config.py文件中填写您的登录信息和项目代号
2. 将需要申请编号的文件放入input_files文件夹
3. 运行此脚本

注意：
- 支持的文件类型：.docx, .xlsx
- 文件会根据名称自动识别类型（DVP/PPL/FN）
- 程序会自动更新文件名和内容中的编号
"""

import sys
import os
from pathlib import Path

def main():
    print("="*60)
    print("                 申请编号自动化程序")
    print("="*60)
    
    # 检查依赖
    try:
        from selenium import webdriver
        from docx import Document
        from openpyxl import load_workbook
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return
    
    # 检查配置
    try:
        import config
        if not config.USERNAME or not config.PASSWORD or not config.PROJECT_CODE:
            print("❌ 请先在config.py文件中填写完整的配置信息:")
            print("   - USERNAME: 用户名")
            print("   - PASSWORD: 密码")
            print("   - PROJECT_CODE: 车型代号")
            return
    except ImportError:
        print("❌ 找不到config.py配置文件")
        return
    
    # 检查输入文件夹
    input_folder = Path("input_files")
    if not input_folder.exists():
        input_folder.mkdir(exist_ok=True)
        print(f"📁 已创建输入文件夹: {input_folder}")
    
    # 检查是否有文件需要处理
    files = list(input_folder.glob("*.docx")) + list(input_folder.glob("*.xlsx"))
    if not files:
        print(f"⚠️  输入文件夹中没有找到.docx或.xlsx文件")
        print(f"   请将需要申请编号的文件放入: {input_folder}")
        return
    
    print(f"✅ 找到 {len(files)} 个文件需要处理:")
    for file in files:
        print(f"   - {file.name}")
    
    print(f"\n📋 当前配置:")
    print(f"   用户名: {config.USERNAME}")
    print(f"   项目代号: {config.PROJECT_CODE}")
    print(f"   DMS地址: {config.DMS_URL}")
    
    # 检查并显示运行模式
    headless_mode = getattr(config, 'HEADLESS_MODE', False)
    mode_text = "🔇 静默模式（后台运行）" if headless_mode else "🖥️ 显示模式（可见浏览器）"
    print(f"   运行模式: {mode_text}")
    
    print(f"\n🚀 开始执行申请编号程序...")
    
    # 运行主程序
    try:
        from apply_id_automation import ApplyIDAutomation
        app = ApplyIDAutomation()
        app.run()
        print("\n🎉 程序执行完成，已自动退出")
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n❌ 程序异常退出")

if __name__ == "__main__":
    main()
