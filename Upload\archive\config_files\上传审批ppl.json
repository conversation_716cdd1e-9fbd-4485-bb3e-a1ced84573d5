{"Name": "上传审批ppl", "CreationDate": "2025-6-24", "Commands": [{"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr/td[2]/div/span/span/span", "xpath=//td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(1) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "相关方角色标签"}, {"Command": "click", "Target": "id=el-id-540-71", "Value": "", "Targets": ["id=el-id-540-71", "xpath=//*[@id=\"el-id-540-71\"]", "xpath=//input[@id='el-id-540-71']", "xpath=//td[3]/div/div/div/div/div/div/div/input", "css=#el-id-540-71"], "Description": "里面用PPL_Signatory的邮箱找到人"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[2]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[2]/td[2]/div/span/span/span", "xpath=//tr[2]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(2) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "数据管理员标签"}, {"Command": "click", "Target": "id=el-id-540-72", "Value": "", "Targets": ["id=el-id-540-72", "xpath=//*[@id=\"el-id-540-72\"]", "xpath=//input[@id='el-id-540-72']", "xpath=//tr[2]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-540-72"], "Description": "填数据管理员的工号找到人"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[3]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[3]/td[2]/div/span/span/span", "xpath=//tr[3]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(3) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "科长角色标签"}, {"Command": "click", "Target": "id=el-id-540-73", "Value": "", "Targets": ["id=el-id-540-73", "xpath=//*[@id=\"el-id-540-73\"]", "xpath=//input[@id='el-id-540-73']", "xpath=//tr[3]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-540-73"], "Description": "科长的邮箱找到人"}]}