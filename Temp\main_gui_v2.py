import sys
import os
import json
import shutil
import subprocess
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox,
    QGroupBox, QListWidget, QTextEdit, QTabWidget, QFileDialog,
    QMessageBox, QProgressBar, QSplitter, QFrame, QListWidgetItem,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QScrollArea,
    QButtonGroup, QRadioButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap


class ApplyIDWorker(QThread):
    """申请编号工作线程"""
    progress = pyqtSignal(int, str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, vehicle_code, username, password, files_info, silent_mode=False, test_mode=False):
        super().__init__()
        self.vehicle_code = vehicle_code
        self.username = username
        self.password = password
        self.files_info = files_info
        self.silent_mode = silent_mode
        self.test_mode = test_mode
        
    def run(self):
        try:
            from selenium_automation import ApplyIDAutomator
            automator = ApplyIDAutomator(self.silent_mode, self.test_mode)
            
            if not automator.setup_driver():
                self.finished.emit(False, "初始化浏览器失败")
                return
            
            # 登录
            self.progress.emit(10, "正在登录...")
            if not automator.login(self.username, self.password):
                automator.close_driver()
                self.finished.emit(False, "登录失败")
                return
            
            total_files = len(self.files_info)
            success_count = 0
            
            # 申请每个文件的编号
            for i, file_info in enumerate(self.files_info):
                self.progress.emit(20 + (i * 60 // total_files), f"申请编号: {file_info['file_name']}")
                
                file_id = automator.apply_file_id(file_info)
                if file_id:
                    success_count += 1
                    # 更新文件
                    from file_manager import FileManager
                    file_manager = FileManager(Path(__file__).parent)
                    file_manager.update_file_with_id(self.vehicle_code, file_info['file_name'], file_id)
            
            automator.close_driver()
            
            if success_count > 0:
                self.finished.emit(True, f"成功申请 {success_count}/{total_files} 个文件编号")
            else:
                self.finished.emit(False, "所有文件编号申请失败")
                
        except Exception as e:
            self.finished.emit(False, f"申请编号过程中出错: {str(e)}")


class UploadWorker(QThread):
    """上传审批工作线程"""
    progress = pyqtSignal(int, str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, vehicle_code, username, password, upload_files, reviewers_info, silent_mode=False, test_mode=False):
        super().__init__()
        self.vehicle_code = vehicle_code
        self.username = username
        self.password = password
        self.upload_files = upload_files
        self.reviewers_info = reviewers_info
        self.silent_mode = silent_mode
        self.test_mode = test_mode
        
    def run(self):
        try:
            from selenium_automation import UploadApprovalAutomator
            automator = UploadApprovalAutomator(self.silent_mode, self.test_mode)
            
            if not automator.setup_driver():
                self.finished.emit(False, "初始化浏览器失败")
                return
            
            # 登录
            self.progress.emit(10, "正在登录...")
            if not automator.login(self.username, self.password):
                automator.close_driver()
                self.finished.emit(False, "登录失败")
                return
            
            total_files = len(self.upload_files)
            success_count = 0
            
            # 上传每个文件
            for i, file_info in enumerate(self.upload_files):
                self.progress.emit(20 + (i * 60 // total_files), f"上传文件: {file_info['file_name']}")
                
                if automator.upload_file_for_approval(file_info, self.reviewers_info):
                    success_count += 1
                    # 更新上传状态
                    from file_manager import FileManager
                    file_manager = FileManager(Path(__file__).parent)
                    file_manager.update_upload_status(self.vehicle_code, file_info['file_name'], 'Y')
            
            automator.close_driver()
            
            if success_count > 0:
                self.finished.emit(True, f"成功上传 {success_count}/{total_files} 个文件")
            else:
                self.finished.emit(False, "所有文件上传失败")
                
        except Exception as e:
            self.finished.emit(False, f"上传过程中出错: {str(e)}")


class VehicleManagementGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("车型文件管理系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置路径
        self.base_path = Path(__file__).parent
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.fillin_path = self.base_path / "Fillin"
        self.final_approval_path = self.base_path / "Final_Approval_Documents"
        self.update_templates_path = self.base_path / "Update_Templates"
        self.logs_path = self.base_path / "logs"
        
        # 创建必要的目录
        self.vehicles_path.mkdir(exist_ok=True)
        self.final_approval_path.mkdir(exist_ok=True)
        self.logs_path.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 初始化UI
        self.init_ui()
        
        # 设置样式
        self.setup_style()
        
        # 当前选择的车型代号
        self.current_vehicle_code = ""
        
        # 初始化车型列表
        self.load_vehicle_codes()
        
        # 工作线程
        self.apply_worker = None
        self.upload_worker = None

    def setup_logging(self):
        """设置日志"""
        log_file = self.logs_path / f"vehicle_management_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建主要工作区域
        self.create_main_workspace(main_layout)
        
        # 创建状态栏
        self.create_status_bar()    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setFixedHeight(120)  # 增加高度
        
        title_layout = QVBoxLayout(title_frame)  # 改为垂直布局
        
        # 第一行：标题
        title_row = QHBoxLayout()
        title_label = QLabel("车型文件管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        title_row.addWidget(title_label)
        title_row.addStretch()
        
        # 第二行：车型设置和快捷操作
        controls_row = QHBoxLayout()
        
        # 车型选择区域 - 紧凑设计
        vehicle_layout = QHBoxLayout()
        vehicle_layout.addWidget(QLabel("车型代号:"))
        self.vehicle_combo = QComboBox()
        self.vehicle_combo.setEditable(True)
        self.vehicle_combo.setMinimumWidth(100)
        self.vehicle_combo.currentTextChanged.connect(self.on_vehicle_changed)
        vehicle_layout.addWidget(self.vehicle_combo)
        
        self.setup_vehicle_btn = QPushButton("设置车型信息")
        self.setup_vehicle_btn.clicked.connect(self.setup_vehicle_info)
        vehicle_layout.addWidget(self.setup_vehicle_btn)
        
        # 快捷按钮区域 - 紧凑设计
        self.open_final_approval_btn = QPushButton("审批文件夹")
        self.open_final_approval_btn.clicked.connect(self.open_final_approval_folder)
        
        self.open_vehicles_btn = QPushButton("车型文件夹")
        self.open_vehicles_btn.clicked.connect(self.open_vehicles_folder)
        
        self.open_templates_btn = QPushButton("模板文件夹")
        self.open_templates_btn.clicked.connect(self.open_templates_folder)
        
        self.open_logs_btn = QPushButton("日志文件夹")
        self.open_logs_btn.clicked.connect(self.open_logs_folder)
        
        controls_row.addLayout(vehicle_layout)
        controls_row.addWidget(QFrame())  # 分隔符
        controls_row.addWidget(QLabel("快捷操作:"))
        controls_row.addWidget(self.open_final_approval_btn)
        controls_row.addWidget(self.open_vehicles_btn)
        controls_row.addWidget(self.open_templates_btn)
        controls_row.addWidget(self.open_logs_btn)
        controls_row.addStretch()
        
        title_layout.addLayout(title_row)
        title_layout.addLayout(controls_row)
        
        parent_layout.addWidget(title_frame)

    def create_main_workspace(self, parent_layout):
        """创建主工作区域"""
        # 创建选项卡widget
        self.tab_widget = QTabWidget()
        
        # 文件操作选项卡
        self.create_file_operations_tab()
        
        # 申请审批选项卡
        self.create_application_approval_tab()
        
        # 上传审批选项卡
        self.create_upload_approval_tab()
        
        # 日志监控选项卡
        self.create_log_monitor_tab()
        
        parent_layout.addWidget(self.tab_widget)

    def create_file_operations_tab(self):
        """创建文件操作选项卡"""
        file_ops_widget = QWidget()
        layout = QVBoxLayout(file_ops_widget)
        
        # 操作类型选择
        operations_group = QGroupBox("操作类型选择")
        operations_layout = QHBoxLayout(operations_group)
        
        self.apply_id_cb = QCheckBox("申请文件编号")
        self.fill_content_cb = QCheckBox("填写文件内容")
        operations_layout.addWidget(self.apply_id_cb)
        operations_layout.addWidget(self.fill_content_cb)
        operations_layout.addStretch()
        
        layout.addWidget(operations_group)
        
        # 文件选择区域
        file_selection_group = QGroupBox("文件选择")
        file_selection_layout = QVBoxLayout(file_selection_group)
        
        # 文件类型分组
        file_types_layout = QHBoxLayout()
        
        # DVP文件
        dvp_group = QGroupBox("DVP文件")
        dvp_layout = QVBoxLayout(dvp_group)
        self.dvp_cb = QCheckBox("DVP-系统设计验证计划")
        dvp_layout.addWidget(self.dvp_cb)
        file_types_layout.addWidget(dvp_group)
        
        # PPL文件
        ppl_group = QGroupBox("PPL文件")
        ppl_layout = QVBoxLayout(ppl_group)
        self.ppl_cb = QCheckBox("PPL-车辆匹配计划")
        ppl_layout.addWidget(self.ppl_cb)
        file_types_layout.addWidget(ppl_group)
        
        # FN文件
        fn_group = QGroupBox("FN文件")
        fn_layout = QVBoxLayout(fn_group)
        
        # 创建滚动区域以显示多个FN文件
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        self.fn_checkboxes = {}
        fn_files = [
            "FN_IMU", "FN_VCU", "FN_IPB", "FN_ESP_BWA", "FN_EPS", 
            "FN_EPSA", "FN_EPB", "FN_DISUS_A", "FN_DISUS_C", 
            "FN_DISUS_P", "FN_DISUS_X", "FN_DISUS_M", "FN_域控"
        ]
        
        for fn_file in fn_files:
            cb = QCheckBox(fn_file.replace("_", "-"))
            self.fn_checkboxes[fn_file] = cb
            scroll_layout.addWidget(cb)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        
        fn_layout.addWidget(scroll_area)
        file_types_layout.addWidget(fn_group)
        
        file_selection_layout.addLayout(file_types_layout)
        
        # 全选/取消全选按钮
        select_buttons_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_files)
        self.deselect_all_btn = QPushButton("取消全选")
        self.deselect_all_btn.clicked.connect(self.deselect_all_files)
        
        select_buttons_layout.addWidget(self.select_all_btn)
        select_buttons_layout.addWidget(self.deselect_all_btn)
        select_buttons_layout.addStretch()
        
        file_selection_layout.addLayout(select_buttons_layout)
        layout.addWidget(file_selection_group)
        
        # 执行按钮
        execute_layout = QHBoxLayout()
        self.execute_btn = QPushButton("执行操作")
        self.execute_btn.clicked.connect(self.execute_file_operations)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        execute_layout.addStretch()
        execute_layout.addWidget(self.execute_btn)
        execute_layout.addStretch()
        
        layout.addLayout(execute_layout)
        
        self.tab_widget.addTab(file_ops_widget, "文件操作")

    def create_application_approval_tab(self):
        """创建申请审批选项卡"""
        app_approval_widget = QWidget()
        layout = QVBoxLayout(app_approval_widget)
        
        # 登录信息设置
        login_group = QGroupBox("登录设置")
        login_layout = QFormLayout(login_group)
        
        self.username_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        
        login_layout.addRow("用户名:", self.username_edit)
        login_layout.addRow("密码:", self.password_edit)
        
        layout.addWidget(login_group)
        
        # 申请选项
        options_group = QGroupBox("申请选项")
        options_layout = QHBoxLayout(options_group)
        
        self.silent_mode_cb = QCheckBox("静默模式")
        self.test_mode_cb = QCheckBox("测试模式")
        
        options_layout.addWidget(self.silent_mode_cb)
        options_layout.addWidget(self.test_mode_cb)
        options_layout.addStretch()
        
        layout.addWidget(options_group)
        
        # 执行申请按钮
        apply_layout = QHBoxLayout()
        self.apply_id_btn = QPushButton("申请文件编号")
        self.apply_id_btn.clicked.connect(self.apply_file_ids)
        self.apply_id_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        
        apply_layout.addStretch()
        apply_layout.addWidget(self.apply_id_btn)
        apply_layout.addStretch()
        
        layout.addLayout(apply_layout)
        
        # 进度显示
        self.apply_progress = QProgressBar()
        self.apply_progress.setVisible(False)
        layout.addWidget(self.apply_progress)
        
        layout.addStretch()
        
        self.tab_widget.addTab(app_approval_widget, "申请编号")

    def create_upload_approval_tab(self):
        """创建上传审批选项卡"""
        upload_approval_widget = QWidget()
        layout = QVBoxLayout(upload_approval_widget)
        
        # 上传选项
        upload_options_group = QGroupBox("上传选项")
        upload_options_layout = QHBoxLayout(upload_options_group)
        
        self.upload_silent_mode_cb = QCheckBox("静默模式")
        self.upload_test_mode_cb = QCheckBox("测试模式")
        
        upload_options_layout.addWidget(self.upload_silent_mode_cb)
        upload_options_layout.addWidget(self.upload_test_mode_cb)
        upload_options_layout.addStretch()
        
        layout.addWidget(upload_options_group)
        
        # 上传状态表格
        status_group = QGroupBox("文件上传状态")
        status_layout = QVBoxLayout(status_group)
        
        self.upload_status_table = QTableWidget()
        self.upload_status_table.setColumnCount(5)
        self.upload_status_table.setHorizontalHeaderLabels([
            "文件名", "编号", "完整文件名", "已填写", "已上传"
        ])
        self.upload_status_table.horizontalHeader().setStretchLastSection(True)
        
        status_layout.addWidget(self.upload_status_table)
        layout.addWidget(status_group)
        
        # 执行上传按钮
        upload_layout = QHBoxLayout()
        self.upload_approval_btn = QPushButton("上传审批文件")
        self.upload_approval_btn.clicked.connect(self.upload_approval_files)
        self.upload_approval_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        upload_layout.addStretch()
        upload_layout.addWidget(self.upload_approval_btn)
        upload_layout.addStretch()
        
        layout.addLayout(upload_layout)
        
        # 进度显示
        self.upload_progress = QProgressBar()
        self.upload_progress.setVisible(False)
        layout.addWidget(self.upload_progress)
        
        self.tab_widget.addTab(upload_approval_widget, "上传审批")

    def create_log_monitor_tab(self):
        """创建日志监控选项卡"""
        log_monitor_widget = QWidget()
        layout = QVBoxLayout(log_monitor_widget)
        
        # 日志控制按钮
        log_controls_layout = QHBoxLayout()
        
        self.open_main_log_btn = QPushButton("主程序日志")
        self.open_main_log_btn.clicked.connect(self.open_main_log)
        
        self.open_subprocess_log_btn = QPushButton("子进程日志")
        self.open_subprocess_log_btn.clicked.connect(self.open_subprocess_log)
        
        self.open_update_templates_btn = QPushButton("更新模板文件夹")
        self.open_update_templates_btn.clicked.connect(self.open_update_templates_folder)
        
        self.replace_templates_btn = QPushButton("替换模板")
        self.replace_templates_btn.clicked.connect(self.replace_templates)
        
        log_controls_layout.addWidget(self.open_main_log_btn)
        log_controls_layout.addWidget(self.open_subprocess_log_btn)
        log_controls_layout.addWidget(self.open_update_templates_btn)
        log_controls_layout.addWidget(self.replace_templates_btn)
        log_controls_layout.addStretch()
        
        layout.addLayout(log_controls_layout)
        
        # 实时日志显示
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        
        layout.addWidget(QLabel("实时日志:"))
        layout.addWidget(self.log_display)
        
        self.tab_widget.addTab(log_monitor_widget, "日志监控")

    def create_status_bar(self):
        """创建状态栏"""
        self.statusBar().showMessage("准备就绪")
        
        # 添加当前时间显示
        self.time_label = QLabel()
        self.statusBar().addPermanentWidget(self.time_label)
        
        # 定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def setup_style(self):
        """设置应用程序样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
            QPushButton:pressed {
                background-color: #1a252f;
            }
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            }
            QLineEdit {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            }
            QCheckBox {
                spacing: 5px;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #95a5a6;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #34495e;
            }
            QTabBar::tab:hover {
                background-color: #7f8c8d;
            }
        """)

    def load_vehicle_codes(self):
        """加载车型代号列表"""
        # 从Vehicles目录读取已有的车型代号
        if self.vehicles_path.exists():
            vehicle_codes = [d.name for d in self.vehicles_path.iterdir() if d.is_dir()]
            self.vehicle_combo.addItems(vehicle_codes)
        
        # 添加一些常用的车型代号
        common_codes = ["HAHB", "QYHA", "GXHD", "SYHC"]
        for code in common_codes:
            if self.vehicle_combo.findText(code) == -1:
                self.vehicle_combo.addItem(code)

    def on_vehicle_changed(self, vehicle_code):
        """车型代号改变时的处理"""
        self.current_vehicle_code = vehicle_code.strip().upper()
        if self.current_vehicle_code:
            self.statusBar().showMessage(f"当前车型: {self.current_vehicle_code}")
            self.load_vehicle_file_status()

    def setup_vehicle_info(self):
        """设置车型信息"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择或输入车型代号!")
            return
        
        try:
            from file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            created, target_file = file_manager.setup_vehicle_folder(self.current_vehicle_code)
            
            if created:
                QMessageBox.information(self, "成功", f"车型 {self.current_vehicle_code} 初始化完成!")
            else:
                QMessageBox.information(self, "提示", f"车型 {self.current_vehicle_code} 已存在，跳过初始化。")
            
            # 打开Excel文件供用户编辑
            os.startfile(str(target_file))
            
        except Exception as e:
            self.logger.error(f"设置车型信息失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"设置车型信息失败: {str(e)}")

    def load_vehicle_file_status(self):
        """加载车型文件状态"""
        try:
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            info_dir = vehicle_dir / "information"
            excel_file = info_dir / f"{self.current_vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                df = pd.read_excel(excel_file, sheet_name="File_Status")
                
                # 更新上传状态表格
                self.upload_status_table.setRowCount(len(df))
                for i, row in df.iterrows():
                    self.upload_status_table.setItem(i, 0, QTableWidgetItem(str(row.get('file_name', ''))))
                    self.upload_status_table.setItem(i, 1, QTableWidgetItem(str(row.get('code', ''))))
                    self.upload_status_table.setItem(i, 2, QTableWidgetItem(str(row.get('numbered_file', ''))))
                    self.upload_status_table.setItem(i, 3, QTableWidgetItem(str(row.get('is_fillin', ''))))
                    self.upload_status_table.setItem(i, 4, QTableWidgetItem(str(row.get('is_upload', ''))))
                
        except Exception as e:
            self.logger.error(f"加载车型文件状态失败: {str(e)}")

    def select_all_files(self):
        """全选所有文件"""
        self.dvp_cb.setChecked(True)
        self.ppl_cb.setChecked(True)
        for cb in self.fn_checkboxes.values():
            cb.setChecked(True)

    def deselect_all_files(self):
        """取消全选"""
        self.dvp_cb.setChecked(False)
        self.ppl_cb.setChecked(False)
        for cb in self.fn_checkboxes.values():
            cb.setChecked(False)

    def execute_file_operations(self):
        """执行文件操作"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        if not (self.apply_id_cb.isChecked() or self.fill_content_cb.isChecked()):
            QMessageBox.warning(self, "警告", "请选择至少一种操作类型!")
            return
        
        # 收集选中的文件
        selected_files = []
        if self.dvp_cb.isChecked():
            selected_files.append("DVP")
        if self.ppl_cb.isChecked():
            selected_files.append("PPL")
        for fn_type, cb in self.fn_checkboxes.items():
            if cb.isChecked():
                selected_files.append(fn_type)
        
        if not selected_files:
            QMessageBox.warning(self, "警告", "请选择至少一个文件!")
            return
        
        try:
            from file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            # 复制模板文件到车型文件夹
            copied_files = file_manager.copy_template_files(self.current_vehicle_code, selected_files)
            
            operations_completed = []
            
            if self.apply_id_cb.isChecked():
                success = self.apply_file_numbers_with_manager(file_manager, selected_files)
                if success:
                    operations_completed.append("申请编号")
            
            if self.fill_content_cb.isChecked():
                success = self.fill_file_contents_with_manager(file_manager, selected_files)
                if success:
                    operations_completed.append("填写内容")
            
            message = f"文件操作完成!\n复制文件: {len(copied_files)} 个\n完成操作: {', '.join(operations_completed)}"
            QMessageBox.information(self, "成功", message)
            
            # 刷新文件状态表
            self.load_vehicle_file_status()
            
        except Exception as e:
            self.logger.error(f"执行文件操作失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行文件操作失败: {str(e)}")

    def apply_file_numbers_with_manager(self, file_manager, selected_files):
        """使用文件管理器申请文件编号"""
        try:
            # 这里暂时跳过申请编号，因为需要selenium
            self.logger.info(f"申请编号功能: {selected_files}")
            QMessageBox.information(self, "提示", "申请编号功能需要在另一台电脑上运行")
            return False
        except Exception as e:
            self.logger.error(f"申请文件编号失败: {str(e)}")
            return False

    def fill_file_contents_with_manager(self, file_manager, selected_files):
        """使用文件管理器填写文件内容"""
        try:
            fill_files = file_manager.get_files_for_fill_content(self.current_vehicle_code, selected_files)
            
            success_count = 0
            for file_info in fill_files:
                if file_manager.fill_file_content(self.current_vehicle_code, file_info):
                    success_count += 1
            
            if success_count > 0:
                self.logger.info(f"成功填写 {success_count} 个文件")
                return True
            else:
                self.logger.warning("没有文件填写成功")
                return False
                
        except Exception as e:
            self.logger.error(f"填写文件内容失败: {str(e)}")
            return False

    def apply_file_ids(self):
        """申请文件编号"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        try:
            from file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            # 获取需要申请编号的文件
            apply_files = file_manager.get_files_for_apply_id(self.current_vehicle_code)
            
            if not apply_files:
                QMessageBox.information(self, "提示", "没有需要申请编号的文件")
                return
            
            # 准备文件信息
            files_info = []
            for file_info in apply_files:
                files_info.append({
                    'file_type': file_info['file_type'],
                    'project_code': self.current_vehicle_code,
                    'file_name': file_info['file_name'],
                    'document_type': self.get_document_type(file_info['file_type'])
                })
            
            # 启动申请编号工作线程
            self.apply_worker = ApplyIDWorker(
                self.current_vehicle_code,
                username,
                password,
                files_info,
                self.silent_mode_cb.isChecked(),
                self.test_mode_cb.isChecked()
            )
            
            self.apply_worker.progress.connect(self.on_apply_progress)
            self.apply_worker.finished.connect(self.on_apply_finished)
            
            self.apply_progress.setVisible(True)
            self.apply_id_btn.setEnabled(False)
            self.apply_worker.start()
            
        except Exception as e:
            self.logger.error(f"启动申请编号失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动申请编号失败: {str(e)}")

    def get_document_type(self, file_type):
        """根据文件类型获取文档类型"""
        if file_type == "DVP":
            return "DVP-系统设计验证计划"
        elif file_type == "PPL":
            return "PPL-车辆匹配计划"
        elif file_type.startswith("FN"):
            return "FN-接口定义/功能输入通知单"
        else:
            return "未知类型"

    def on_apply_progress(self, value, message):
        """申请编号进度更新"""
        self.apply_progress.setValue(value)
        self.statusBar().showMessage(message)

    def on_apply_finished(self, success, message):
        """申请编号完成"""
        self.apply_progress.setVisible(False)
        self.apply_id_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.load_vehicle_file_status()
        else:
            QMessageBox.critical(self, "失败", message)
        
        self.statusBar().showMessage("准备就绪")

    def upload_approval_files(self):
        """上传审批文件"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        try:
            from file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            # 获取需要上传的文件
            upload_files = file_manager.get_upload_files(self.current_vehicle_code)
            
            if not upload_files:
                QMessageBox.information(self, "提示", "没有需要上传的文件")
                return
            
            # 获取审批人员信息
            reviewers_info = file_manager.get_reviewer_info(self.current_vehicle_code)
            
            if not reviewers_info:
                QMessageBox.warning(self, "警告", "无法获取审批人员信息，请检查车型配置文件")
                return
            
            # 启动上传工作线程
            self.upload_worker = UploadWorker(
                self.current_vehicle_code,
                username,
                password,
                upload_files,
                reviewers_info,
                self.upload_silent_mode_cb.isChecked(),
                self.upload_test_mode_cb.isChecked()
            )
            
            self.upload_worker.progress.connect(self.on_upload_progress)
            self.upload_worker.finished.connect(self.on_upload_finished)
            
            self.upload_progress.setVisible(True)
            self.upload_approval_btn.setEnabled(False)
            self.upload_worker.start()
            
        except Exception as e:
            self.logger.error(f"启动上传审批失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动上传审批失败: {str(e)}")

    def on_upload_progress(self, value, message):
        """上传进度更新"""
        self.upload_progress.setValue(value)
        self.statusBar().showMessage(message)

    def on_upload_finished(self, success, message):
        """上传完成"""
        self.upload_progress.setVisible(False)
        self.upload_approval_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.load_vehicle_file_status()
        else:
            QMessageBox.critical(self, "失败", message)
        
        self.statusBar().showMessage("准备就绪")

    def open_final_approval_folder(self):
        """打开最终审批文件夹"""
        try:
            os.startfile(str(self.final_approval_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_logs_folder(self):
        """打开日志文件夹"""
        try:
            os.startfile(str(self.logs_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_main_log(self):
        """打开主程序日志"""
        try:
            log_files = list(self.logs_path.glob("vehicle_management_*.log"))
            if log_files:
                # 打开最新的日志文件
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                os.startfile(str(latest_log))
            else:
                QMessageBox.information(self, "提示", "没有找到主程序日志文件")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开日志文件: {str(e)}")

    def open_subprocess_log(self):
        """打开子进程日志"""
        try:
            # 这里可以打开Fillin程序的日志
            dvp_logs = self.fillin_path / "dvp" / "logs"
            fn_logs = self.fillin_path / "fn" / "logs"
            ppl_logs = self.fillin_path / "ppl" / "logs"
            
            for log_dir in [dvp_logs, fn_logs, ppl_logs]:
                if log_dir.exists():
                    os.startfile(str(log_dir))
                    break
            else:
                QMessageBox.information(self, "提示", "没有找到子进程日志目录")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开子进程日志: {str(e)}")

    def open_update_templates_folder(self):
        """打开更新模板文件夹"""
        try:
            self.update_templates_path.mkdir(exist_ok=True)
            os.startfile(str(self.update_templates_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def replace_templates(self):
        """替换模板"""
        try:
            from file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            if file_manager.replace_templates():
                QMessageBox.information(self, "成功", "模板替换完成!")
            else:
                QMessageBox.warning(self, "警告", "模板替换失败或没有可替换的文件")
                
        except Exception as e:
            self.logger.error(f"替换模板失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"替换模板失败: {str(e)}")

    def update_time(self):
        """更新状态栏时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def closeEvent(self, event):
        """程序关闭时的处理"""
        # 清空密码
        self.password_edit.clear()
        
        # 关闭工作线程
        if self.apply_worker and self.apply_worker.isRunning():
            self.apply_worker.terminate()
            self.apply_worker.wait()
        
        if self.upload_worker and self.upload_worker.isRunning():
            self.upload_worker.terminate()
            self.upload_worker.wait()
        
        event.accept()


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("车型文件管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 设置应用程序图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.png"))
    
    window = VehicleManagementGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
