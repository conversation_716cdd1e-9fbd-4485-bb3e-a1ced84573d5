#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试模板文件查找和复制功能
"""

import sys
import os
from pathlib import Path
import shutil
import logging

def find_template_file(templates_path, file_mappings, file_type):
    """根据文件类型查找模板文件"""
    if file_type not in file_mappings:
        return None
        
    mapping = file_mappings[file_type]
    pattern = mapping.get("pattern", "")
    extension = mapping.get("extension", "")
    
    print(f"查找文件类型: {file_type}")
    print(f"关键字: {pattern}")
    print(f"扩展名: {extension}")
    
    # 在Templates文件夹中查找包含关键字的文件
    for template_file in templates_path.glob(f"*{extension}"):
        print(f"检查文件: {template_file.name}")
        if pattern in template_file.name:
            print(f"✅ 找到匹配的模板文件: {template_file.name}")
            return template_file
    
    # 如果按关键字没找到，尝试精确匹配原来的文件名
    exact_file = templates_path / mapping["template"]
    if exact_file.exists():
        print(f"✅ 使用精确匹配的文件: {exact_file.name}")
        return exact_file
        
    print(f"❌ 未找到匹配的模板文件")
    return None

def test_template_file_finding():
    """测试模板文件查找功能"""
    print("开始测试模板文件查找功能...")
    
    base_path = Path(__file__).parent
    templates_path = base_path / "Templates"
      # 更新的文件映射，使用关键字而不是完整文件名
    file_mappings = {
        "DVP": {
            "template": "软件设计验证计划.xlsx",  # 原来的映射作为后备
            "pattern": "软件设计验证计划",  # 关键字
            "extension": ".xlsx"
        },
        "PPL": {
            "template": "软件开发匹配测试计划.xlsx", 
            "pattern": "软件开发匹配测试计划",
            "extension": ".xlsx"
        },
        "FN_IMU": {
            "template": "XX项目VSE系统 to 气囊系统接口定义通知单.docx",
            "pattern": "安全气囊节点接口定义通知单",  # 使用实际文件名中的关键字
            "extension": ".docx"
        },
        "FN_VCU": {
            "template": "XX项目VSE系统 to VCU系统接口定义通知单.docx",
            "pattern": "VCU系统接口定义通知单", 
            "extension": ".docx"
        },
        "FN_IPB": {
            "template": "XX项目VSE系统 to IPB系统接口定义通知单.docx",
            "pattern": "IPB系统接口定义通知单",
            "extension": ".docx"
        },
        "FN_域控": {
            "template": "XX项目VSE系统 to 跨域计算平台接口定义通知单.docx",
            "pattern": "跨域计算平台接口定义通知单",
            "extension": ".docx"
        }
    }
    
    print(f"Templates文件夹路径: {templates_path}")
    print(f"Templates文件夹存在: {templates_path.exists()}")
    
    if templates_path.exists():
        print("\nTemplates文件夹中的文件:")
        for file in templates_path.iterdir():
            if file.is_file():
                print(f"  - {file.name}")
    
    print("\n" + "="*50)
    print("测试各种文件类型的查找:")
    
    for file_type in file_mappings.keys():
        print(f"\n--- 测试 {file_type} ---")
        found_file = find_template_file(templates_path, file_mappings, file_type)
        if found_file:
            print(f"成功: {found_file.name}")
        else:
            print("失败: 未找到文件")

if __name__ == "__main__":
    test_template_file_finding()
