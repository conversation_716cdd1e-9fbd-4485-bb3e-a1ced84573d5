"""
第一个页面处理模块
处理文档信息填写、文件上传等功能
"""

import time
import logging
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)

class FirstPageHandler:
    """第一个页面处理器"""
    
    def __init__(self, driver, wait, config):
        self.driver = driver
        self.wait = wait
        self.config = config
        
    def fill_document_info(self, doc_id, source_file, file_type, excel_file=None):
        """填写文档信息（第一个页面）"""
        try:
            logger.info(f"📝 开始填写文档信息: {doc_id}")
            time.sleep(self.config.get('OPERATION_DELAY', 3))
            
            # 1. 填写文档编号
            if not self._fill_document_number(doc_id):
                return False
            
            # 2. 填写项目阶段
            if not self._fill_project_stage(file_type):
                return False
            
            # 3. 填写文档名称
            if not self._fill_document_name(source_file['name']):
                return False
            
            # 4. 填写内容简要
            if not self._fill_content_summary(doc_id, file_type):
                return False
            
            # 5. 上传文件
            if not self._upload_files(source_file, excel_file):
                return False
            
            # 6. 保存并发起评审
            if not self._save_and_start_review():
                return False
            
            logger.info(f"✅ 第一页信息填写完成: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 第一页处理失败: {str(e)}")
            return False
    
    def _fill_document_number(self, doc_id):
        """填写文档编号"""
        try:
            logger.info("📝 填写文档编号...")
            
            # 优化后的文档编号选择器 - 只保留有效的
            doc_number_selectors = [
                "//div[2]/div/div/div/div/input"
            ]
            
            for i, selector in enumerate(doc_number_selectors):
                try:
                    logger.info(f"尝试文档编号选择器 {i+1}/{len(doc_number_selectors)}")
                    doc_number_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    doc_number_field.click()
                    time.sleep(1)
                    doc_number_field.clear()
                    doc_number_field.send_keys(doc_id)
                    logger.info(f"✅ 已输入文档编号: {doc_id}")
                    time.sleep(2)
                    
                    # 尝试点击下拉选项
                    dropdown_selectors = [
                        f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{doc_id}')]",
                        f"//div[contains(@class, 'el-select-dropdown')]//span[contains(text(), '{doc_id}')]",
                        f"//ul//li//span[contains(text(), '{doc_id}')]"
                    ]
                    
                    dropdown_clicked = False
                    for dropdown_selector in dropdown_selectors:
                        try:
                            dropdown_item = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, dropdown_selector)
                            ))
                            dropdown_item.click()
                            logger.info("✅ 成功点击文档编号下拉选项")
                            dropdown_clicked = True
                            break
                        except:
                            continue
                    
                    if not dropdown_clicked:
                        doc_number_field.send_keys(Keys.ENTER)
                        time.sleep(1)
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"文档编号选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文档编号填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写文档编号异常: {str(e)}")
            return False
    
    def _fill_project_stage(self, file_type):
        """填写项目阶段"""
        try:
            logger.info("📝 填写项目阶段...")
            
            stage_selectors = [
                "//input[contains(@id, '1627')]",
                "//label[contains(text(), '阶段')]/following-sibling::div//input",
                "//div[5]/div/div[2]/div/div/div/div/input"
            ]
            
            stage_value = "B版"  # 默认阶段
            
            for i, selector in enumerate(stage_selectors):
                try:
                    stage_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    stage_field.click()
                    time.sleep(1)
                    stage_field.clear()
                    stage_field.send_keys(stage_value)
                    time.sleep(1)
                    
                    # 尝试选择下拉选项
                    try:
                        stage_option = self.wait.until(EC.element_to_be_clickable(
                            (By.XPATH, f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{stage_value}')]")
                        ))
                        stage_option.click()
                        logger.info(f"✅ 已选择项目阶段: {stage_value}")
                    except:
                        stage_field.send_keys(Keys.ENTER)
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"项目阶段选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 项目阶段填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写项目阶段异常: {str(e)}")
            return False
    
    def _fill_document_name(self, filename):
        """填写文档名称"""
        try:
            logger.info("📝 填写文档名称...")
            
            # 从文件名中提取文档名称（去掉扩展名）
            doc_name = Path(filename).stem
            
            name_selectors = [
                "//input[contains(@id, '1628')]",
                "//label[contains(text(), '名称')]/following-sibling::div//input",
                "//div[6]/div/div[2]/div/div/div/input"
            ]
            
            for i, selector in enumerate(name_selectors):
                try:
                    name_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    name_field.click()
                    time.sleep(1)
                    name_field.clear()
                    name_field.send_keys(doc_name)
                    logger.info(f"✅ 已输入文档名称: {doc_name}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"文档名称选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文档名称填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写文档名称异常: {str(e)}")
            return False
    
    def _fill_content_summary(self, doc_id, file_type):
        """填写内容简要"""
        try:
            logger.info("📝 填写内容简要...")
            
            # 生成内容简要
            content_summary = f"{doc_id} - {file_type}类型文档"
            
            summary_selectors = [
                "//textarea[contains(@id, '1629')]",
                "//label[contains(text(), '简要')]/following-sibling::div//textarea",
                "//div[7]/div/div[2]/div/div/div/textarea"
            ]
            
            for i, selector in enumerate(summary_selectors):
                try:
                    summary_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    summary_field.click()
                    time.sleep(1)
                    summary_field.clear()
                    summary_field.send_keys(content_summary)
                    logger.info(f"✅ 已输入内容简要: {content_summary}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"内容简要选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 内容简要填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写内容简要异常: {str(e)}")
            return False
    
    def _upload_files(self, source_file, excel_file=None):
        """上传文件"""
        try:
            logger.info("📁 开始上传文件...")
            
            files_to_upload = [source_file]
            if excel_file:
                files_to_upload.append(excel_file)
            
            upload_selectors = [
                "//input[@type='file']",
                "//div[contains(@class, 'el-upload')]//input[@type='file']",
                "//input[contains(@accept, 'application')]"
            ]
            
            for i, selector in enumerate(upload_selectors):
                try:
                    upload_input = self.driver.find_element(By.XPATH, selector)
                    
                    # 上传所有文件
                    file_paths = []
                    for file_info in files_to_upload:
                        file_paths.append(str(file_info['path']))
                    
                    # 一次性上传多个文件
                    upload_input.send_keys('\n'.join(file_paths))
                    
                    logger.info(f"✅ 已上传 {len(file_paths)} 个文件")
                    time.sleep(3)  # 等待文件上传完成
                    return True
                    
                except Exception as e:
                    logger.warning(f"上传选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文件上传失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 上传文件异常: {str(e)}")
            return False
    
    def _save_and_start_review(self):
        """保存并发起评审"""
        try:
            logger.info("💾 保存并发起评审...")
            
            # 尝试点击保存并发起评审按钮
            save_selectors = [
                "//button[contains(text(), '保存并发起评审')]",
                "//span[contains(text(), '保存并发起评审')]/parent::button",
                "//div[contains(@class, 'el-button--primary') and contains(text(), '保存')]"
            ]
            
            for i, selector in enumerate(save_selectors):
                try:
                    save_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    save_btn.click()
                    logger.info("✅ 已点击保存并发起评审按钮")
                    time.sleep(3)  # 等待页面跳转
                    return True
                    
                except Exception as e:
                    logger.warning(f"保存按钮选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 保存并发起评审失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 保存并发起评审异常: {str(e)}")
            return False
