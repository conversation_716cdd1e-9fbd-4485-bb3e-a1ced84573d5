# 车型文件管理系统 - 完整功能说明

## 项目概述

本系统是一个基于PyQt5的现代化GUI程序，专门用于车型文件管理，包括模板文件复制、申请编号、文件内容填写、上传审批等完整流程。系统采用模块化设计，支持多车型管理、批量文件操作、自动化Selenium流程等功能。

---

## 目录结构

```
车型文件管理系统/
├── main_gui_final.py          # 主程序GUI界面
├── requirements.txt           # Python依赖包列表
├── README.md                 # 项目说明文档
│
├── Core/                     # 核心功能模块
│   ├── file_manager.py       # 文件管理核心模块
│   ├── selenium_automation.py # 自动化操作模块
│   └── fillin_filecode.py    # 文件内容填写模块
│
├── Scripts/                  # 脚本文件
│   ├── install.bat          # 安装依赖脚本
│   ├── run_gui.bat          # 启动GUI脚本
│   └── launcher.py          # 程序启动器
│
├── Config/                   # 配置文件
│   ├── 申请编号.json         # 申请编号自动化配置
│   ├── 登录.json            # 登录配置
│   ├── 上传审批.json         # 上传审批配置
│   ├── 审批人员类别及填名字地方.json # 审批人员配置
│   └── 提取编号.json         # 编号提取配置
│
├── Templates/                # 模板文件夹
│   ├── Fill_Template_Data.xlsx # Excel配置模板
│   ├── 数据管理员名单.png     # 管理员名单图片
│   └── XX项目VSE*.xlsx/docx  # 各类文档模板
│
├── Vehicles/                 # 车型数据文件夹
│   └── [车型代号]/
│       ├── information/      # 车型配置信息
│       └── *.xlsx/docx      # 车型相关文件
│
├── Final_Approval_Documents/ # 审批文档
├── Apply/                   # 申请相关
├── Upload/                  # 上传相关
├── Update_Templates/        # 模板更新
├── Fillin/                  # 填写功能相关
├── logs/                    # 日志文件
│
├── Docs/                    # 文档和说明
│   ├── 项目总结.md
│   ├── 界面改进总结.md
│   ├── 问题修正总结.md
│   ├── 模板文件修正总结.md
│   ├── 申请编号问题修正总结.md
│   ├── File_Status表结构修正总结.md
│   └── project_structure_original.md
│
└── Temp/                    # 临时和测试文件
    ├── test_*.py           # 各种测试脚本
    ├── main_gui*.py        # 旧版本GUI
    ├── file_manager_*.py   # 旧版本文件管理器
    └── 其他临时文件
```

---

## 核心功能模块

### 1. 主程序界面 (main_gui_final.py)

**功能描述**: 基于PyQt5的现代化图形用户界面

#### 主要界面组件:
- **标题栏**: 显示系统名称和快捷操作按钮
- **车型设置区**: 车型选择、车型信息配置
- **快捷按钮区**: 
  - 审批文件夹按钮
  - 车型文件夹按钮  
  - 模板文件夹按钮
  - 日志文件夹按钮
- **选项卡界面**:
  - 文件操作选项卡
  - 上传审批选项卡
  - 日志监控选项卡

#### 登录设置区:
- 用户名输入框（优化长度）
- 密码输入框（优化长度）
- 数据管理员名单按钮（点击打开图片）

#### 文件操作功能:
- 操作类型选择（申请编号、填写内容）
- 文件类型选择（DVP、PPL、各种接口文件）
- 批量操作支持
- 模式选择（静默模式、测试模式）

#### 状态监控:
- 实时日志显示
- 进度条显示
- 操作状态反馈

### 2. 文件管理模块 (Core/file_manager.py)

**功能描述**: 核心文件管理功能，包括模板复制、车型配置、状态管理

#### 主要功能:

##### 2.1 车型文件夹管理
- `setup_vehicle_folder(vehicle_code)`: 创建车型文件夹结构
- `update_vehicle_code_in_excel(excel_file, vehicle_code)`: 更新Excel中的车型代号

##### 2.2 智能模板文件查找
- `find_template_file(file_type)`: 根据关键字智能匹配模板文件
- 支持模糊匹配，避免文件名细微差异导致的问题
- 自动回退到精确匹配

##### 2.3 模板文件复制和处理
- `copy_template_files(vehicle_code, selected_files)`: 批量复制模板文件
- 自动将文件名中的"XX"替换为车型代号
- 支持Excel和Word文件内容替换
- `replace_vehicle_code_in_file(file_path, vehicle_code)`: 文件内容替换

##### 2.4 File_Status表管理
- `update_file_status(vehicle_code, new_files)`: 更新文件状态表
- `get_files_for_apply_id(vehicle_code)`: 获取需要申请编号的文件
- `update_file_with_id(vehicle_code, file_name, file_id)`: 更新文件编号
- `update_fill_status(vehicle_code, file_name, status)`: 更新填写状态

File_Status表结构:
- `file_name`: 带车型代号的完整文件名
- `code`: 文件编号
- `numbered_file`: 编号-完整文件名格式
- `is_fillin`: 填写状态(Y/N)
- `is_upload`: 上传状态(Y/N)

##### 2.5 审批人员管理
- `get_reviewer_info(vehicle_code)`: 获取审批人员信息
- 支持数据管理员、科长、相关方等角色

##### 2.6 上传文件管理
- `get_upload_files(vehicle_code)`: 获取需要上传的文件列表

#### 支持的文件类型:
- **DVP**: 软件设计验证计划
- **PPL**: 软件开发匹配测试计划
- **接口文件**: VCU、IPB、ESP+BWA、EPS、EPSA、EPB、DiSus系列、安全气囊、跨域计算平台等

### 3. 自动化操作模块 (Core/selenium_automation.py)

**功能描述**: 基于Selenium的网页自动化操作

#### 主要功能:

##### 3.1 申请编号自动化 (ApplyIDAutomator)
- 自动登录系统
- 批量申请文件编号
- 支持多种文件类型
- 错误处理和重试机制

##### 3.2 上传审批自动化 (UploadApprovalAutomator)  
- 自动上传文件
- 添加审批人员
- 提交审批流程
- 状态跟踪

#### 配置支持:
- 静默模式运行
- 测试模式调试
- 自定义浏览器设置

### 4. 文件内容填写模块 (Core/fillin_filecode.py)

**功能描述**: 自动填写文件内容

#### 主要功能:
- Excel文件内容自动填写
- Word文档内容处理
- 模板变量替换
- 批量文件处理

---

## 主要工作流程

### 1. 车型初始化流程
1. 选择或输入车型代号
2. 点击"设置车型"按钮
3. 系统自动创建车型文件夹结构
4. 复制并配置Excel模板文件
5. 更新车型代号信息

### 2. 模板文件复制流程  
1. 选择要复制的文件类型
2. 系统智能查找匹配的模板文件
3. 复制文件到车型文件夹
4. 自动替换文件名中的"XX"为车型代号
5. 更新File_Status表
6. 替换文件内容中的车型信息

### 3. 申请编号流程
1. 输入登录用户名和密码
2. 选择"申请文件编号"选项
3. 系统获取需要申请编号的文件列表
4. 自动登录申请系统
5. 批量申请文件编号
6. 更新File_Status表中的编号信息

### 4. 文件内容填写流程
1. 选择"填写文件内容"选项
2. 系统识别需要填写的文件
3. 自动填写文件内容
4. 更新填写状态

### 5. 上传审批流程
1. 切换到"上传审批"选项卡
2. 选择要上传的文件
3. 添加审批人员信息
4. 自动上传文件并提交审批
5. 更新上传状态

---

## 关键特性

### 1. 智能化特性
- **智能文件匹配**: 基于关键字的模糊匹配，容错性强
- **自动化流程**: 全流程自动化操作，减少人工干预
- **批量处理**: 支持多文件、多车型批量操作

### 2. 用户体验优化
- **现代化界面**: 基于PyQt5的美观界面
- **实时反馈**: 进度条、日志显示、状态提示
- **快捷操作**: 一键打开文件夹、快速车型切换

### 3. 数据管理
- **结构化存储**: Excel表格管理文件状态
- **状态跟踪**: 完整的文件生命周期管理
- **配置灵活**: JSON配置文件，易于维护

### 4. 错误处理
- **完善的日志系统**: 详细的操作日志记录
- **异常捕获**: 友好的错误提示和处理
- **数据备份**: 自动备份重要配置

### 5. 扩展性
- **模块化设计**: 核心功能独立，易于扩展
- **配置驱动**: 通过配置文件支持新的文件类型
- **接口标准**: 清晰的模块接口，便于二次开发

---

## 配置文件说明

### 1. 申请编号配置 (Config/申请编号.json)
- 网页元素定位配置
- 申请流程步骤配置
- 重试和超时设置

### 2. 登录配置 (Config/登录.json)
- 登录页面元素配置
- 用户认证流程
- 会话管理设置

### 3. 上传审批配置 (Config/上传审批.json)
- 文件上传界面配置
- 审批人员添加流程
- 提交确认设置

### 4. 审批人员配置 (Config/审批人员类别及填名字地方.json)
- 审批人员角色定义
- 人员信息字段配置
- 通知设置

---

## 使用说明

### 1. 系统启动
- 双击 `Scripts/run_gui.bat` 启动系统
- 或运行 `python main_gui_final.py`

### 2. 环境安装
- 运行 `Scripts/install.bat` 安装依赖
- 或手动执行 `pip install -r requirements.txt`

### 3. 日常操作
1. 启动程序后选择或输入车型代号
2. 根据需要执行相应操作（复制模板、申请编号等）
3. 查看日志监控选项卡了解操作进度
4. 使用快捷按钮快速访问相关文件夹

---

## 技术栈

- **界面框架**: PyQt5
- **网页自动化**: Selenium WebDriver
- **数据处理**: Pandas
- **文件操作**: Python标准库 + openpyxl
- **配置管理**: JSON
- **日志系统**: Python logging

---

## 维护说明

### 1. 添加新文件类型
1. 在模板文件夹添加新的模板文件
2. 更新 `Core/file_manager.py` 中的 `file_mappings` 配置
3. 在GUI中添加对应的选择选项

### 2. 修改自动化流程
1. 更新相应的JSON配置文件
2. 如需修改复杂逻辑，编辑 `Core/selenium_automation.py`

### 3. 界面调整
1. 编辑 `main_gui_final.py` 中的界面布局代码
2. 注意保持响应式设计和用户体验

### 4. 日志和调试
- 查看 `logs/` 文件夹中的日志文件
- 使用测试模式进行调试
- 临时文件和测试脚本在 `Temp/` 文件夹中

---

## 注意事项

1. **权限要求**: 程序需要文件读写权限和网络访问权限
2. **浏览器依赖**: 自动化功能需要Chrome浏览器和ChromeDriver
3. **网络环境**: 申请编号和上传功能需要访问相应的内部系统
4. **数据备份**: 建议定期备份Vehicles文件夹和配置文件
5. **版本兼容**: 注意Python版本和依赖包的兼容性

---

## 联系支持

如有问题或需要功能扩展，请参考：
- `Docs/` 文件夹中的详细文档
- 程序日志文件进行问题诊断
- 测试文件夹中的示例和测试用例
