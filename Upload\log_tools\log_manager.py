"""
日志文件管理工具
用于自动清理和轮转upload_approval.log文件
"""

import os
import shutil
from datetime import datetime, timedelta
import logging


class LogManager:
    def __init__(self, log_file="upload_approval.log", max_size_mb=5, max_backups=3, max_days=7):
        """
        初始化日志管理器
        
        Args:
            log_file: 日志文件路径
            max_size_mb: 最大文件大小(MB)
            max_backups: 保留的备份文件数量
            max_days: 日志保留天数
        """
        self.log_file = log_file
        self.max_size = max_size_mb * 1024 * 1024  # 转换为字节
        self.max_backups = max_backups
        self.max_days = max_days
        
    def get_file_size(self):
        """获取日志文件大小"""
        try:
            return os.path.getsize(self.log_file)
        except FileNotFoundError:
            return 0
    
    def rotate_log(self):
        """轮转日志文件"""
        if not os.path.exists(self.log_file):
            return
            
        # 创建备份文件名（带时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{self.log_file}.{timestamp}"
        
        try:
            # 移动当前日志到备份
            shutil.move(self.log_file, backup_file)
            print(f"日志文件已轮转: {self.log_file} -> {backup_file}")
            
            # 创建新的空日志文件
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 日志轮转开始 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                
        except Exception as e:
            print(f"日志轮转失败: {e}")
    
    def clean_old_backups(self):
        """清理旧的备份文件"""
        if not os.path.exists(os.path.dirname(self.log_file) or "."):
            return
            
        # 获取所有备份文件
        dir_path = os.path.dirname(self.log_file) or "."
        backup_files = []
        
        for file in os.listdir(dir_path):
            if file.startswith(os.path.basename(self.log_file) + "."):
                file_path = os.path.join(dir_path, file)
                if os.path.isfile(file_path):
                    mtime = os.path.getmtime(file_path)
                    backup_files.append((file_path, mtime))
        
        # 按修改时间排序（最新的在前）
        backup_files.sort(key=lambda x: x[1], reverse=True)
        
        # 删除超过数量限制的备份
        if len(backup_files) > self.max_backups:
            for file_path, _ in backup_files[self.max_backups:]:
                try:
                    os.remove(file_path)
                    print(f"删除旧备份: {file_path}")
                except Exception as e:
                    print(f"删除备份失败 {file_path}: {e}")
        
        # 删除超过时间限制的备份
        cutoff_time = datetime.now() - timedelta(days=self.max_days)
        cutoff_timestamp = cutoff_time.timestamp()
        
        for file_path, mtime in backup_files:
            if mtime < cutoff_timestamp:
                try:
                    os.remove(file_path)
                    print(f"删除过期备份: {file_path}")
                except Exception as e:
                    print(f"删除过期备份失败 {file_path}: {e}")
    
    def truncate_log(self, keep_lines=1000):
        """截断日志文件，只保留最后N行"""
        if not os.path.exists(self.log_file):
            return
            
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) > keep_lines:
                # 保留最后的指定行数
                kept_lines = lines[-keep_lines:]
                
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    f.write(f"=== 日志截断 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (保留最后{keep_lines}行) ===\n")
                    f.writelines(kept_lines)
                
                print(f"日志文件已截断，保留最后 {keep_lines} 行")
                
        except Exception as e:
            print(f"日志截断失败: {e}")
    
    def check_and_clean(self):
        """检查并清理日志"""
        file_size = self.get_file_size()
        print(f"当前日志文件大小: {file_size / 1024 / 1024:.2f} MB")
        
        if file_size > self.max_size:
            print(f"日志文件超过限制 ({self.max_size / 1024 / 1024:.2f} MB)，开始清理...")
            self.rotate_log()
        
        # 清理旧备份
        self.clean_old_backups()
    
    def manual_clean(self, method="rotate"):
        """手动清理日志"""
        if method == "rotate":
            self.rotate_log()
        elif method == "truncate":
            self.truncate_log()
        elif method == "clear":
            try:
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    f.write(f"=== 日志清空 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                print("日志文件已清空")
            except Exception as e:
                print(f"清空日志失败: {e}")
        
        self.clean_old_backups()


def main():
    """主函数 - 命令行工具"""
    import sys
    
    log_manager = LogManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            log_manager.check_and_clean()
        elif command == "rotate":
            log_manager.manual_clean("rotate")
        elif command == "truncate":
            log_manager.manual_clean("truncate")
        elif command == "clear":
            log_manager.manual_clean("clear")
        elif command == "status":
            size = log_manager.get_file_size()
            print(f"日志文件: {log_manager.log_file}")
            print(f"大小: {size / 1024 / 1024:.2f} MB")
            print(f"最大限制: {log_manager.max_size / 1024 / 1024:.2f} MB")
        else:
            print("用法: python log_manager.py [check|rotate|truncate|clear|status]")
    else:
        # 默认执行检查和清理
        log_manager.check_and_clean()


if __name__ == "__main__":
    main()
