# 申请编号自动化配置文件 - 示例
# 请复制此文件为config.py并填写实际信息

# 登录信息 - 请填写您的实际信息
USERNAME = "您的用户名"  # 例如: "6604331"
PASSWORD = "您的密码"    # 例如: "your_password"

# 项目信息 - 请填写实际的车型代号
PROJECT_CODE = "车型代号"  # 例如: "HYHB"

# 系统配置 - 一般不需要修改
DMS_URL = "https://gcy.byd.com/dms/#/home"

# 运行模式设置
HEADLESS_MODE = False  # 静默模式：True=后台运行不显示浏览器窗口，False=显示浏览器窗口

# 等待时间设置（秒） - 可根据网络状况调整
WAIT_TIMEOUT = 30      # 元素等待超时时间
OPERATION_DELAY = 2    # 操作间隔时间
