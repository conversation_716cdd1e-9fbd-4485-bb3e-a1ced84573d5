{"Name": "上传审批-DVP", "CreationDate": "2025-6-24", "Commands": [{"Command": "open", "Target": "https://gcy.byd.com/dms/#/dashboard", "Value": "", "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"main\"]/div/div/div/div/div/div/span", "Value": "", "Targets": ["xpath=//*[@id=\"main\"]/div/div/div/div/div/div/span", "xpath=//div/div/div/div/span", "css=#main > div > div > div:nth-child(1) > div:nth-child(1) > div > div.skip.DocCreate > span"], "Description": "点击文档创建，出现了新的页面填要上传审批的文件的相关信息"}, {"Command": "click", "Target": "id=el-id-7049-1462", "Value": "", "Targets": ["id=el-id-7049-1462", "xpath=//*[@id=\"el-id-7049-1462\"]", "xpath=//input[@id='el-id-7049-1462']", "xpath=//div[2]/div/div/div/div/input", "css=#el-id-7049-1462"], "Description": "点击文档编号输入框，填入要上传审批的文档的编号"}, {"Command": "type", "Target": "id=el-id-7049-1462", "Value": "HYHB_DVP_A19-100002", "Targets": ["id=el-id-7049-1462", "xpath=//*[@id=\"el-id-7049-1462\"]", "xpath=//input[@id='el-id-7049-1462']", "xpath=//div[2]/div/div/div/div/input", "css=#el-id-7049-1462"], "Description": ""}, {"Command": "type", "Target": "id=el-id-7049-1462", "Value": "HYHB_DVP_A19-100002", "Targets": ["id=el-id-7049-1462", "xpath=//*[@id=\"el-id-7049-1462\"]", "xpath=//input[@id='el-id-7049-1462']", "xpath=//div[2]/div/div/div/div/input", "css=#el-id-7049-1462"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-id-7049-1533\"]/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-id-7049-1533\"]/span", "xpath=//div[8]/div/div/div/ul/li[10]/span", "css=#el-id-7049-1533 > span"], "Description": "填入后还要点击一下浮动出现的填的编号"}, {"Command": "click", "Target": "id=el-id-7049-1627", "Value": "", "Targets": ["id=el-id-7049-1627", "xpath=//*[@id=\"el-id-7049-1627\"]", "xpath=//input[@id='el-id-7049-1627']", "xpath=//div[5]/div/div[2]/div/div/div/div/input", "css=#el-id-7049-1627"], "Description": "点击填写所属项目阶段的框，一律都填入B版"}, {"Command": "type", "Target": "id=el-id-7049-1627", "Value": "B版", "Targets": ["id=el-id-7049-1627", "xpath=//*[@id=\"el-id-7049-1627\"]", "xpath=//input[@id='el-id-7049-1627']", "xpath=//div[5]/div/div[2]/div/div/div/div/input", "css=#el-id-7049-1627"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-id-7049-1586\"]/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-id-7049-1586\"]/span", "xpath=//ul[5]/li[2]/ul/li[4]/span", "css=#el-id-7049-1586 > span"], "Description": "输入B版后，点击浮动出现的整车项目阶段-产品详细设计阶段-B版"}, {"Command": "click", "Target": "css=#el-collapse-content-1438 > div > div:nth-child(2) > div > div:nth-child(5) > div:nth-child(2) > div.el-form-item__content > div > div > div.el-select__suffix > i > svg", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-1438\"]/div/div[2]/div/div[5]/div[2]/div[2]/div/div/div[2]/i/svg", "css=#el-collapse-content-1438 > div > div:nth-child(2) > div > div:nth-child(5) > div:nth-child(2) > div.el-form-item__content > div > div > div.el-select__suffix > i > svg"], "Description": "点击交付物级别的下拉按钮"}, {"Command": "click", "Target": "id=el-id-7049-1600", "Value": "", "Targets": ["id=el-id-7049-1600", "xpath=//*[@id=\"el-id-7049-1600\"]", "xpath=//li[@id='el-id-7049-1600']", "xpath=//div[13]/div/div/div/ul/li", "css=#el-id-7049-1600"], "Description": "选择浮动出现的项目级"}, {"Command": "click", "Target": "id=el-id-7049-1468", "Value": "", "Targets": ["id=el-id-7049-1468", "xpath=//*[@id=\"el-id-7049-1468\"]", "xpath=//textarea[@id='el-id-7049-1468']", "xpath=//textarea", "css=#el-id-7049-1468"], "Description": "点击文档内容简要的输入框，输入文件名即可"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div/div[2]/div[2]/div/div/div/div/div/img", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div/div[2]/div[2]/div/div/div/div/div/img", "xpath=//div/div/div/img", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(1) > div.el-form-item__content > div:nth-child(2) > div > div > div > div > div.el-upload__text.top-item > img"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div/div[2]/div[2]/div/div/input", "Value": "", "Targets": ["name=file", "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div/div[2]/div[2]/div/div/input", "xpath=//div[3]/div[2]/div/div/div/div/div[2]/div[2]/div/div/input", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(1) > div.el-form-item__content > div:nth-child(2) > div > div > input"], "Description": "我只点击了一下上传源文件的按钮，但这里记录了两次click，"}, {"Command": "type", "Target": "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div/div[2]/div[2]/div/div/input", "Value": "C:\\fakepath\\QXHB-SFT-CHC-10005-QX项目VSE系统功能测试用例.xlsx", "Targets": ["name=file", "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div/div[2]/div[2]/div/div/input", "xpath=//div[3]/div[2]/div/div/div/div/div[2]/div[2]/div/div/input", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(1) > div.el-form-item__content > div:nth-child(2) > div > div > input"], "Description": "选择了文件并上传"}, {"Command": "click", "Target": "css=#el-collapse-content-1452 > div > div > div > div:nth-child(2) > div.el-form-item__content > div > div > div > div > div > div > div > div.file-item > div:nth-child(2) > button > span > i > svg > path", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/button/span/i/svg/path", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(2) > div.el-form-item__content > div > div > div > div > div > div > div > div.file-item > div:nth-child(2) > button > span > i > svg > path"], "Description": "删掉自动转换的pdf，因为自动转换的pdf一般不可用"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/img", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/img", "xpath=//div[2]/div/div/div/div/div/div/img", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(2) > div.el-form-item__content > div > div > div > div > div > div.el-upload__text.top-item > img"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/input", "Value": "", "Targets": ["name=file", "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/input", "xpath=//div[2]/div[2]/div/div/div/input", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(2) > div.el-form-item__content > div > div > div > input"], "Description": "我只点击了一下上传pdf格式文件的按钮，但这里记录了两次click"}, {"Command": "type", "Target": "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/input", "Value": "C:\\fakepath\\HYHB_PPL_A19-000010-HY项目VSE软件开发匹配计划.pdf", "Targets": ["name=file", "xpath=//*[@id=\"el-collapse-content-1452\"]/div/div/div/div[2]/div[2]/div/div/div/input", "xpath=//div[2]/div[2]/div/div/div/input", "css=#el-collapse-content-1452 > div > div > div > div:nth-child(2) > div.el-form-item__content > div > div > div > input"], "Description": "选择了文件的对应的pdf版本并上传"}, {"Command": "click", "Target": "xpath=//*[@id=\"pane-docInfo\"]/div[2]/div/button[2]/span/div/span", "Value": "", "Targets": ["xpath=//*[@id=\"pane-docInfo\"]/div[2]/div/button[2]/span/div/span", "xpath=//button[2]/span/div/span", "css=#pane-docInfo > div.cus-footer > div > button.el-button.is-plain > span > div > span"], "Description": "点击保存并发起评审"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr/td[2]/div/span/span/span", "xpath=//td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(1) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "点击了DVP的评审角色部门相关方，让你看一下，因为后面我要点击输入邮箱的地方，应该与这个角色对应上"}, {"Command": "click", "Target": "id=el-id-5391-82", "Value": "", "Targets": ["id=el-id-5391-82", "xpath=//*[@id=\"el-id-5391-82\"]", "xpath=//input[@id='el-id-5391-82']", "xpath=//td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-82"], "Description": "点击了部门相关方的框，填的内容应该是DVP_Signatory里填的邮箱"}, {"Command": "type", "Target": "id=el-id-5391-82", "Value": "yin.shaod", "Targets": ["id=el-id-5391-82", "xpath=//*[@id=\"el-id-5391-82\"]", "xpath=//input[@id='el-id-5391-82']", "xpath=//td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-82"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-id-5391-180\"]/div/div/div[2]", "Value": "", "Targets": ["xpath=//*[@id=\"el-id-5391-180\"]/div/div/div[2]", "xpath=//li/div/div/div[2]", "css=#el-id-5391-180 > div > div > div.text-[12px]"], "Description": "点了一下输入邮箱后浮动出来的人，点击后就选好了这个人"}, {"Command": "click", "Target": "id=el-id-5391-82", "Value": "", "Targets": ["id=el-id-5391-82", "xpath=//*[@id=\"el-id-5391-82\"]", "xpath=//input[@id='el-id-5391-82']", "xpath=//div[2]/input", "css=#el-id-5391-82"], "Description": "如果要填多个人，在上一个人选好后，应该再点击一下这个框的空白处，然后再填下一个人的邮箱"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[2]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[2]/td[2]/div/span/span/span", "xpath=//tr[2]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(2) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "点击系统专家角色"}, {"Command": "click", "Target": "id=el-id-5391-83", "Value": "", "Targets": ["id=el-id-5391-83", "xpath=//*[@id=\"el-id-5391-83\"]", "xpath=//input[@id='el-id-5391-83']", "xpath=//tr[2]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-83"], "Description": "系统专家输入框，Experts的邮箱"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[3]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[3]/td[2]/div/span/span/span", "xpath=//tr[3]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(3) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "点击项目主管角色，实际用时不用点也行，主要是让你能否获得项目主管这几个字，然后进一步确认后面的输入框填项目主管"}, {"Command": "click", "Target": "id=el-id-5391-84", "Value": "", "Targets": ["id=el-id-5391-84", "xpath=//*[@id=\"el-id-5391-84\"]", "xpath=//input[@id='el-id-5391-84']", "xpath=//tr[3]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-84"], "Description": "项目主管输入框，填底盘项目主管的邮箱"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[4]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[4]/td[2]/div/span/span/span", "xpath=//tr[4]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(4) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "车型品质主管角色"}, {"Command": "click", "Target": "id=el-id-5391-85", "Value": "", "Targets": ["id=el-id-5391-85", "xpath=//*[@id=\"el-id-5391-85\"]", "xpath=//input[@id='el-id-5391-85']", "xpath=//tr[4]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-85"], "Description": "输入车型品质主管的邮箱找到人"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[5]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[5]/td[2]/div/span/span/span", "xpath=//tr[5]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(5) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "车型研发品质经理角色"}, {"Command": "click", "Target": "id=el-id-5391-86", "Value": "", "Targets": ["id=el-id-5391-86", "xpath=//*[@id=\"el-id-5391-86\"]", "xpath=//input[@id='el-id-5391-86']", "xpath=//tr[5]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-86"], "Description": "研发品质经理的邮箱找人"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[6]/td[2]/div/span/span/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-25\"]/div/form/div[2]/div/div/div[2]/form/div/div/div[3]/div/div/div/table/tbody/tr[6]/td[2]/div/span/span/span", "xpath=//tr[6]/td[2]/div/span/span/span", "css=#el-collapse-content-25 > div > form > div:nth-child(2) > div > div > div.el-form-item__content > form > div.el-table--fit.el-table--enable-row-hover.el-table--enable-row-transition.el-table.el-table--layout-fixed.is-scrolling-none > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr:nth-child(6) > td.el-table_1_column_2.el-table__cell > div > span > span > span"], "Description": "数据管理员角色标签"}, {"Command": "click", "Target": "id=el-id-5391-87", "Value": "", "Targets": ["id=el-id-5391-87", "xpath=//*[@id=\"el-id-5391-87\"]", "xpath=//input[@id='el-id-5391-87']", "xpath=//tr[6]/td[3]/div/div/div/div/div/div/div/input", "css=#el-id-5391-87"], "Description": "数据管理员工号找到人"}, {"Command": "click", "Target": "id=el-id-5391-52", "Value": "", "Targets": ["id=el-id-5391-52", "xpath=//*[@id=\"el-id-5391-52\"]", "xpath=//input[@id='el-id-5391-52']", "xpath=//div[4]/div/div[2]/div/form/div[3]/div/div/div/div/div/input", "css=#el-id-5391-52"], "Description": "点击后可以选择截止日期"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-id-5391-29\"]/div/div/div/div[2]/table/tbody/tr[7]/td[2]/div/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-id-5391-29\"]/div/div/div/div[2]/table/tbody/tr[7]/td[2]/div/span", "xpath=//div[2]/table/tbody/tr[7]/td[2]/div/span", "css=#el-id-5391-29 > div > div.el-picker-panel__body-wrapper > div > div.el-picker-panel__content > table > tbody > tr:nth-child(7) > td:nth-child(2) > div > span"], "Description": "截止日期选择一周后的时间"}]}