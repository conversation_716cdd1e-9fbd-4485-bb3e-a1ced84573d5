"""
文件操作模块
包含文件处理、复制、分发等相关功能
"""

import shutil
import subprocess
import sys
import logging
from pathlib import Path


class FileOperations:
    """文件操作管理类"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
    
    def clear_directory(self, directory_path):
        """清空指定目录"""
        try:
            directory_path.mkdir(parents=True, exist_ok=True)
            for item in directory_path.iterdir():
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)
        except Exception as e:
            self.logger.error(f"清空目录失败 {directory_path}: {str(e)}")

    def copy_templates_to_apply_input(self, selected_files):
        """复制模板文件到Apply输入文件夹并重命名"""
        try:
            templates_path = self.main_window.base_path / "Templates"
            apply_input_path = self.main_window.base_path / "Apply" / "input_files"
            apply_input_path.mkdir(parents=True, exist_ok=True)
            
            # 定义文件类型与模板匹配规则的映射
            template_matching_rules = {
                "DVP": {
                    "keywords": ["设计验证计划"],
                    "extension": ".xlsx"
                },
                "PPL": {
                    "keywords": ["开发匹配测试计划"],
                    "extension": ".xlsx"
                },
                "FN_IMU": {
                    "keywords": ["气囊", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_VCU": {
                    "keywords": ["VCU", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_IPB": {
                    "keywords": ["IPB", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_ESP_BWA": {
                    "keywords": ["ESP", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_EPS": {
                    "keywords": ["EPS", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_EPSA": {
                    "keywords": ["EPSA", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_EPB": {
                    "keywords": ["EPB", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_A": {
                    "keywords": ["DiSus-A", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_C": {
                    "keywords": ["DiSus-C", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_P": {
                    "keywords": ["DiSus-P", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_X": {
                    "keywords": ["DiSus-X", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_M": {
                    "keywords": ["DiSus-M", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_域控": {
                    "keywords": ["域", "接口定义通知单"],
                    "extension": ".docx"
                }
            }
            
            for file_type in selected_files:
                source_file = None
                
                if file_type in template_matching_rules:
                    # 使用新的匹配规则
                    rule = template_matching_rules[file_type]
                    keywords = rule["keywords"]
                    extension = rule["extension"]
                    
                    # 查找符合所有关键词的模板文件
                    all_files = list(templates_path.glob(f"*{extension}"))
                    for template_file in all_files:
                        filename = template_file.name
                        # 检查文件名是否包含所有必需的关键词
                        if all(keyword in filename for keyword in keywords):
                            source_file = template_file
                            break
                else:
                    # 备用匹配方式：使用原有的关键字匹配
                    template_files = list(templates_path.glob(f"*{file_type}*.xlsx"))
                    if not template_files:
                        template_files = list(templates_path.glob(f"*{file_type}*.docx"))
                    if template_files:
                        source_file = template_files[0]
                
                if source_file:
                    # 智能重命名：将模板文件名中的XX替换为车型代号
                    original_filename = source_file.name
                    if "XX" in original_filename:
                        # 替换XX为车型代号
                        target_filename = original_filename.replace("XX", self.main_window.current_vehicle_code)
                    else:
                        # 如果没有XX，使用映射方式生成文件名
                        file_mapping = {
                            "DVP": f"{self.main_window.current_vehicle_code}_DVP_系统设计验证计划.xlsx",
                            "PPL": f"{self.main_window.current_vehicle_code}_PPL_开发匹配测试计划.xlsx",
                            "FN_IMU": f"{self.main_window.current_vehicle_code}_FN_IMU_接口定义通知单.docx",
                            "FN_VCU": f"{self.main_window.current_vehicle_code}_FN_VCU_接口定义通知单.docx",
                            "FN_IPB": f"{self.main_window.current_vehicle_code}_FN_IPB_接口定义通知单.docx",
                            "FN_ESP_BWA": f"{self.main_window.current_vehicle_code}_FN_ESP_BWA_接口定义通知单.docx",
                            "FN_EPS": f"{self.main_window.current_vehicle_code}_FN_EPS_接口定义通知单.docx",
                            "FN_EPSA": f"{self.main_window.current_vehicle_code}_FN_EPSA_接口定义通知单.docx",
                            "FN_EPB": f"{self.main_window.current_vehicle_code}_FN_EPB_接口定义通知单.docx",
                            "FN_DISUS_A": f"{self.main_window.current_vehicle_code}_FN_DiSus-A_接口定义通知单.docx",
                            "FN_DISUS_C": f"{self.main_window.current_vehicle_code}_FN_DiSus-C_接口定义通知单.docx",
                            "FN_DISUS_P": f"{self.main_window.current_vehicle_code}_FN_DiSus-P_接口定义通知单.docx",
                            "FN_DISUS_X": f"{self.main_window.current_vehicle_code}_FN_DiSus-X_接口定义通知单.docx",
                            "FN_DISUS_M": f"{self.main_window.current_vehicle_code}_FN_DiSus-M_接口定义通知单.docx",
                            "FN_域控": f"{self.main_window.current_vehicle_code}_FN_域控_接口定义通知单.docx"
                        }
                        target_filename = file_mapping.get(file_type, f"{self.main_window.current_vehicle_code}_{file_type}.docx")
                    
                    target_file = apply_input_path / target_filename
                    
                    shutil.copy2(source_file, target_file)
                    self.logger.info(f"已复制模板文件: {source_file} -> {target_file}")
                else:
                    self.logger.warning(f"未找到 {file_type} 的模板文件")
                    
        except Exception as e:
            self.logger.error(f"复制模板文件失败: {str(e)}")
            raise

    def distribute_numbered_files(self):
        """将已编号的文件分发到Fillin相应的输入文件夹"""
        try:
            apply_output_path = self.main_window.base_path / "Apply" / "output_files"
            
            # 文件类型到Fillin文件夹的映射
            fillin_mapping = {
                "DVP": self.main_window.base_path / "Fillin" / "dvp" / "input",
                "PPL": self.main_window.base_path / "Fillin" / "ppl" / "inputs",
                "FN": self.main_window.base_path / "Fillin" / "fn" / "inputs"
            }
            
            # 记录实际分发的文件类型
            distributed_types = set()
            
            # 扫描Apply输出文件夹
            for file_path in apply_output_path.iterdir():
                if file_path.is_file():
                    filename = file_path.name
                    target_dir = None
                    file_type = None
                    
                    # 根据文件名内容确定文件类型
                    if "设计验证计划" in filename:
                        target_dir = fillin_mapping["DVP"]
                        file_type = "DVP"
                    elif "接口定义通知单" in filename or "接口定义" in filename:
                        target_dir = fillin_mapping["FN"]
                        file_type = "FN"
                    elif "开发匹配测试计划" in filename or "匹配计划" in filename:
                        target_dir = fillin_mapping["PPL"]
                        file_type = "PPL"
                    else:
                        # 备用识别方式：使用原有的关键字识别
                        if "DVP" in filename:
                            target_dir = fillin_mapping["DVP"]
                            file_type = "DVP"
                        elif "FN" in filename:
                            target_dir = fillin_mapping["FN"]
                            file_type = "FN"
                        elif "PPL" in filename:
                            target_dir = fillin_mapping["PPL"]
                            file_type = "PPL"
                        else:
                            self.logger.warning(f"未识别的文件类型，跳过文件: {filename}")
                            continue  # 跳过未识别的文件
                    
                    if target_dir and file_type:
                        target_dir.mkdir(parents=True, exist_ok=True)
                        target_file = target_dir / filename
                        shutil.move(str(file_path), str(target_file))
                        distributed_types.add(file_type)
                        self.logger.info(f"分发文件: {filename} -> {target_dir}")
            
            return distributed_types
            
        except Exception as e:
            self.logger.error(f"分发文件失败: {str(e)}")
            raise

    def run_fillin_processes(self, distributed_types):
        """运行Fillin各个模块的处理程序"""
        try:
            # 使用当前Python解释器的完整路径
            python_executable = sys.executable
            
            # 定义脚本路径和对应的文件类型
            fillin_scripts = [
                (self.main_window.base_path / "Fillin" / "dvp" / "main.py", "DVP"),
                (self.main_window.base_path / "Fillin" / "fn" / "src" / "main.py", "FN"),
                (self.main_window.base_path / "Fillin" / "ppl" / "src" / "main.py", "PPL")
            ]
            
            executed_count = 0
            total_to_execute = len([script for script, file_type in fillin_scripts if file_type in distributed_types])
            
            for script, file_type in fillin_scripts:
                # 只运行有对应文件分发的程序
                if file_type not in distributed_types:
                    self.logger.info(f"跳过填写程序 {script}：没有对应的 {file_type} 文件")
                    continue
                
                if script.exists():
                    self.logger.info(f"运行填写程序: {script}")
                    
                    # 使用当前Python解释器运行脚本，添加错误处理以避免UnicodeDecodeError
                    result = subprocess.run([
                        python_executable, str(script)
                    ], cwd=str(script.parent),
                       capture_output=True, text=True, encoding='utf-8', errors='replace')
                    
                    if result.stdout:
                        self.logger.info(f"填写程序输出 {script}: {result.stdout}")
                    
                    if result.returncode != 0:
                        error_msg = result.stderr or result.stdout or "未知错误"
                        self.logger.warning(f"填写程序执行警告 {script}: {error_msg}")
                    else:
                        self.logger.info(f"填写程序执行成功: {script}")
                
                executed_count += 1
                # 更新进度（通过主窗口）
                if total_to_execute > 0:
                    progress = 70 + (executed_count * 20 // total_to_execute)
                    self.main_window.progress_bar.setValue(min(progress, 90))
            
            return executed_count > 0
            
        except Exception as e:
            self.logger.error(f"运行填写进程失败: {str(e)}")
            raise

    def collect_final_results(self):
        """收集最终处理结果到车型文件夹"""
        try:
            # 创建结果文件夹
            vehicle_dir = self.main_window.vehicles_path / self.main_window.current_vehicle_code
            result_folder = vehicle_dir / "Numbered_and_Filled"
            result_folder.mkdir(parents=True, exist_ok=True)
            
            # 收集所有输出文件
            output_dirs = [
                self.main_window.base_path / "Fillin" / "dvp" / "output",
                self.main_window.base_path / "Fillin" / "fn" / "outputs",
                self.main_window.base_path / "Fillin" / "ppl" / "outputs"
            ]
            
            collected_files = 0
            for output_dir in output_dirs:
                if output_dir.exists():
                    for file_path in output_dir.iterdir():
                        if file_path.is_file():
                            target_file = result_folder / file_path.name
                            shutil.copy2(file_path, target_file)
                            self.logger.info(f"收集结果文件: {file_path.name}")
                            collected_files += 1
            
            # 同时复制到Final_Approval_Documents文件夹
            final_approval_dir = self.main_window.base_path / "Final_Approval_Documents"
            final_approval_dir.mkdir(parents=True, exist_ok=True)
            
            for file_path in result_folder.iterdir():
                if file_path.is_file():
                    target_file = final_approval_dir / file_path.name
                    shutil.copy2(file_path, target_file)
            
            self.logger.info(f"处理完成，共收集 {collected_files} 个文件到: {result_folder}")
            return collected_files
            
        except Exception as e:
            self.logger.error(f"收集结果失败: {str(e)}")
            raise

    def copy_template_data_to_root(self):
        """将模板数据文件复制到根目录和Upload\Data文件夹（用于上传功能）"""
        try:
            if not self.main_window.current_vehicle_code:
                self.logger.warning("未选择车型代号")
                return False

            # 源文件路径
            source_file = (self.main_window.vehicles_path / self.main_window.current_vehicle_code /
                          "information" / f"{self.main_window.current_vehicle_code}_Fill_Template_Data.xlsx")

            if not source_file.exists():
                self.logger.warning(f"源文件不存在: {source_file}")
                return False

            # 第一步：复制到根目录
            target_file_root = self.main_window.base_path / "Fill_Template_Data.xlsx"
            shutil.copy2(source_file, target_file_root)
            self.logger.info(f"已复制模板数据文件到根目录: {target_file_root}")

            # 第二步：复制到Upload\Data文件夹（先清空该文件夹）
            upload_data_dir = self.main_window.base_path / "Upload" / "Data"
            self.clear_directory(upload_data_dir)

            target_file_upload = upload_data_dir / "Fill_Template_Data.xlsx"
            shutil.copy2(source_file, target_file_upload)
            self.logger.info(f"已复制模板数据文件到Upload\Data: {target_file_upload}")

            return True

        except Exception as e:
            self.logger.error(f"复制模板数据文件失败: {str(e)}")
            return False
