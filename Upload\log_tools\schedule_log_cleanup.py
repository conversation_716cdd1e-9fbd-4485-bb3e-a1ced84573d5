"""
定时日志清理脚本
可以作为定时任务运行，自动维护日志文件大小
"""

import schedule
import time
import os
from log_manager import LogManager
from datetime import datetime


def scheduled_log_cleanup():
    """定时执行的日志清理任务"""
    print(f"=== 定时日志清理开始 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
    
    # 创建日志管理器
    log_manager = LogManager(
        log_file="upload_approval.log",
        max_size_mb=5,  # 5MB限制
        max_backups=3,  # 保留3个备份
        max_days=7      # 保留7天
    )
    
    # 执行清理
    log_manager.check_and_clean()
    
    print(f"=== 定时日志清理完成 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")


def run_scheduler():
    """运行定时调度器"""
    print("日志清理定时任务已启动...")
    print("计划任务:")
    print("- 每天凌晨2点清理")
    print("- 每小时检查一次")
    
    # 每天凌晨2点执行清理
    schedule.every().day.at("02:00").do(scheduled_log_cleanup)
    
    # 每小时检查一次（可选）
    schedule.every().hour.do(scheduled_log_cleanup)
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        print("\n定时任务已停止")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "once":
        # 立即执行一次清理
        scheduled_log_cleanup()
    else:
        # 运行定时任务
        run_scheduler()
