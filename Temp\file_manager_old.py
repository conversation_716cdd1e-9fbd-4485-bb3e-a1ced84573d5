import os
import shutil
import logging
from pathlib import Path
import pandas as pd
from datetime import datetime
import subprocess
import sys


class FileManager:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.fillin_path = self.base_path / "Fillin"
        self.logger = logging.getLogger(__name__)
          # 文件类型映射
        self.file_mappings = {
            "DVP": {
                "template": "XX项目VSE软件设计验证计划（DVP）.xlsx",
                "pattern": "VSE软件设计验证计划（DVP）",
                "extension": ".xlsx"
            },
            "PPL": {
                "template": "XX项目VSE软件开发匹配测试计划.xlsx", 
                "pattern": "VSE软件开发匹配测试计划",
                "extension": ".xlsx"
            },
            "FN_IMU": {
                "template": "XX项目VSE系统 to 安全气囊节点接口定义通知单.docx",
                "pattern": "VSE系统 to 安全气囊节点接口定义通知单",
                "extension": ".docx"
            },
            "FN_VCU": {
                "template": "XX项目VSE系统 to VCU系统接口定义通知单.docx",
                "pattern": "VSE系统 to VCU系统接口定义通知单", 
                "extension": ".docx"
            },
            "FN_IPB": {
                "template": "XX项目VSE系统 to IPB系统接口定义通知单.docx",
                "pattern": "VSE系统 to IPB系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_ESP_BWA": {
                "template": "XX项目VSE系统 to ESP+BWA系统接口定义通知单.docx",
                "pattern": "VSE系统 to ESP+BWA系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_EPS": {
                "template": "XX项目VSE系统 to EPS系统接口定义通知单.docx",
                "pattern": "VSE系统 to EPS系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_EPSA": {
                "template": "XX项目VSE系统 to EPSA系统接口定义通知单.docx",
                "pattern": "VSE系统 to EPSA系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_EPB": {
                "template": "XX项目VSE系统 to EPB系统接口定义通知单.docx",
                "pattern": "VSE系统 to EPB系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_A": {
                "template": "XX项目VSE系统 to DiSus-A系统接口定义通知单.docx",
                "pattern": "VSE系统 to DiSus-A系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_C": {
                "template": "XX项目VSE系统 to DiSus-C系统接口定义通知单 .docx",
                "pattern": "VSE系统 to DiSus-C系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_P": {
                "template": "XX项目VSE系统 to DiSus-P系统接口定义通知单.docx",
                "pattern": "VSE系统 to DiSus-P系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_X": {
                "template": "XX项目VSE系统 to DiSus-X系统接口定义通知单.docx",
                "pattern": "VSE系统 to DiSus-X系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_M": {
                "template": "XX项目VSE系统 to DiSus-M系统接口定义通知单.docx",
                "pattern": "VSE系统 to DiSus-M系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_域控": {
                "template": "XX项目VSE系统 to 跨域计算平台接口定义通知单.docx",
                "pattern": "VSE系统 to 跨域计算平台接口定义通知单",
                "extension": ".docx"
            }
        }

    def setup_vehicle_folder(self, vehicle_code):
        """设置车型文件夹结构"""
        try:
            vehicle_dir = self.vehicles_path / vehicle_code
            info_dir = vehicle_dir / "information"
            info_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制并重命名Fill_Template_Data.xlsx
            template_file = self.templates_path / "Fill_Template_Data.xlsx"
            target_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not target_file.exists():
                shutil.copy2(template_file, target_file)
                self.update_vehicle_code_in_excel(target_file, vehicle_code)
                self.logger.info(f"创建车型模板文件: {target_file}")
                return True, target_file
            else:
                self.logger.info(f"车型模板文件已存在: {target_file}")
                return False, target_file
                
        except Exception as e:
            self.logger.error(f"设置车型文件夹失败: {str(e)}")
            raise

    def update_vehicle_code_in_excel(self, excel_file, vehicle_code):
        """更新Excel文件中的车型代号"""
        try:
            # 读取Info sheet
            df = pd.read_excel(excel_file, sheet_name="Info")
            df.loc[df['角色'] == '车型代号', 'people'] = vehicle_code
            
            # 写回Excel文件
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name="Info", index=False)
            
            self.logger.info(f"更新Excel中车型代号为: {vehicle_code}")
            
        except Exception as e:
            self.logger.error(f"更新Excel中车型代号失败: {str(e)}")
            raise

    def find_template_file(self, file_type):
        """根据文件类型查找模板文件"""
        if file_type not in self.file_mappings:
            return None
            
        mapping = self.file_mappings[file_type]
        pattern = mapping.get("pattern", "")
        extension = mapping.get("extension", "")
        
        # 在Templates文件夹中查找包含关键字的文件
        for template_file in self.templates_path.glob(f"*{extension}"):
            if pattern in template_file.name:
                self.logger.info(f"找到匹配的模板文件: {template_file.name} (关键字: {pattern})")
                return template_file
        
        # 如果按关键字没找到，尝试精确匹配原来的文件名
        exact_file = self.templates_path / mapping["template"]
        if exact_file.exists():
            return exact_file
              self.logger.warning(f"未找到匹配的模板文件，关键字: {pattern}, 扩展名: {extension}")
        return None

    def copy_template_files(self, vehicle_code, selected_files):
        """复制模板文件到车型文件夹"""
        vehicle_dir = self.vehicles_path / vehicle_code
        vehicle_dir.mkdir(exist_ok=True)
        
        copied_files = []
        new_files = []
        
        for file_type in selected_files:
            if file_type in self.file_mappings:
                mapping = self.file_mappings[file_type]
                
                # 使用新的查找方法
                template_file = self.find_template_file(file_type)
                
                if template_file and template_file.exists():
                    # 生成目标文件名，将XX替换为车型代号
                    target_name = f"{vehicle_code}项目{mapping['pattern']}{mapping['extension']}"
                    target_path = vehicle_dir / target_name
                    
                    if not target_path.exists():
                        # 复制文件
                        shutil.copy2(template_file, target_path)
                        
                        # 修改文件内容中的XX为车型代号
                        self.replace_vehicle_code_in_file(target_path, vehicle_code)
                        
                        copied_files.append(target_name)
                        new_files.append(target_name)
                        self.logger.info(f"复制并修改模板文件: {template_file.name} -> {target_name}")
                    else:
                        self.logger.info(f"文件已存在，跳过复制: {target_name}")
                else:
                    self.logger.warning(f"未找到模板文件，文件类型: {file_type}")
        
        # 更新File_Status表
        if new_files:
            self.update_file_status(vehicle_code, new_files)
        
        return copied_files

    def replace_vehicle_code_in_file(self, file_path, vehicle_code):
        """替换文件内容中的XX为车型代号"""
        try:
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.xlsx':
                # 处理Excel文件
                self.replace_in_excel(file_path, vehicle_code)
            elif file_extension == '.docx':
                # 处理Word文件
                self.replace_in_word(file_path, vehicle_code)
            else:
                self.logger.warning(f"不支持的文件类型: {file_extension}")
                
        except Exception as e:
            self.logger.error(f"替换文件内容失败: {str(e)}")

    def replace_in_excel(self, file_path, vehicle_code):
        """替换Excel文件中的XX"""
        try:
            # 读取Excel文件的所有sheet
            excel_file = pd.ExcelFile(file_path)
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for sheet_name in excel_file.sheet_names:
                    df = excel_file.parse(sheet_name)
                    
                    # 替换所有列中的XX为车型代号
                    for col in df.columns:
                        if df[col].dtype == 'object':  # 只处理文本列
                            df[col] = df[col].astype(str).str.replace('XX', vehicle_code, regex=False)
                    
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
            self.logger.info(f"Excel文件内容替换完成: {file_path}")
            
        except Exception as e:
            self.logger.error(f"替换Excel文件内容失败: {str(e)}")

    def replace_in_word(self, file_path, vehicle_code):
        """替换Word文件中的XX"""
        try:
            # 这里可以使用python-docx库来处理Word文件
            # 由于可能没有安装python-docx，先记录日志
            self.logger.info(f"Word文件内容替换功能待完善: {file_path}")
            # TODO: 实现Word文件内容替换
            
        except Exception as e:
            self.logger.error(f"替换Word文件内容失败: {str(e)}")

    def update_file_status(self, vehicle_code, new_files):
        """更新File_Status表"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                # 读取现有的File_Status
                try:
                    df = pd.read_excel(excel_file, sheet_name="File_Status")
                except:
                    # 如果sheet不存在，创建新的
                    df = pd.DataFrame(columns=['file_name', 'code', 'numbered_file', 'is_fillin', 'is_upload'])
                
                # 添加新文件
                for file_name in new_files:
                    # 去掉扩展名
                    name_without_ext = Path(file_name).stem
                    
                    # 检查是否已存在
                    if name_without_ext not in df['file_name'].values:
                        new_row = {
                            'file_name': name_without_ext,
                            'code': '',
                            'numbered_file': '',
                            'is_fillin': 'N',
                            'is_upload': 'N'
                        }
                        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
                
                # 写回Excel
                with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                    df.to_excel(writer, sheet_name="File_Status", index=False)
                
                self.logger.info(f"更新File_Status表，添加 {len(new_files)} 个新文件")
                
        except Exception as e:
            self.logger.error(f"更新File_Status表失败: {str(e)}")

    def get_files_for_apply_id(self, vehicle_code):
        """获取需要申请编号的文件"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not excel_file.exists():
                return []
            
            df = pd.read_excel(excel_file, sheet_name="File_Status")
            
            # 筛选出需要申请编号的文件（code和numbered_file都为空）
            need_apply = df[(df['code'].isna() | (df['code'] == '')) & 
                           (df['numbered_file'].isna() | (df['numbered_file'] == ''))]
            
            apply_files = []
            for _, row in need_apply.iterrows():
                file_name = row['file_name']
                file_path = self.vehicles_path / vehicle_code / f"{file_name}.xlsx"
                if not file_path.exists():
                    file_path = self.vehicles_path / vehicle_code / f"{file_name}.docx"
                
                if file_path.exists():
                    apply_files.append({
                        'file_name': file_name,
                        'file_path': file_path,
                        'file_type': self.determine_file_type(file_name)
                    })
            
            return apply_files
            
        except Exception as e:
            self.logger.error(f"获取需要申请编号的文件失败: {str(e)}")
            return []

    def determine_file_type(self, file_name):
        """根据文件名确定文件类型"""
        if "软件设计验证计划" in file_name or "DVP" in file_name:
            return "DVP"
        elif "软件开发匹配测试计划" in file_name or "PPL" in file_name:
            return "PPL"
        elif "接口定义通知单" in file_name or "FN" in file_name:
            return "FN"
        else:
            return "UNKNOWN"

    def update_file_with_id(self, vehicle_code, file_name, file_id):
        """用申请到的编号更新文件名和内容"""
        try:
            vehicle_dir = self.vehicles_path / vehicle_code
            info_dir = vehicle_dir / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            # 找到原文件
            original_file = None
            for ext in ['.xlsx', '.docx']:
                test_path = vehicle_dir / f"{file_name}{ext}"
                if test_path.exists():
                    original_file = test_path
                    break
            
            if not original_file:
                raise FileNotFoundError(f"找不到文件: {file_name}")
            
            # 生成新文件名
            new_file_name = f"{file_id}-{original_file.name}"
            new_file_path = vehicle_dir / new_file_name
            
            # 重命名文件
            shutil.move(original_file, new_file_path)
            
            # 更新文件内容中的编号
            self.update_file_content_with_id(new_file_path, file_id)
            
            # 更新Excel表格
            self.update_file_codes_and_status(vehicle_code, file_name, file_id, new_file_name)
            
            self.logger.info(f"文件更新完成: {original_file.name} -> {new_file_name}")
            return new_file_path
            
        except Exception as e:
            self.logger.error(f"更新文件编号失败: {str(e)}")
            raise

    def update_file_content_with_id(self, file_path, file_id):
        """更新文件内容中的编号"""
        try:
            # 调用fillin_filecode.py中的功能
            if file_path.suffix == '.docx':
                self.update_docx_with_id(file_path, file_id)
            elif file_path.suffix == '.xlsx':
                self.update_xlsx_with_id(file_path, file_id)
                
        except Exception as e:
            self.logger.error(f"更新文件内容编号失败: {str(e)}")

    def update_docx_with_id(self, file_path, file_id):
        """更新DOCX文件中的编号"""
        try:
            # 这里调用fillin_filecode.py中的update_docx_document函数
            from fillin_filecode import update_docx_document
            update_docx_document(file_path, file_id)
        except Exception as e:
            self.logger.error(f"更新DOCX文件编号失败: {str(e)}")

    def update_xlsx_with_id(self, file_path, file_id):
        """更新XLSX文件中的编号"""
        try:
            # 这里调用fillin_filecode.py中的update_xlsx_document函数
            from fillin_filecode import update_xlsx_document
            update_xlsx_document(file_path, file_id)
        except Exception as e:
            self.logger.error(f"更新XLSX文件编号失败: {str(e)}")

    def update_file_codes_and_status(self, vehicle_code, file_name, file_id, numbered_file_name):
        """更新File_Code和File_Status表"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            # 更新File_Code表
            try:
                df_codes = pd.read_excel(excel_file, sheet_name="File_Code")
            except:
                df_codes = pd.DataFrame(columns=['code', 'file_name', 'is_use'])
            
            # 添加新的编号记录
            new_code_row = {
                'code': file_id,
                'file_name': file_name,
                'is_use': 'N'
            }
            df_codes = pd.concat([df_codes, pd.DataFrame([new_code_row])], ignore_index=True)
            
            # 更新File_Status表
            df_status = pd.read_excel(excel_file, sheet_name="File_Status")
            df_status.loc[df_status['file_name'] == file_name, 'code'] = file_id
            df_status.loc[df_status['file_name'] == file_name, 'numbered_file'] = numbered_file_name
            
            # 写回Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                df_codes.to_excel(writer, sheet_name="File_Code", index=False)
                df_status.to_excel(writer, sheet_name="File_Status", index=False)
            
            self.logger.info(f"更新File_Code和File_Status表完成")
            
        except Exception as e:
            self.logger.error(f"更新File_Code和File_Status表失败: {str(e)}")

    def get_files_for_fill_content(self, vehicle_code, selected_files):
        """获取需要填写内容的文件"""
        try:
            vehicle_dir = self.vehicles_path / vehicle_code
            fill_files = []
            
            for file_type in selected_files:
                if file_type in self.file_mappings:
                    mapping = self.file_mappings[file_type]
                    
                    # 寻找匹配的文件（可能有编号前缀）
                    pattern = mapping["pattern"]
                    extension = mapping["extension"]
                    
                    for file_path in vehicle_dir.glob(f"*{pattern}{extension}"):
                        fill_files.append({
                            'file_type': file_type,
                            'file_path': file_path,
                            'mapping': mapping
                        })
            
            return fill_files
            
        except Exception as e:
            self.logger.error(f"获取需要填写内容的文件失败: {str(e)}")
            return []

    def fill_file_content(self, vehicle_code, file_info):
        """填写文件内容"""
        try:
            file_type = file_info['file_type']
            file_path = file_info['file_path']
            
            # 根据文件类型调用相应的填写程序
            if file_type == "DVP":
                return self.run_dvp_filler(vehicle_code, file_path)
            elif file_type == "PPL":
                return self.run_ppl_filler(vehicle_code, file_path)
            elif file_type.startswith("FN"):
                return self.run_fn_filler(vehicle_code, file_path)
            else:
                self.logger.warning(f"不支持的文件类型: {file_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"填写文件内容失败: {str(e)}")
            return False

    def run_dvp_filler(self, vehicle_code, file_path):
        """运行DVP填写程序"""
        try:
            dvp_main = self.fillin_path / "dvp" / "main.py"
            if not dvp_main.exists():
                self.logger.error("DVP填写程序不存在")
                return False
            
            # 准备输入和输出目录
            input_dir = self.fillin_path / "dvp" / "input"
            output_dir = self.fillin_path / "dvp" / "output"
            input_dir.mkdir(exist_ok=True)
            output_dir.mkdir(exist_ok=True)
            
            # 复制文件到输入目录
            input_file = input_dir / file_path.name
            shutil.copy2(file_path, input_file)
            
            # 运行填写程序
            result = subprocess.run([
                sys.executable, str(dvp_main)
            ], cwd=str(dvp_main.parent), capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                # 复制输出文件回车型目录
                output_files = list(output_dir.glob(f"*{file_path.stem}*"))
                if output_files:
                    shutil.copy2(output_files[0], file_path)
                    self.update_fill_status(vehicle_code, file_path.stem, 'Y')
        """运行FN填写程序"""
        try:
            fn_main = self.fillin_path / "fn" / "src" / "main.py"
            if not fn_main.exists():
                self.logger.error("FN填写程序不存在")
                return False
            
            # 准备输入和输出目录
            input_dir = self.fillin_path / "fn" / "inputs"
            output_dir = self.fillin_path / "fn" / "outputs"
            input_dir.mkdir(exist_ok=True)
            output_dir.mkdir(exist_ok=True)
            
            # 复制文件到输入目录
            input_file = input_dir / file_path.name
            shutil.copy2(file_path, input_file)
            
            # 运行填写程序
            result = subprocess.run([
                sys.executable, str(fn_main)
            ], cwd=str(fn_main.parent.parent), capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                # 复制输出文件回车型目录
                output_files = list(output_dir.glob(f"*{file_path.stem}*"))
                if output_files:
                    shutil.copy2(output_files[0], file_path)
                    self.update_fill_status(vehicle_code, file_path.stem, 'Y')
                    self.logger.info(f"FN文件填写完成: {file_path.name}")
                    return True
            else:
                self.logger.error(f"FN填写程序执行失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"运行FN填写程序失败: {str(e)}")
            return False

    def update_fill_status(self, vehicle_code, file_name, status):
        """更新文件填写状态"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            df = pd.read_excel(excel_file, sheet_name="File_Status")
            df.loc[df['file_name'].str.contains(file_name, na=False), 'is_fillin'] = status
            
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name="File_Status", index=False)
            
            self.logger.info(f"更新填写状态: {file_name} -> {status}")
            
        except Exception as e:
            self.logger.error(f"更新填写状态失败: {str(e)}")

    def get_reviewer_info(self, vehicle_code):
        """获取审批人员信息"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            df = pd.read_excel(excel_file, sheet_name="Info")
            
            reviewers = {
                'data_managers': [],
                'section_chief': '',
                'related_parties': []
            }
            
            for _, row in df.iterrows():
                role = row.get('角色', '')
                email = row.get('邮箱', '')
                work_id = row.get('工号', '')
                
                if '数据管理员' in role and work_id:
                    reviewers['data_managers'].append(work_id)
                elif '科长' in role and email:
                    reviewers['section_chief'] = email
                elif '相关方' in role and email:
                    reviewers['related_parties'].append(email)
            
            return reviewers
            
        except Exception as e:
            self.logger.error(f"获取审批人员信息失败: {str(e)}")
            return {}

    def get_upload_files(self, vehicle_code):
        """获取需要上传的文件"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            df = pd.read_excel(excel_file, sheet_name="File_Status")
            
            # 筛选出已填写但未上传的文件
            upload_files = df[(df['is_fillin'] == 'Y') & (df['is_upload'] != 'Y')]
            
            file_list = []
            vehicle_dir = self.vehicles_path / vehicle_code
            
            for _, row in upload_files.iterrows():
                numbered_file = row.get('numbered_file', '')
                if numbered_file:
                    source_file = vehicle_dir / numbered_file
                    if source_file.exists():
                        # 生成PDF文件路径（假设已经生成）
                        pdf_file = source_file.with_suffix('.pdf')
                        
                        file_list.append({
                            'file_code': row.get('code', ''),
                            'file_name': numbered_file,
                            'file_type': self.determine_file_type(numbered_file),
                            'source_file_path': source_file,
                            'pdf_file_path': pdf_file
                        })
            
            return file_list
            
        except Exception as e:
            self.logger.error(f"获取上传文件列表失败: {str(e)}")
            return []

    def update_upload_status(self, vehicle_code, file_name, status):
        """更新文件上传状态"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            # 更新File_Status
            df_status = pd.read_excel(excel_file, sheet_name="File_Status")
            df_status.loc[df_status['numbered_file'] == file_name, 'is_upload'] = status
            
            # 如果上传成功，更新File_Code中的is_use状态
            if status == 'Y':
                df_codes = pd.read_excel(excel_file, sheet_name="File_Code")
                # 从文件名中提取编号
                file_code = file_name.split('-')[0] if '-' in file_name else ''
                if file_code:
                    df_codes.loc[df_codes['code'] == file_code, 'is_use'] = 'Y'
                
                with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                    df_status.to_excel(writer, sheet_name="File_Status", index=False)
                    df_codes.to_excel(writer, sheet_name="File_Code", index=False)
            else:
                with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                    df_status.to_excel(writer, sheet_name="File_Status", index=False)
            
            self.logger.info(f"更新上传状态: {file_name} -> {status}")
            
        except Exception as e:
            self.logger.error(f"更新上传状态失败: {str(e)}")

    def replace_templates(self):
        """从Update_Templates文件夹替换模板"""
        try:
            update_templates_path = self.base_path / "Update_Templates"
            if not update_templates_path.exists():
                self.logger.warning("Update_Templates文件夹不存在")
                return False
            
            replaced_count = 0
            for template_file in update_templates_path.iterdir():
                if template_file.is_file():
                    target_file = self.templates_path / template_file.name
                    if target_file.exists():
                        shutil.copy2(template_file, target_file)
                        replaced_count += 1
                        self.logger.info(f"替换模板文件: {template_file.name}")
            
            self.logger.info(f"模板替换完成，共替换 {replaced_count} 个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"替换模板失败: {str(e)}")
            return False
