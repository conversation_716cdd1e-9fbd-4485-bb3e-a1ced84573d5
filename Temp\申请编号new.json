{"Name": "申请编号2", "CreationDate": "2025-6-24", "Commands": [{"Command": "open", "Target": "https://gcy.byd.com/dms/#/home", "Value": "", "Description": ""}, {"Command": "clickAndWait", "Target": "xpath=//*[@id=\"app\"]/section/header/div/div[2]/button/span", "Value": "", "Targets": ["xpath=//*[@id=\"app\"]/section/header/div/div[2]/button/span", "xpath=//span", "css=#app > section > header > div > div.flx-justify-between > button > span"], "Description": "点击登录按钮"}, {"Command": "type", "Target": "xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div/div/div/input", "Value": "6604331", "Targets": ["xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div/div/div/input", "xpath=//input[@type='text']", "xpath=//input", "css=#container > div > div.login-content > div.login-password > form > div.el-form-item.is-success.is-required > div > div > input"], "Description": ""}, {"Command": "type", "Target": "xpath=//*[@id=\"content\"]/div[4]/form/div/div/div/input", "Value": "6604331", "Targets": ["xpath=//*[@id=\"content\"]/div[4]/form/div/div/div/input", "xpath=//div[4]/form/div/div/div/input", "css=#content > div.content-change.content-weChat > form > div.el-form-item.is-success.is-required > div > div > input"], "Description": ""}, {"Command": "type", "Target": "xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div[2]/div/div/input", "Value": "yo156240.", "Targets": ["xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div[2]/div/div/input", "xpath=//input[@type='password']", "xpath=//div[2]/div/div/input", "css=#container > div > div.login-content > div.login-password > form > div:nth-child(2) > div > div > input"], "Description": ""}, {"Command": "type", "Target": "xpath=//*[@id=\"content\"]/div[4]/form/div[2]/div/div/input", "Value": "yo156240.", "Targets": ["xpath=//*[@id=\"content\"]/div[4]/form/div[2]/div/div/input", "xpath=//div[4]/form/div[2]/div/div/input", "css=#content > div.content-change.content-weChat > form > div:nth-child(2) > div > div > input"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"content\"]/div[2]/div", "Value": "", "Targets": ["xpath=//*[@id=\"content\"]/div[2]/div", "xpath=//div[2]/div[2]/div", "css=#content > div:nth-child(2) > div"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div/div/div/input", "Value": "", "Targets": ["xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div/div/div/input", "xpath=//input[@type='text']", "xpath=//input", "css=#container > div > div.login-content > div.login-password > form > div:nth-child(1) > div > div > input"], "Description": "点击填写账号的框"}, {"Command": "click", "Target": "xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div[2]/div/div/input", "Value": "", "Targets": ["xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div[2]/div/div/input", "xpath=//input[@type='password']", "xpath=//div[2]/div/div/input", "css=#container > div > div.login-content > div.login-password > form > div:nth-child(2) > div > div > input"], "Description": "点击填写密码的框"}, {"Command": "clickAndWait", "Target": "xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div[4]/div/button", "Value": "", "Targets": ["xpath=//*[@id=\"container\"]/div/div[2]/div[2]/form/div[4]/div/button", "xpath=//div/button", "css=#container > div > div.login-content > div.login-password > form > div:nth-child(4) > div > button"], "Description": "点击登录的按钮"}, {"Command": "click", "Target": "xpath=//*[@id=\"main\"]/div/div/div/div/div/div[3]/span", "Value": "", "Targets": ["xpath=//*[@id=\"main\"]/div/div/div/div/div/div[3]/span", "xpath=//div[3]/span", "css=#main > div > div > div:nth-child(1) > div:nth-child(1) > div > div.skip.NumberApplication > span"], "Description": "点击申请编号按钮，右侧弹出了模态框"}, {"Command": "click", "Target": "css=#el-collapse-content-14 > div > form > div > div.el-form-item__content > div > div > div.el-select__suffix > i > svg", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-14\"]/div/form/div/div[2]/div/div/div[2]/i/svg", "css=#el-collapse-content-14 > div > form > div > div.el-form-item__content > div > div > div.el-select__suffix > i > svg"], "Description": "点击文件类型下拉框"}, {"Command": "click", "Target": "id=el-id-7049-24", "Value": "", "Targets": ["id=el-id-7049-24", "xpath=//*[@id=\"el-id-7049-24\"]", "xpath=//li[@id='el-id-7049-24']", "xpath=//div[2]/div/div/div/ul/li", "css=#el-id-7049-24"], "Description": "下拉框中选择了项目文件"}, {"Command": "click", "Target": "css=#el-collapse-content-14 > div > form > div:nth-child(2) > div > div.el-form-item__content > span > div > div > div.el-select__suffix > i > svg", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-14\"]/div/form/div[2]/div/div[2]/span/div/div/div[2]/i/svg", "css=#el-collapse-content-14 > div > form > div:nth-child(2) > div > div.el-form-item__content > span > div > div > div.el-select__suffix > i > svg"], "Description": ""}, {"Command": "click", "Target": "id=el-id-7049-681", "Value": "", "Targets": ["id=el-id-7049-681", "xpath=//*[@id=\"el-id-7049-681\"]", "xpath=//input[@id='el-id-7049-681']", "xpath=//span/div/div/div/div/input", "css=#el-id-7049-681"], "Description": "点击项目文档填写的框,我在里面填了DVP"}, {"Command": "type", "Target": "id=el-id-7049-681", "Value": "DVP", "Targets": ["id=el-id-7049-681", "xpath=//*[@id=\"el-id-7049-681\"]", "xpath=//input[@id='el-id-7049-681']", "xpath=//span/div/div/div/div/input", "css=#el-id-7049-681"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-id-7049-95\"]/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-id-7049-95\"]/span", "xpath=//li[67]/span", "css=#el-id-7049-95 > span"], "Description": "点击了DVP-系统设计验证计划"}, {"Command": "click", "Target": "id=el-id-7049-685", "Value": "", "Targets": ["id=el-id-7049-685", "xpath=//*[@id=\"el-id-7049-685\"]", "xpath=//input[@id='el-id-7049-685']", "xpath=//div[3]/div[2]/div/div/div/div/input", "css=#el-id-7049-685"], "Description": "点击了输入项目代号的框，输入了HYHB"}, {"Command": "click", "Target": "id=el-id-7049-685", "Value": "", "Targets": ["id=el-id-7049-685", "xpath=//*[@id=\"el-id-7049-685\"]", "xpath=//input[@id='el-id-7049-685']", "xpath=//div[3]/div[2]/div/div/div/div/input", "css=#el-id-7049-685"], "Description": ""}, {"Command": "type", "Target": "id=el-id-7049-685", "Value": "HYHB", "Targets": ["id=el-id-7049-685", "xpath=//*[@id=\"el-id-7049-685\"]", "xpath=//input[@id='el-id-7049-685']", "xpath=//div[3]/div[2]/div/div/div/div/input", "css=#el-id-7049-685"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-id-7049-691\"]/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-id-7049-691\"]/span", "xpath=//div[6]/div/div/div/ul/li/span", "css=#el-id-7049-691 > span"], "Description": "选中了HYHB"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/button/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/button/span", "xpath=//button/span", "css=#el-collapse-content-19 > div > button > span"], "Description": "点击新增行按钮，DVP只需要一份文件，故点击一下就行，FN有多份的话，可以点多次"}, {"Command": "click", "Target": "id=el-id-7049-707", "Value": "", "Targets": ["id=el-id-7049-707", "xpath=//*[@id=\"el-id-7049-707\"]", "xpath=//input[@id='el-id-7049-707']", "xpath=//td[2]/div/div/div/input", "css=#el-id-7049-707"], "Description": "点击了新增行的第一行的文档名称的输入框，并输入DVP文件，实际应该填车型文件夹里文件的名称，不含后缀"}, {"Command": "type", "Target": "id=el-id-7049-707", "Value": "DVP文件", "Targets": ["id=el-id-7049-707", "xpath=//*[@id=\"el-id-7049-707\"]", "xpath=//input[@id='el-id-7049-707']", "xpath=//td[2]/div/div/div/input", "css=#el-id-7049-707"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[3]/div/button/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[3]/div/button/span", "xpath=//td[3]/div/button/span", "css=#el-collapse-content-19 > div > form > div > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr > td.el-table_2_column_12.el-table__cell > div > button > span"], "Description": "点击生成编号按钮，生成了编号"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span", "xpath=//td[4]/div/span", "css=#el-collapse-content-19 > div > form > div > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr > td.el-table_2_column_13.el-table__cell > div > span"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span", "xpath=//td[4]/div/span", "css=#el-collapse-content-19 > div > form > div > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr > td.el-table_2_column_13.el-table__cell > div > span"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span", "xpath=//td[4]/div/span", "css=#el-collapse-content-19 > div > form > div > div.el-table__inner-wrapper > div.el-table__body-wrapper > div > div.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default > div > table > tbody > tr > td.el-table_2_column_13.el-table__cell > div > span"], "Description": "这里连续点击了三次可以选中生成的编号，选中后就可以复制保存到本地了，当然要跟文件名对齐"}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/button/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/button/span", "xpath=//button/span", "css=#el-collapse-content-19 > div > button > span"], "Description": ""}, {"Command": "click", "Target": "xpath=//*[@id=\"el-collapse-content-19\"]/div/button/span", "Value": "", "Targets": ["xpath=//*[@id=\"el-collapse-content-19\"]/div/button/span", "xpath=//button/span", "css=#el-collapse-content-19 > div > button > span"], "Description": "又点了两次新增行，可以申请更多的DVP文件编号"}, {"Command": "click", "Target": "xpath=//*[@id=\"main\"]/div/div/div[2]/div/div/div[2]/div/button/span", "Value": "", "Targets": ["xpath=//*[@id=\"main\"]/div/div/div[2]/div/div/div[2]/div/button/span", "xpath=//div[2]/div/div/div[2]/div/button/span", "css=#main > div > div > div.drawer-wrap.container > div > div > div.el-drawer__footer > div > button.el-button.el-button--primary > span"], "Description": "点击提交按钮，编号就申请好了"}, {"Command": "click", "Target": "xpath=//*[@id=\"main\"]/div/div/div/div/wujie-app", "Value": "", "Targets": ["xpath=//*[@id=\"main\"]/div/div/div/div/wujie-app", "xpath=//wujie-app", "css=#main > div > div > div.h-full.w-full.overflow-hidden.micro-app-container > div > wujie-app"], "Description": "可以利用点击提交后的页面为其他类型文件申请编号，这一步点击了申请编号按钮，弹出了右侧的模态框，可以填信息，申请编号"}]}