# 车型文件管理系统 - 项目结构

## 项目入口
- **main_gui_final.py** - 主程序入口文件（261行，重构后的简洁版本）
- **启动系统.bat** - 快速启动脚本

## 核心模块
- **GUI_Core/** - 核心GUI模块（已模块化重构）
  - `__init__.py` - 包初始化文件
  - `file_manager.py` - 文件管理器（54行精简版，车型设置和Excel处理）
  - `ui_components.py` - UI组件创建（320行，所有界面组件）
  - `file_operations.py` - 文件操作处理（245行，文件复制、分发、收集）
  - `process_manager.py` - 处理流程管理（215行，流程控制和配置）
  - `utility_functions.py` - 工具函数（295行，辅助功能和文件夹操作）

## 工具脚本
- **Scripts/** - 安装和启动脚本
  - `install.bat` - 依赖安装脚本
  - `run_gui.bat` - 启动脚本
  - `launcher.py` - Python启动器

## 应用模块
- **Apply/** - 申请相关功能
- **Fillin/** - 填写相关功能
- **Upload/** - 上传相关功能

## 数据和模板
- **Templates/** - 模板文件
- **Update_Templates/** - 模板更新文件
- **Vehicles/** - 车型数据
- **Fill_Template_Data.xlsx** - 模板填充数据

## 输出和临时文件
- **Final_Approval_Documents/** - 最终审批文档
- **Temp/** - 临时文件
- **logs/** - 系统日志

## 文档
- **Docs/** - 详细文档
  - `车型文件管理系统功能说明.md` - 主要功能说明
  - `系统重大改进总结.md` - 改进记录
  - `项目结构整理总结.md` - 结构整理
  - 各种问题修正总结
- **README.md** - 项目说明
- **快速启动指南.md** - 快速启动说明
- **代码清理总结_main_gui_final.md** - main_gui_final.py清理记录
- **代码清理总结_file_manager.md** - file_manager.py精简记录
- **代码重构总结.md** - 模块化重构完整记录

## 备份文件
- **Backup/** - 备份的旧版本文件
  - `Old_Main_Files/` - 旧的主文件
  - `Old_Core_Files/` - 旧的核心文件
  - 其他备份文件

## 配置文件
- **requirements.txt** - Python依赖列表

## 快速开始
1. 运行 `启动系统.bat` 或 `Scripts\install.bat` 安装依赖
2. 运行 `python main_gui_final.py` 启动系统

## 最新清理状态 (2025年7月11日)
- ✅ 移除了111行未使用的Worker类代码
- ✅ 清理了未使用的导入
- ✅ 修复了重复异常处理
- ✅ 实现了missing replace_templates方法
- ✅ 整理了文档结构
- ✅ Core文件夹只保留必要的file_manager.py
