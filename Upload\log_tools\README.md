# 日志管理工具集

这个文件夹包含了各种日志管理工具，供高级用户或特殊需求时使用。

## 主要功能
项目已在 `utils.py` 中集成了自动日志轮转功能，正常使用时无需使用这些额外工具。

## 工具列表

### 高级工具
- **`log_manager.py`** - 功能完整的日志管理器
- **`simple_log_cleanup.py`** - 简单的日志清理工具
- **`schedule_log_cleanup.py`** - 定时清理脚本

### 脚本工具
- **`cleanup_logs.bat`** - Windows批处理清理脚本
- **`start_with_log_cleanup.bat`** - 集成日志检查的启动脚本

### 说明文档
- **`日志管理说明.md`** - 详细使用说明

## 使用场景

这些工具适用于以下情况：
1. 需要手动控制日志清理
2. 需要定时自动清理（非程序运行时）
3. 需要批量清理历史日志
4. 需要自定义清理规则

## 推荐使用方式

**正常使用**: 直接使用主程序，`utils.py` 中的自动轮转功能会自动处理日志大小。

**特殊需求**: 从这个文件夹中选择合适的工具使用。

---
*注意：这些工具是可选的，主程序已包含自动日志管理功能。*
