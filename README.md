# 车型文件管理系统

## 快速开始

### 1. 安装依赖
```bash
cd Scripts
install.bat
```

### 2. 启动程序
```bash
cd Scripts  
run_gui.bat
```

### 3. 或直接运行
```bash
python main_gui_final.py
```

## 项目结构

```
车型文件管理系统/
├── main_gui_final.py          # 主程序入口
├── README.md                  # 项目说明
├── requirements.txt           # Python依赖列表
│
├── Core/                      # 核心功能模块
│   ├── file_manager.py        # 文件管理器
│   ├── selenium_automation.py # 自动化模块
│   └── fillin_filecode.py     # 文件填写模块
│
├── Scripts/                   # 脚本工具
│   ├── install.bat           # 依赖安装脚本
│   ├── run_gui.bat           # 启动脚本
│   └── launcher.py           # Python启动器
│
├── Config/                    # 配置文件
│   ├── 登录.json             # 登录配置
│   ├── 申请编号.json         # 申请编号配置
│   ├── 上传审批.json         # 上传审批配置
│   ├── 审批人员类别及填名字地方.json
│   └── 提取编号.json         # 编号提取配置
│
├── Templates/                 # 模板文件
│   ├── Fill_Template_Data.xlsx
│   ├── 数据管理员名单.png
│   └── [其他模板文件]
│
├── Vehicles/                  # 车型数据存储
├── Docs/                      # 文档目录
│   ├── 车型文件管理系统功能说明.md  # 详细功能说明
│   └── [其他技术文档]
│
├── Temp/                      # 临时和测试文件
│   └── [测试脚本和备份文件]
│
├── Fillin/                    # 填写处理目录
├── Final_Approval_Documents/  # 最终审批文档
├── Apply/                     # 申请相关文件
├── Upload/                    # 上传相关文件
├── Update_Templates/          # 模板更新目录
└── logs/                      # 日志文件
```

## 详细说明

请查看 `Docs/车型文件管理系统功能说明.md` 获取完整的功能描述和使用指南。

## 主要功能

1. **车型管理** - 车型文件夹创建和配置，自动打开数据管理员名单
2. **一体化文件处理** - 自动执行申请编号→填写内容→生成完整文件的完整流程
3. **智能模板管理** - 自动复制和重命名模板文件
4. **上传审批** - 自动化上传Final_Files中的文件并启动审批流程
5. **状态跟踪** - 完整的文件处理状态跟踪和结果展示
6. **安全管理** - 自动清理配置文件中的敏感信息

## 工作流程

### 文件处理流程
1. 选择文件类型和运行模式
2. 系统自动复制模板文件到Apply/input_files
3. 运行Apply模块申请文件编号
4. 将已编号文件分发到Fillin各子模块
5. 运行Fillin模块填写文件内容
6. 收集最终结果到车型的Numbered_and_Filled文件夹

### 上传审批流程
1. 将最终文件放入车型的Final_Files文件夹
2. 系统自动复制文件到Upload工作目录
3. 运行Upload模块执行上传和审批

## 依赖要求

- Python 3.7+
- PyQt5
- Selenium
- Pandas
- openpyxl

## 技术栈

- **界面框架**: PyQt5
- **网页自动化**: Selenium WebDriver
- **数据处理**: Pandas
- **文件操作**: openpyxl
- **配置管理**: JSON
