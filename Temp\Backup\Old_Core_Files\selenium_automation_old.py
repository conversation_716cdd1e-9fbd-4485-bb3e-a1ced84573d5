import json
import time
import logging
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class SeleniumAutomator:
    def __init__(self, silent_mode=False, test_mode=False):
        self.silent_mode = silent_mode
        self.test_mode = test_mode
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            if self.silent_mode:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            self.logger.info("Chrome驱动初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"初始化Chrome驱动失败: {str(e)}")
            return False
    
    def close_driver(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.wait = None
            self.logger.info("Chrome驱动已关闭")
    
    def execute_json_commands(self, json_file_path, variables=None):
        """执行JSON文件中的命令"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            commands = data.get('Commands', [])
            self.logger.info(f"开始执行 {json_file_path} 中的 {len(commands)} 个命令")
            
            for i, command in enumerate(commands):
                try:
                    self.execute_command(command, variables)
                    time.sleep(2)  # 等待2秒让网页反应
                except Exception as e:
                    self.logger.error(f"执行第 {i+1} 个命令失败: {str(e)}")
                    if not self.test_mode:
                        raise
            
            self.logger.info(f"完成执行 {json_file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"执行JSON命令失败: {str(e)}")
            return False    
    def upload_for_approval(self, file_info: Dict, file_path: str, approvers: Dict) -> bool:
        """上传文件进行审批
        
        Args:
            file_info: 文件信息字典，包含file_code等
            file_path: 要上传的文件路径
            approvers: 审批人信息字典，包含各类别审批人
            
        Returns:
            上传成功返回True，失败返回False
        """
        try:
            self.logger.info(f"开始上传文件进行审批: {file_info['file_code']}")
            
            # 确保在主页面
            if "/home" not in self.driver.current_url:
                self.driver.get(self.base_url)
                time.sleep(3)
            
            # 点击功能入口
            function_button = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span")
            ))
            function_button.click()
            time.sleep(2)
            
            # 点击文档创建功能
            doc_create = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='main']/div/div/div/div/div/div/span[contains(text(), '文档创建')]")
            ))
            doc_create.click()
            time.sleep(3)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, "form")
            ))
            
            # 填写文件编号
            file_code_field = self.wait.until(EC.element_to_be_clickable(
                (By.CSS_SELECTOR, "input[placeholder*='编号']")
            ))
            file_code_field.clear()
            file_code_field.send_keys(file_info['file_code'])
            time.sleep(1)
            
            # 填写文档名称
            if 'file_name' in file_info:
                doc_name_field = self.wait.until(EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, "input[placeholder*='文档名称']")
                ))
                doc_name_field.clear()
                doc_name_field.send_keys(file_info['file_name'])
            
            # 上传文件
            self._upload_file(file_path)
            
            # 设置审批人
            self._set_approvers(approvers)
            
            # 点击提交审批
            submit_button = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(@class, 'el-button--primary') and .//span[text()='提交审批']]")
            ))
            submit_button.click()
            
            # 等待提交完成
            time.sleep(3)
            
            # 处理确认弹窗
            try:
                confirm_button = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//button[contains(@class, 'el-button--primary') and .//span[text()='确定']]")
                ))
                confirm_button.click()
                time.sleep(2)
            except TimeoutException:
                pass
            
            self.logger.info("文件上传审批成功")
            return True
            
        except Exception as e:
            self.logger.error(f"上传文件审批失败: {str(e)}")
            return False
    
    def _upload_file(self, file_path: str):
        """上传文件"""
        try:
            # 查找文件上传元素
            file_input = self.wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, "input[type='file']")
            ))
            
            # 上传文件
            file_input.send_keys(os.path.abspath(file_path))
            time.sleep(2)
            
            # 等待上传完成
            self.wait.until(EC.presence_of_element_located(
                (By.XPATH, "//div[contains(@class, 'upload-success') or contains(text(), '上传成功')]")
            ))
            
            self.logger.info(f"文件上传成功: {file_path}")
            
        except Exception as e:
            self.logger.error(f"文件上传失败: {str(e)}")
            raise
    
    def _set_approvers(self, approvers: Dict):
        """设置审批人"""
        try:
            # 审批人类别映射
            approver_types = {
                "技术负责人": "technical_leader",
                "质量负责人": "quality_leader", 
                "项目经理": "project_manager",
                "部门负责人": "department_leader"
            }
            
            for approver_type, field_name in approver_types.items():
                if field_name in approvers and approvers[field_name]:
                    self._set_single_approver(approver_type, approvers[field_name])
            
            self.logger.info("审批人设置完成")
            
        except Exception as e:
            self.logger.error(f"设置审批人失败: {str(e)}")
            raise
    
    def _set_single_approver(self, approver_type: str, approver_name: str):
        """设置单个审批人"""
        try:
            # 查找对应类别的审批人输入框
            approver_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, f"//label[contains(text(), '{approver_type}')]/following-sibling::div//input")
            ))
            approver_field.clear()
            approver_field.send_keys(approver_name)
            time.sleep(1)
            
            # 选择下拉选项中的第一个匹配项
            try:
                first_option = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{approver_name}')]")
                ))
                first_option.click()
                time.sleep(1)
            except TimeoutException:
                # 如果没有下拉选项，直接输入
                pass
                
            self.logger.info(f"设置{approver_type}: {approver_name}")
            
        except Exception as e:
            self.logger.error(f"设置{approver_type}失败: {str(e)}")
            # 不抛出异常，继续设置其他审批人
                
        elif cmd_type == 'type':
            element = self.find_element(target)
            if element:
                element.clear()
                element.send_keys(value)
                
        elif cmd_type == 'select':
            # 处理下拉选择
            element = self.find_element(target)
            if element:
                element.click()
                time.sleep(1)
                # 寻找选项
                option_element = self.find_element(f"//option[@value='{value}']")
                if option_element:
                    option_element.click()
        
        elif cmd_type == 'wait':
            time.sleep(int(value) if value else 3)
        
        else:
            self.logger.warning(f"未知命令类型: {cmd_type}")
    
    def find_element(self, locator, timeout=10):
        """查找元素"""
        try:
            if locator.startswith('id='):
                element_id = locator[3:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.ID, element_id))
                )
            elif locator.startswith('xpath='):
                xpath = locator[6:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, xpath))
                )
            elif locator.startswith('css='):
                css_selector = locator[4:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, css_selector))
                )
            elif locator.startswith('name='):
                name = locator[5:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.NAME, name))
                )
            else:
                # 默认尝试xpath
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, locator))
                )
            
            return element
            
        except TimeoutException:
            self.logger.error(f"查找元素超时: {locator}")
            return None
        except Exception as e:
            self.logger.error(f"查找元素失败: {locator} - {str(e)}")
            return None
    
    def close_popups(self):
        """关闭弹窗"""
        try:
            # 尝试关闭常见的弹窗
            popup_selectors = [
                '#maxkb > div.maxkb-tips > div.maxkb-close > svg',
                '#notification_1 > div > i > svg',
                '.el-dialog__close',
                '.modal-close',
                '[aria-label="Close"]'
            ]
            
            for selector in popup_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        element.click()
                        time.sleep(1)
                        self.logger.info(f"关闭弹窗: {selector}")
                except:
                    continue
                    
        except Exception as e:
            self.logger.debug(f"关闭弹窗时出错: {str(e)}")


class ApplyIDAutomator(SeleniumAutomator):
    def __init__(self, silent_mode=False, test_mode=False):
        super().__init__(silent_mode, test_mode)
        self.base_path = Path(__file__).parent.parent  # 指向项目根目录
    
    def login(self, username, password):
        """登录系统"""
        try:
            login_json = self.base_path / "登录.json"
            variables = {
                'username': username,
                'password': password
            }
            
            success = self.execute_json_commands(login_json, variables)
            if success:
                time.sleep(3)
                self.close_popups()  # 登录后关闭弹窗
            
            return success
            
        except Exception as e:
            self.logger.error(f"登录失败: {str(e)}")
            return False
    
    def apply_file_id(self, file_info):
        """申请单个文件编号
        
        Args:
            file_info (dict): 文件信息
                - file_type: 文件类型 (DVP, PPL, FN)
                - project_code: 项目代号
                - file_name: 文件名
                - document_type: 文档类型
        """
        try:
            # 检查必需的字段
            required_fields = ['file_type', 'project_code', 'file_name', 'document_type']
            for field in required_fields:
                if field not in file_info:
                    raise ValueError(f"缺少必需字段: {field}")
            
            apply_json = self.base_path / "Config" / "申请编号.json"
            
            variables = {
                'project_code': file_info['project_code'],
                'file_name': file_info['file_name'],
                'document_type': file_info['document_type']
            }
            
            # 根据文件类型选择不同的文档类型
            if file_info['file_type'] == 'DVP':
                variables['document_type'] = 'DVP-系统设计验证计划'
            elif file_info['file_type'] == 'PPL':
                variables['document_type'] = 'PPL-车辆匹配计划'
            elif file_info['file_type'].startswith('FN'):
                variables['document_type'] = 'FN-接口定义/功能输入通知单'
            
            self.logger.info(f"申请编号参数: {variables}")
            
            success = self.execute_json_commands(apply_json, variables)
            
            if success and not self.test_mode:
                # 提取申请到的编号
                file_id = self.extract_file_id()
                return file_id
            else:
                return "TEST_ID_123456" if self.test_mode else None
                
        except Exception as e:
            self.logger.error(f"申请文件编号失败: {str(e)}")
            self.logger.error(f"文件信息: {file_info}")
            return None
    
    def extract_file_id(self):
        """提取申请到的文件编号"""
        try:
            extract_json = self.base_path / "Config" / "提取编号.json"
            success = self.execute_json_commands(extract_json)
            
            if success:
                # 从页面提取编号（需要根据实际页面结构调整）
                try:
                    # 这里需要根据实际页面的结构来提取编号
                    id_element = self.find_element("//span[contains(@class, 'file-id')]")
                    if id_element:
                        file_id = id_element.text.strip()
                        self.logger.info(f"提取到文件编号: {file_id}")
                        return file_id
                except:
                    pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取文件编号失败: {str(e)}")
            return None


class UploadApprovalAutomator(SeleniumAutomator):
    def __init__(self, silent_mode=False, test_mode=False):
        super().__init__(silent_mode, test_mode)
        self.base_path = Path(__file__).parent
    
    def login(self, username, password):
        """登录系统"""
        try:
            login_json = self.base_path / "登录.json"
            variables = {
                'username': username,
                'password': password
            }
            
            success = self.execute_json_commands(login_json, variables)
            if success:
                time.sleep(3)
                self.close_popups()
            
            return success
            
        except Exception as e:
            self.logger.error(f"登录失败: {str(e)}")
            return False
    
    def upload_file_for_approval(self, file_info, reviewers_info):
        """上传文件进行审批
        
        Args:
            file_info (dict): 文件信息
                - file_code: 文件编号
                - file_name: 完整文件名
                - file_type: 文件类型
                - source_file_path: 源文件路径
                - pdf_file_path: PDF文件路径
            reviewers_info (dict): 审批人员信息
                - data_managers: 数据管理员列表
                - section_chief: 科长邮箱
                - related_parties: 相关方邮箱列表
        """
        try:
            upload_json = self.base_path / "上传审批.json"
            
            variables = {
                'file_code': file_info['file_code'],
                'file_name': file_info['file_name'],
                'file_type': file_info['file_type'],
                'brief_content': file_info['file_name'].replace(file_info['file_code'] + '-', '')
            }
            
            # 执行上传流程
            success = self.execute_json_commands(upload_json, variables)
            
            if success:
                # 上传文件
                self.upload_files(file_info['source_file_path'], file_info['pdf_file_path'])
                
                if not self.test_mode:
                    # 点击保存并发起评审
                    self.click_save_and_review()
                    
                    # 填写审批人员
                    self.fill_reviewers(reviewers_info)
                    
                    # 提交审批
                    self.submit_approval()
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"上传文件审批失败: {str(e)}")
            return False
    
    def upload_files(self, source_file_path, pdf_file_path):
        """上传源文件和PDF文件"""
        try:
            # 上传源文件
            source_upload_element = self.find_element("//input[@type='file'][1]")
            if source_upload_element:
                source_upload_element.send_keys(str(source_file_path))
                time.sleep(2)
            
            # 上传PDF文件
            pdf_upload_element = self.find_element("//input[@type='file'][2]")
            if pdf_upload_element:
                pdf_upload_element.send_keys(str(pdf_file_path))
                time.sleep(2)
            
            self.logger.info("文件上传完成")
            
        except Exception as e:
            self.logger.error(f"上传文件失败: {str(e)}")
            raise
    
    def click_save_and_review(self):
        """点击保存并发起评审"""
        try:
            save_button = self.find_element("//button[contains(text(), '保存并发起评审')]")
            if save_button:
                save_button.click()
                time.sleep(3)
                self.logger.info("点击保存并发起评审")
            
        except Exception as e:
            self.logger.error(f"点击保存并发起评审失败: {str(e)}")
            raise
    
    def fill_reviewers(self, reviewers_info):
        """填写审批人员"""
        try:
            # 填写数据管理员
            for manager in reviewers_info.get('data_managers', []):
                self.add_reviewer('数据管理员', manager)
            
            # 填写科长
            if reviewers_info.get('section_chief'):
                self.add_reviewer('科长', reviewers_info['section_chief'])
            
            # 填写相关方
            for party in reviewers_info.get('related_parties', []):
                self.add_reviewer('相关方', party)
            
            self.logger.info("审批人员填写完成")
            
        except Exception as e:
            self.logger.error(f"填写审批人员失败: {str(e)}")
            raise
    
    def add_reviewer(self, role, email):
        """添加审批人员"""
        try:
            # 根据角色选择对应的区域
            if role == '数据管理员':
                role_button = self.find_element("//button[contains(text(), '数据管理员')]")
            elif role == '科长':
                role_button = self.find_element("//button[contains(text(), '科长')]")
            elif role == '相关方':
                role_button = self.find_element("//button[contains(text(), '相关方')]")
            else:
                return
            
            if role_button:
                role_button.click()
                time.sleep(1)
                
                # 在搜索框中输入邮箱
                search_input = self.find_element("//input[@placeholder='请输入邮箱搜索']")
                if search_input:
                    search_input.clear()
                    search_input.send_keys(email)
                    time.sleep(2)
                    
                    # 选择搜索结果
                    result_item = self.find_element("//li[contains(@class, 'search-result')]")
                    if result_item:
                        result_item.click()
                        time.sleep(1)
                        
                        self.logger.info(f"添加审批人员: {role} - {email}")
            
        except Exception as e:
            self.logger.error(f"添加审批人员失败: {role} - {email} - {str(e)}")
    
    def submit_approval(self):
        """提交审批"""
        try:
            submit_button = self.find_element("//button[contains(text(), '提交')]")
            if submit_button:
                submit_button.click()
                time.sleep(3)
                self.logger.info("提交审批完成")
            
        except Exception as e:
            self.logger.error(f"提交审批失败: {str(e)}")
            raise
