"""
第一个页面处理模块
处理文档信息填写、文件上传等功能
"""

import time
import logging
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)

class FirstPageHandler:
    """第一个页面处理器"""
    
    def __init__(self, driver, wait, config):
        self.driver = driver
        self.wait = wait
        self.config = config
        
    def fill_document_info(self, doc_id, source_file, file_type, excel_file=None):
        """填写文档信息（第一个页面）"""
        try:
            logger.info(f"📝 开始填写文档信息: {doc_id}")
            time.sleep(self.config.get('OPERATION_DELAY', 3))
            
            # 1. 填写文档编号
            if not self._fill_document_number(doc_id):
                return False
            
            # 2. 等待几秒让页面加载更多项
            logger.info("⏳ 等待页面加载更多项...")
            time.sleep(5)
            
            # 3. 滚动到底部
            self._scroll_to_bottom()
            
            # 4. 填写所属项目阶段
            if not self._fill_project_stage():
                return False
            
            # 5. 填写交付物级别
            if not self._fill_delivery_level():
                return False
            
            # 6. 填写文件内容简要
            if not self._fill_content_summary(source_file['name']):
                return False
            
            # 7. 上传文件
            if not self._upload_files(source_file, excel_file):
                return False
            
            # 8. 保存并发起评审
            if not self._save_and_start_review():
                return False
            
            logger.info(f"✅ 第一页信息填写完成: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 第一页处理失败: {str(e)}")
            return False
    
    def _scroll_to_bottom(self):
        """滚动到页面底部"""
        try:
            logger.info("📜 滚动到页面底部...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            logger.info("✅ 已滚动到底部")
        except Exception as e:
            logger.warning(f"⚠️ 滚动失败: {str(e)}")
    
    def _fill_document_number(self, doc_id):
        """填写文档编号"""
        try:
            logger.info("📝 填写文档编号...")
            
            # 文档编号选择器
            doc_number_selectors = [
                "//div[2]/div/div/div/div/input",
                "//input[contains(@id, 'el-id-') and contains(@placeholder, '选择')]",
                "//div[contains(@class, 'el-select')]//input"
            ]
            
            for i, selector in enumerate(doc_number_selectors):
                try:
                    logger.info(f"尝试文档编号选择器 {i+1}/{len(doc_number_selectors)}")
                    doc_number_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    doc_number_field.click()
                    time.sleep(1)
                    doc_number_field.clear()
                    doc_number_field.send_keys(doc_id)
                    logger.info(f"✅ 已输入文档编号: {doc_id}")
                    time.sleep(2)
                    
                    # 尝试点击下拉选项
                    dropdown_selectors = [
                        f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{doc_id}')]",
                        f"//div[contains(@class, 'el-select-dropdown')]//span[contains(text(), '{doc_id}')]",
                        f"//ul//li//span[contains(text(), '{doc_id}')]"
                    ]
                    
                    dropdown_clicked = False
                    for dropdown_selector in dropdown_selectors:
                        try:
                            dropdown_item = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, dropdown_selector)
                            ))
                            dropdown_item.click()
                            logger.info("✅ 成功点击文档编号下拉选项")
                            dropdown_clicked = True
                            break
                        except:
                            continue
                    
                    if not dropdown_clicked:
                        doc_number_field.send_keys(Keys.ENTER)
                        time.sleep(1)
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"文档编号选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文档编号填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写文档编号异常: {str(e)}")
            return False
    
    def _fill_project_stage(self):
        """填写所属项目阶段"""
        try:
            logger.info("📝 填写所属项目阶段...")
            
            # 项目阶段输入框选择器
            stage_selectors = [
                "//input[contains(@id, 'el-id-') and contains(@id, '1627')]",
                "//label[contains(text(), '阶段')]/following-sibling::div//input",
                "//div[contains(text(), '所属项目阶段')]/following-sibling::div//input",
                "//div[5]/div/div[2]/div/div/div/div/input"
            ]
            
            stage_value = "B版"
            
            for i, selector in enumerate(stage_selectors):
                try:
                    stage_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    stage_field.click()
                    time.sleep(1)
                    stage_field.clear()
                    stage_field.send_keys(stage_value)
                    time.sleep(2)
                    
                    # 尝试选择下拉选项 - B版
                    stage_option_selectors = [
                        f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{stage_value}')]",
                        f"//span[contains(text(), '{stage_value}')]",
                        "//span[contains(text(), '整车项目阶段-产品详细设计阶段-B版')]"
                    ]
                    
                    option_clicked = False
                    for option_selector in stage_option_selectors:
                        try:
                            stage_option = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, option_selector)
                            ))
                            stage_option.click()
                            logger.info(f"✅ 已选择项目阶段: {stage_value}")
                            option_clicked = True
                            break
                        except:
                            continue
                    
                    if not option_clicked:
                        stage_field.send_keys(Keys.ENTER)
                        logger.info(f"✅ 通过回车确认项目阶段: {stage_value}")
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"项目阶段选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 项目阶段填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写项目阶段异常: {str(e)}")
            return False
    
    def _fill_delivery_level(self):
        """填写交付物级别"""
        try:
            logger.info("📝 填写交付物级别...")
            
            # 交付物级别下拉按钮选择器
            dropdown_selectors = [
                "//div[contains(text(), '交付物级别')]/following-sibling::div//i[contains(@class, 'el-select__caret')]",
                "//div[5]/div[2]/div[2]/div/div/div[2]/i/svg",
                "//div[contains(@class, 'el-select__suffix')]//i[contains(@class, 'el-select__caret')]"
            ]
            
            # 先点击下拉按钮
            for i, selector in enumerate(dropdown_selectors):
                try:
                    dropdown_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    dropdown_btn.click()
                    logger.info("✅ 成功点击交付物级别下拉按钮")
                    time.sleep(1)
                    break
                except Exception as e:
                    logger.warning(f"交付物级别下拉按钮选择器 {i+1} 失败: {str(e)}")
                    continue
            else:
                logger.error("❌ 无法点击交付物级别下拉按钮")
                return False
            
            # 选择"项目级"
            level_selectors = [
                "//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '项目级')]",
                "//span[contains(text(), '项目级')]",
                "//li[contains(@id, 'el-id-') and contains(text(), '项目级')]"
            ]
            
            for selector in level_selectors:
                try:
                    level_option = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    level_option.click()
                    logger.info("✅ 已选择交付物级别: 项目级")
                    return True
                except:
                    continue
            
            logger.error("❌ 无法选择项目级选项")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写交付物级别异常: {str(e)}")
            return False
    
    def _fill_content_summary(self, filename):
        """填写文件内容简要"""
        try:
            logger.info("📝 填写文件内容简要...")
            
            # 从文件名中提取文档名称（去掉扩展名）
            doc_name = Path(filename).stem
            
            summary_selectors = [
                "//textarea[contains(@id, 'el-id-') and contains(@placeholder, '请输入内容')]",
                "//label[contains(text(), '简要')]/following-sibling::div//textarea",
                "//div[contains(text(), '文件内容简要')]/following-sibling::div//textarea",
                "//textarea[contains(@maxlength, '1000')]"
            ]
            
            for i, selector in enumerate(summary_selectors):
                try:
                    summary_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    summary_field.click()
                    time.sleep(1)
                    summary_field.clear()
                    summary_field.send_keys(doc_name)
                    logger.info(f"✅ 已输入文件内容简要: {doc_name}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"内容简要选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文件内容简要填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写文件内容简要异常: {str(e)}")
            return False
    
    def _upload_files(self, source_file, excel_file=None):
        """上传文件"""
        try:
            logger.info("📁 开始上传文件...")
            
            # 1. 上传源文件
            if not self._upload_source_file(source_file):
                return False
            
            # 2. 删除自动转换的PDF（如果存在）
            self._delete_auto_converted_pdf()
            
            # 3. 上传PDF文件（如果有对应的PDF）
            if excel_file and excel_file['path'].suffix.lower() == '.pdf':
                if not self._upload_pdf_file(excel_file):
                    return False
            
            logger.info("✅ 文件上传完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 上传文件异常: {str(e)}")
            return False
    
    def _upload_source_file(self, source_file):
        """上传源文件"""
        try:
            logger.info("📄 上传源文件...")
            
            # 源文件上传选择器
            upload_selectors = [
                "//input[@type='file' and @name='file']",
                "//div[contains(@class, 'el-upload')]//input[@type='file']",
                "//input[contains(@accept, '')][@type='file']"
            ]
            
            for i, selector in enumerate(upload_selectors):
                try:
                    upload_input = self.driver.find_element(By.XPATH, selector)
                    # 检查是否是源文件上传区域（第一个上传区域）
                    if i == 0 or "源文件" in upload_input.get_attribute('outerHTML'):
                        upload_input.send_keys(str(source_file['path']))
                        logger.info(f"✅ 已上传源文件: {source_file['name']}")
                        time.sleep(3)  # 等待文件上传完成
                        return True
                except Exception as e:
                    logger.warning(f"源文件上传选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 源文件上传失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 上传源文件异常: {str(e)}")
            return False
    
    def _delete_auto_converted_pdf(self):
        """删除自动转换的PDF文件"""
        try:
            logger.info("🗑️ 尝试删除自动转换的PDF...")
            
            # 查找删除按钮
            delete_selectors = [
                "//button[contains(@class, 'el-button') and contains(.//svg, 'path')]//span//i//svg//path",
                "//div[contains(@class, 'file-item')]//button[contains(.//svg, 'delete')]",
                "//button[contains(@title, '删除') or contains(@aria-label, '删除')]"
            ]
            
            for selector in delete_selectors:
                try:
                    delete_btn = self.driver.find_element(By.XPATH, selector)
                    delete_btn.click()
                    logger.info("✅ 已删除自动转换的PDF")
                    time.sleep(1)
                    break
                except:
                    continue
            else:
                logger.info("ℹ️ 未找到需要删除的自动转换PDF")
                
        except Exception as e:
            logger.warning(f"⚠️ 删除自动转换PDF失败: {str(e)}")
    
    def _upload_pdf_file(self, pdf_file):
        """上传PDF文件"""
        try:
            logger.info("📋 上传PDF文件...")
            
            # PDF文件上传选择器（第二个上传区域）
            pdf_upload_selectors = [
                "//div[contains(@class, 'el-form-item') and contains(.//div, 'PDF')]//input[@type='file']",
                "(//input[@type='file'])[2]",  # 第二个文件上传输入框
                "//div[contains(text(), 'PDF')]//following-sibling::div//input[@type='file']"
            ]
            
            for i, selector in enumerate(pdf_upload_selectors):
                try:
                    pdf_upload_input = self.driver.find_element(By.XPATH, selector)
                    pdf_upload_input.send_keys(str(pdf_file['path']))
                    logger.info(f"✅ 已上传PDF文件: {pdf_file['name']}")
                    time.sleep(3)  # 等待文件上传完成
                    return True
                except Exception as e:
                    logger.warning(f"PDF上传选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ PDF文件上传失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 上传PDF文件异常: {str(e)}")
            return False
    
    def _save_and_start_review(self):
        """保存并发起评审"""
        try:
            logger.info("💾 保存并发起评审...")
            
            # 保存并发起评审按钮选择器
            save_selectors = [
                "//button[contains(text(), '保存并发起评审')]",
                "//span[contains(text(), '保存并发起评审')]/parent::button",
                "//button[contains(@class, 'el-button') and contains(.//span, '保存并发起评审')]",
                "//div[contains(@class, 'cus-footer')]//button[contains(.//span, '保存并发起评审')]"
            ]
            
            for i, selector in enumerate(save_selectors):
                try:
                    save_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    save_btn.click()
                    logger.info("✅ 已点击保存并发起评审按钮")
                    time.sleep(3)  # 等待页面跳转
                    return True
                    
                except Exception as e:
                    logger.warning(f"保存按钮选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 保存并发起评审失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 保存并发起评审异常: {str(e)}")
            return False
