# 上传审批自动化程序

## 程序结构

程序已经按功能模块化拆分为以下文件：

### 主要文件
- `main_controller.py` - 主控制器，协调所有模块执行完整流程
- `browser_manager.py` - 浏览器管理，处理浏览器启动、登录、弹窗关闭
- `first_page_handler.py` - 第一个页面处理，文档信息填写和文件上传
- `second_page_handler.py` - 第二个页面处理，评审人选择和提交
- `utils.py` - 通用工具模块，文件处理、数据管理、**自动日志轮转**
- `config.py` - 配置文件
- `run.py` - 启动脚本
- `start.bat` - 一键启动脚本

### 数据文件
- `Data/Fill_Template_Data.xlsx` - 角色映射数据
- `Final_Approval_Documents/` - 待处理文档目录
- `upload_approval.log` - 运行日志（自动管理大小）

### 工具文件夹
- `log_tools/` - 可选的日志管理工具集
- `backup/` - 旧版本和测试文件
- `archive/` - 归档的配置和文档文件

## 日志管理

程序已集成**自动日志轮转功能**：
- 📁 **自动检查**: 每次启动时检查日志文件大小
- 🔄 **自动轮转**: 超过5MB时自动轮转并备份
- 🗂️ **智能清理**: 自动保留最近3个备份文件
- ⚡ **无需干预**: 完全自动化，无需手动管理

如需特殊日志管理需求，可使用 `log_tools/` 中的工具。

### 配置文件
请在 `config.py` 中配置以下信息：
```python
USERNAME = '你的用户名'        # 必填
PASSWORD = '你的密码'          # 必填
HEADLESS_MODE = False         # True: 静默模式（后台运行），False: 显示浏览器窗口
```

### 使用方法

1. **配置设置**：
   - 修改 `config.py` 中的用户名和密码
   - 设置静默模式（可选，建议熟悉操作后开启）
   - 根据需要调整其他配置参数

2. **准备文件**：
   - 将要处理的文档放入 `Final_Approval_Documents` 文件夹
   - 支持的文件格式：`.docx`, `.xlsx`, `.pdf`, `.doc`, `.xls`
   - 文件名必须包含标准编号格式，如：`HYHB_FN_A19-000011`

3. **运行程序**：
   ```bash
   # 方式1: 双击启动
   start.bat
   
   # 方式2: 命令行启动
   python run.py
   
   # 方式3: 直接运行主控制器
   python main_controller.py
   ```

## 主要改进

1. **模块化结构**：
   - 每个功能模块独立，便于维护和扩展
   - 清晰的职责分离

2. **自动日志管理**：
   - 集成自动日志轮转，防止日志文件过大
   - 智能备份和清理机制

3. **错误处理**：
   - 完善的异常处理机制
   - 详细的日志记录

4. **配置管理**：
   - 集中的配置管理
   - 支持测试模式和正式模式

5. **文件处理**：
   - 优化的文档编号提取逻辑
   - 支持多种文件格式
   - 自动文件配对（Word+Excel）

6. **元素定位**：
   - 保留多种元素定位方式
   - 提高页面变化的适应性
- 详细的日志记录

## 环境要求

- Python 3.7+
- Chrome浏览器
- ChromeDriver

## 安装步骤

1. 安装Python依赖包：
```bash
pip install -r requirements.txt
```

2. 下载ChromeDriver：
   - 访问 https://chromedriver.chromium.org/
   - 下载与您的Chrome浏览器版本匹配的ChromeDriver
   - 将chromedriver.exe放在程序同一目录下

## 配置说明

1. 编辑 `config.py` 文件：
   - 填写您的用户名和密码（可选，程序会提示手动登录）
   - 设置静默模式（建议熟悉操作后开启 HEADLESS_MODE = True）

2. 准备数据文件：
   - 将Excel数据文件放在 `Data` 文件夹中
   - Excel文件包含角色、姓名、邮箱等信息

### 静默模式说明

- **HEADLESS_MODE = False** (默认): 显示浏览器窗口，可以看到操作过程
- **HEADLESS_MODE = True**: 静默模式，程序在后台运行，不显示浏览器窗口
  - 优点：用户可以在前台做其他工作，不影响程序运行
  - 适用：熟悉程序操作后的日常使用

### Excel数据文件格式

Excel文件应包含以下列：
- **角色**: 中文角色名称
- **role**: 英文角色标识（用于程序识别）
- **people**: 人员姓名（支持多种分隔符混合使用：逗号、空格、分号等，如"张三，李四 王五；赵六"）
- **mail**: 第一个人员的邮箱
- **注：**: 第二个人员的邮箱（如有更多人员，继续向右扩展列）

**多人员处理方式：**
- people列：支持常用分隔符混合使用，如中文逗号(，)、英文逗号(,)、空格、中文分号(；)、英文分号(;)等
- 邮箱：按people列中人员顺序，从mail列开始横向扩展填写
- 数据管理员可以填工号而不是邮箱

3. 准备文档文件：
   - 将要上传的文档放在 `Final_Approval_Documents` 文件夹中
   - 每个文档需要有源文件和对应的PDF文件
   - 文件名必须以文档编号开头，如：HYHB_FN_A19-000011-XXXXXX.docx

## 文件命名规范

文档编号格式：
- DVP类型：如 HYHB_DVP_A19-000001
- PPL类型：如 HC2_PPL_A19-000001  
- FN类型：如 SY_FN_A19-000056

文件对示例：
```
Final_Approval_Documents/
├── HYHB_FN_A19-000011-接口定义通知单.docx
├── HYHB_FN_A19-000011-接口定义通知单.pdf
├── HC2_PPL_A19-000001-匹配计划.xlsx
└── HC2_PPL_A19-000001-匹配计划.pdf
```

## 使用方法

1. 首次运行（窗口模式）：
```bash
python run.py
```

2. 日常运行（可选静默模式）：
   - 将 `config.py` 中的 `HEADLESS_MODE` 设为 `True`
   - 再次运行程序，程序将在后台执行

## 程序流程

1. 启动浏览器并登录
2. 关闭弹窗
3. 扫描文档文件夹
4. 对每个文档执行：
   - 点击文档创建
   - 填写文档信息（编号、阶段、级别、内容简要）
   - 上传源文件和PDF文件
   - 保存并发起评审
   - 选择评审人方案
   - 填写评审人
   - 设置截止日期
   - 提交评审（测试模式下跳过）
   - 返回主页面

## 注意事项

1. **测试模式**：首次运行请保持测试模式开启，避免误提交
2. **网络环境**：确保能正常访问DMS系统
3. **文件格式**：支持 .docx, .xlsx, .ppt, .pdf 等格式
4. **数据准确性**：请确保Excel中的人员信息准确无误
5. **浏览器版本**：ChromeDriver版本需与Chrome浏览器匹配

## 故障排除

1. **ChromeDriver错误**：
   - 检查ChromeDriver版本是否与Chrome匹配
   - 确认ChromeDriver在PATH中或程序目录下

2. **登录失败**：
   - 检查用户名密码是否正确
   - 可以选择手动登录模式

3. **元素定位失败**：
   - 网页结构可能发生变化
   - 检查日志文件了解详细错误信息

4. **文件上传失败**：
   - 检查文件路径是否正确
   - 确认文件格式被系统支持

## 日志文件

程序运行时会生成 `upload_approval.log` 文件，记录详细的操作过程和错误信息。

## 联系支持

如遇问题，请查看日志文件并联系技术支持。
