@echo off
echo 上传审批自动化程序启动器
echo ===========================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未安装Python或Python不在系统PATH中
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

:: 检查requirements.txt是否存在
if not exist requirements.txt (
    echo 错误：未找到requirements.txt文件
    pause
    exit /b 1
)

:: 安装依赖包
echo 正在检查并安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 错误：依赖包安装失败
    pause
    exit /b 1
)

:: 检查配置文件
if not exist config.py (
    echo 错误：未找到config.py配置文件
    pause
    exit /b 1
)

:: 检查数据文件夹
if not exist Data (
    echo 创建Data文件夹...
    mkdir Data
)

:: 检查文档文件夹
if not exist Final_Approval_Documents (
    echo 创建Final_Approval_Documents文件夹...
    mkdir Final_Approval_Documents
)

:: 检查是否有数据文件
if not exist "Data\*.xlsx" (
    echo 警告：Data文件夹中未找到Excel数据文件
    echo 正在创建示例数据文件...
    python create_sample_data.py
    echo 请编辑 Data\Fill_Template_Data.xlsx 文件中的人员信息
    echo.
)

:: 检查是否有文档文件
if not exist "Final_Approval_Documents\*" (
    echo 警告：Final_Approval_Documents文件夹为空
    echo 请将要上传审批的文档文件放入该文件夹
    echo 每个文档需要有源文件和对应的PDF文件
    echo.
)

echo 环境检查完成！
echo.
echo 使用说明：
echo 1. 请确保已配置config.py中的用户名密码（可选）
echo 2. 请将要处理的文档放入Final_Approval_Documents文件夹
echo 3. 请编辑Data文件夹中的Excel文件，填入正确的人员信息
echo 4. 可在config.py中开启静默模式（HEADLESS_MODE = True）实现后台运行
echo.

set /p choice="是否现在运行上传审批程序？(y/n): "
if /i "%choice%"=="y" (
    echo 启动程序...
    python run.py
    echo.
    echo 程序已完成执行
) else (
    echo 程序已准备就绪，您可以稍后运行：
    echo python run.py
)

echo.
echo 脚本执行完成，窗口将自动关闭
timeout /t 3 /nobreak >nul
