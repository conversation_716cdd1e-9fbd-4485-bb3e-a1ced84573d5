import json
import time
import logging
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class SeleniumAutomator:
    def __init__(self, silent_mode=False, test_mode=False):
        self.silent_mode = silent_mode
        self.test_mode = test_mode
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            if self.silent_mode:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            self.logger.info("Chrome驱动初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"初始化Chrome驱动失败: {str(e)}")
            return False
    
    def close_driver(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.wait = None
            self.logger.info("Chrome驱动已关闭")
    
    def execute_json_commands(self, json_file_path, variables=None):
        """执行JSON文件中的命令"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            commands = data.get('Commands', [])
            self.logger.info(f"开始执行 {json_file_path} 中的 {len(commands)} 个命令")
            
            for i, command in enumerate(commands):
                try:
                    self.execute_command(command, variables)
                    time.sleep(2)  # 等待2秒让网页反应
                except Exception as e:
                    self.logger.error(f"执行第 {i+1} 个命令失败: {str(e)}")
                    if not self.test_mode:
                        raise
            
            self.logger.info(f"完成执行 {json_file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"执行JSON命令失败: {str(e)}")
            return False
    
    def execute_command(self, command, variables=None):
        """执行单个命令"""
        cmd_type = command.get('Command')
        target = command.get('Target', '')
        value = command.get('Value', '')
        
        # 替换变量
        if variables:
            for var_name, var_value in variables.items():
                target = target.replace(f"{{{var_name}}}", str(var_value))
                value = value.replace(f"{{{var_name}}}", str(var_value))
        
        self.logger.debug(f"执行命令: {cmd_type} - {target} - {value}")
        
        if cmd_type == 'open':
            self.driver.get(target)
            
        elif cmd_type == 'click':
            element = self.find_element(target)
            if element:
                element.click()
                
        elif cmd_type == 'clickAndWait':
            element = self.find_element(target)
            if element:
                element.click()
                time.sleep(3)  # 额外等待时间
                
        elif cmd_type == 'type':
            element = self.find_element(target)
            if element:
                element.clear()
                element.send_keys(value)
                
        elif cmd_type == 'select':
            # 处理下拉选择
            element = self.find_element(target)
            if element:
                element.click()
                time.sleep(1)
                # 寻找选项
                option_element = self.find_element(f"//option[@value='{value}']")
                if option_element:
                    option_element.click()
        
        elif cmd_type == 'wait':
            time.sleep(int(value) if value else 3)
        
        else:
            self.logger.warning(f"未知命令类型: {cmd_type}")
    
    def find_element(self, locator, timeout=10):
        """查找元素"""
        try:
            if locator.startswith('id='):
                element_id = locator[3:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.ID, element_id))
                )
            elif locator.startswith('xpath='):
                xpath = locator[6:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, xpath))
                )
            elif locator.startswith('css='):
                css_selector = locator[4:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, css_selector))
                )
            elif locator.startswith('name='):
                name = locator[5:]
                element = self.wait.until(
                    EC.presence_of_element_located((By.NAME, name))
                )
            else:
                # 默认尝试xpath
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, locator))
                )
            
            return element
            
        except TimeoutException:
            self.logger.error(f"查找元素超时: {locator}")
            return None
        except Exception as e:
            self.logger.error(f"查找元素失败: {locator} - {str(e)}")
            return None
    
    def close_popups(self):
        """关闭弹窗"""
        try:
            # 尝试关闭常见的弹窗
            popup_selectors = [
                '#maxkb > div.maxkb-tips > div.maxkb-close > svg',
                '#notification_1 > div > i > svg',
                '.el-dialog__close',
                '.modal-close',
                '[aria-label="Close"]'
            ]
            
            for selector in popup_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        element.click()
                        time.sleep(1)
                        self.logger.info(f"关闭弹窗: {selector}")
                except:
                    continue
                    
        except Exception as e:
            self.logger.debug(f"关闭弹窗时出错: {str(e)}")


class ApplyIDAutomator(SeleniumAutomator):
    def __init__(self, silent_mode=False, test_mode=False):
        super().__init__(silent_mode, test_mode)
        self.base_path = Path(__file__).parent
    
    def login(self, username, password):
        """登录系统"""
        try:
            login_json = self.base_path / "登录.json"
            variables = {
                'username': username,
                'password': password
            }
            
            success = self.execute_json_commands(login_json, variables)
            if success:
                time.sleep(3)
                self.close_popups()  # 登录后关闭弹窗
            
            return success
            
        except Exception as e:
            self.logger.error(f"登录失败: {str(e)}")
            return False
    
    def apply_file_id(self, file_info):
        """申请单个文件编号
        
        Args:
            file_info (dict): 文件信息
                - file_type: 文件类型 (DVP, PPL, FN)
                - project_code: 项目代号
                - file_name: 文件名
                - document_type: 文档类型
        """
        try:
            apply_json = self.base_path / "申请编号.json"
            
            variables = {
                'project_code': file_info['project_code'],
                'file_name': file_info['file_name'],
                'document_type': file_info['document_type']
            }
            
            # 根据文件类型选择不同的文档类型
            if file_info['file_type'] == 'DVP':
                variables['document_type'] = 'DVP-系统设计验证计划'
            elif file_info['file_type'] == 'PPL':
                variables['document_type'] = 'PPL-车辆匹配计划'
            elif file_info['file_type'].startswith('FN'):
                variables['document_type'] = 'FN-接口定义/功能输入通知单'
            
            success = self.execute_json_commands(apply_json, variables)
            
            if success and not self.test_mode:
                # 提取申请到的编号
                file_id = self.extract_file_id()
                return file_id
            else:
                return "TEST_ID_123456" if self.test_mode else None
                
        except Exception as e:
            self.logger.error(f"申请文件编号失败: {str(e)}")
            return None
    
    def extract_file_id(self):
        """提取申请到的文件编号"""
        try:
            extract_json = self.base_path / "提取编号.json"
            success = self.execute_json_commands(extract_json)
            
            if success:
                # 从页面提取编号（需要根据实际页面结构调整）
                try:
                    # 这里需要根据实际页面的结构来提取编号
                    id_element = self.find_element("//span[contains(@class, 'file-id')]")
                    if id_element:
                        file_id = id_element.text.strip()
                        self.logger.info(f"提取到文件编号: {file_id}")
                        return file_id
                except:
                    pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取文件编号失败: {str(e)}")
            return None


class UploadApprovalAutomator(SeleniumAutomator):
    def __init__(self, silent_mode=False, test_mode=False):
        super().__init__(silent_mode, test_mode)
        self.base_path = Path(__file__).parent
    
    def login(self, username, password):
        """登录系统"""
        try:
            login_json = self.base_path / "登录.json"
            variables = {
                'username': username,
                'password': password
            }
            
            success = self.execute_json_commands(login_json, variables)
            if success:
                time.sleep(3)
                self.close_popups()
            
            return success
            
        except Exception as e:
            self.logger.error(f"登录失败: {str(e)}")
            return False
    
    def upload_file_for_approval(self, file_info, reviewers_info):
        """上传文件进行审批
        
        Args:
            file_info (dict): 文件信息
                - file_code: 文件编号
                - file_name: 完整文件名
                - file_type: 文件类型
                - source_file_path: 源文件路径
                - pdf_file_path: PDF文件路径
            reviewers_info (dict): 审批人员信息
                - data_managers: 数据管理员列表
                - section_chief: 科长邮箱
                - related_parties: 相关方邮箱列表
        """
        try:
            upload_json = self.base_path / "上传审批.json"
            
            variables = {
                'file_code': file_info['file_code'],
                'file_name': file_info['file_name'],
                'file_type': file_info['file_type'],
                'brief_content': file_info['file_name'].replace(file_info['file_code'] + '-', '')
            }
            
            # 执行上传流程
            success = self.execute_json_commands(upload_json, variables)
            
            if success:
                # 上传文件
                self.upload_files(file_info['source_file_path'], file_info['pdf_file_path'])
                
                if not self.test_mode:
                    # 点击保存并发起评审
                    self.click_save_and_review()
                    
                    # 填写审批人员
                    self.fill_reviewers(reviewers_info)
                    
                    # 提交审批
                    self.submit_approval()
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"上传文件审批失败: {str(e)}")
            return False
    
    def upload_files(self, source_file_path, pdf_file_path):
        """上传源文件和PDF文件"""
        try:
            # 上传源文件
            source_upload_element = self.find_element("//input[@type='file'][1]")
            if source_upload_element:
                source_upload_element.send_keys(str(source_file_path))
                time.sleep(2)
            
            # 上传PDF文件
            pdf_upload_element = self.find_element("//input[@type='file'][2]")
            if pdf_upload_element:
                pdf_upload_element.send_keys(str(pdf_file_path))
                time.sleep(2)
            
            self.logger.info("文件上传完成")
            
        except Exception as e:
            self.logger.error(f"上传文件失败: {str(e)}")
            raise
    
    def click_save_and_review(self):
        """点击保存并发起评审"""
        try:
            save_button = self.find_element("//button[contains(text(), '保存并发起评审')]")
            if save_button:
                save_button.click()
                time.sleep(3)
                self.logger.info("点击保存并发起评审")
            
        except Exception as e:
            self.logger.error(f"点击保存并发起评审失败: {str(e)}")
            raise
    
    def fill_reviewers(self, reviewers_info):
        """填写审批人员"""
        try:
            # 填写数据管理员
            for manager in reviewers_info.get('data_managers', []):
                self.add_reviewer('数据管理员', manager)
            
            # 填写科长
            if reviewers_info.get('section_chief'):
                self.add_reviewer('科长', reviewers_info['section_chief'])
            
            # 填写相关方
            for party in reviewers_info.get('related_parties', []):
                self.add_reviewer('相关方', party)
            
            self.logger.info("审批人员填写完成")
            
        except Exception as e:
            self.logger.error(f"填写审批人员失败: {str(e)}")
            raise
    
    def add_reviewer(self, role, email):
        """添加审批人员"""
        try:
            # 根据角色选择对应的区域
            if role == '数据管理员':
                role_button = self.find_element("//button[contains(text(), '数据管理员')]")
            elif role == '科长':
                role_button = self.find_element("//button[contains(text(), '科长')]")
            elif role == '相关方':
                role_button = self.find_element("//button[contains(text(), '相关方')]")
            else:
                return
            
            if role_button:
                role_button.click()
                time.sleep(1)
                
                # 在搜索框中输入邮箱
                search_input = self.find_element("//input[@placeholder='请输入邮箱搜索']")
                if search_input:
                    search_input.clear()
                    search_input.send_keys(email)
                    time.sleep(2)
                    
                    # 选择搜索结果
                    result_item = self.find_element("//li[contains(@class, 'search-result')]")
                    if result_item:
                        result_item.click()
                        time.sleep(1)
                        
                        self.logger.info(f"添加审批人员: {role} - {email}")
            
        except Exception as e:
            self.logger.error(f"添加审批人员失败: {role} - {email} - {str(e)}")
    
    def submit_approval(self):
        """提交审批"""
        try:
            submit_button = self.find_element("//button[contains(text(), '提交')]")
            if submit_button:
                submit_button.click()
                time.sleep(3)
                self.logger.info("提交审批完成")
            
        except Exception as e:
            self.logger.error(f"提交审批失败: {str(e)}")
            raise
