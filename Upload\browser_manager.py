"""
浏览器管理模块
处理浏览器启动、登录、弹窗关闭等基础功能
"""

import os
import time
import logging
import shutil
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)

class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, config):
        self.config = config
        self.driver = None
        self.wait = None
        
    def setup_browser(self):
        """设置并启动浏览器"""
        try:
            chrome_options = Options()
            
            # 静默模式配置
            if self.config.get('HEADLESS_MODE', False):
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                logger.info("启用静默模式（无头模式）")
            else:
                chrome_options.add_argument("--start-maximized")
                logger.info("启用窗口模式")
            
            # 通用配置
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 禁用保存密码提示
            prefs = {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 查找ChromeDriver
            chromedriver_path = self._find_chromedriver()
            
            if chromedriver_path:
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info(f"使用ChromeDriver: {chromedriver_path}")
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
                logger.info("使用默认ChromeDriver")
            
            # 防止被检测为自动化
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置等待器
            self.wait = WebDriverWait(self.driver, self.config.get('WAIT_TIMEOUT', 20))
            logger.info("浏览器启动成功")
            
        except Exception as e:
            logger.error(f"浏览器启动失败: {str(e)}")
            raise
    
    def _find_chromedriver(self):
        """查找ChromeDriver路径"""
        current_dir = Path(__file__).parent
        possible_paths = [
            current_dir / "chromedriver.exe",
            current_dir.parent / "chromedriver.exe",
            "chromedriver.exe",
            "chromedriver"
        ]
        
        for path in possible_paths:
            if isinstance(path, Path) and path.exists():
                return str(path)
            elif isinstance(path, str) and shutil.which(path):
                return path
        
        return None
    
    def login(self, username, password, base_url):
        """登录系统"""
        try:
            logger.info("开始登录...")
            self.driver.get(base_url)
            time.sleep(self.config.get('OPERATION_DELAY', 3) + 1)
            
            # 点击登录按钮
            login_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span")
            ))
            login_btn.click()
            logger.info("已点击登录按钮")
            time.sleep(self.config.get('OPERATION_DELAY', 3))
            
            # 输入用户名
            username_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div/div/div/input")
            ))
            username_field.clear()
            username_field.send_keys(username)
            logger.info("已输入用户名")
            time.sleep(1)
            
            # 输入密码
            password_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[2]/div/div/input")
            ))
            password_field.clear()
            password_field.send_keys(password)
            logger.info("已输入密码")
            time.sleep(1)
            
            # 点击登录
            submit_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[4]/div/button")
            ))
            submit_btn.click()
            logger.info("已点击登录提交按钮")
            time.sleep(5)  # 等待登录完成
            
            logger.info("登录成功")
            
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            raise
    
    def close_popups(self):
        """关闭登录后的弹窗"""
        try:
            logger.info("🔄 开始弹窗快速检测与关闭...")
            time.sleep(1)
            
            max_attempts = 8
            check_interval = 0.6
            popup_found = False
            
            for attempt in range(max_attempts):
                logger.info(f"  📍 第 {attempt + 1}/{max_attempts} 次弹窗检测...")
                current_popup_closed = False
                
                # 尝试关闭智能小助手弹窗
                try:
                    close_btn1 = self.driver.find_element(By.CSS_SELECTOR, ".maxkb-close")
                    if close_btn1.is_displayed():
                        close_btn1.click()
                        logger.info("    ✅ 关闭智能小助手弹窗")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试点击"我知道了"按钮
                try:
                    know_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), '我知道了')]")
                    if know_btn.is_displayed():
                        know_btn.click()
                        logger.info("    ✅ 点击'我知道了'按钮")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试关闭文档管控公告弹窗
                try:
                    notification_selectors = [
                        ".el-notification .el-icon",
                        ".el-notification__closeBtn",
                        "#notification_1 .el-icon",
                        ".layout-notification .el-icon"
                    ]
                    
                    for selector in notification_selectors:
                        try:
                            close_btn2 = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if close_btn2.is_displayed():
                                close_btn2.click()
                                logger.info(f"    ✅ 关闭文档管控公告弹窗 (选择器: {selector})")
                                current_popup_closed = True
                                popup_found = True
                                time.sleep(0.5)
                                break
                        except (NoSuchElementException, Exception):
                            continue
                except Exception:
                    pass
                
                # 检测逻辑
                if not current_popup_closed:
                    if popup_found and attempt < max_attempts - 1:
                        time.sleep(check_interval)
                        continue
                    elif not popup_found and attempt >= 3:
                        logger.info("    📝 连续多次未发现弹窗，提前结束检测")
                        break
                    else:
                        time.sleep(check_interval)
                else:
                    time.sleep(0.8)
                    
            if popup_found:
                logger.info("🎉 弹窗关闭流程完成")
                time.sleep(2.5)
            else:
                logger.info("✨ 弹窗检测完成 (未发现弹窗)")
                time.sleep(1.5)
                
        except Exception as e:
            logger.warning(f"⚠️ 关闭弹窗过程中出现问题，继续执行: {str(e)}")
    
    def navigate_to_document_creation(self):
        """导航到文档创建页面"""
        try:
            logger.info("🧭 导航到文档创建页面...")
            
            # 直接访问文档创建页面URL
            doc_create_url = "https://gcy.byd.com/dms/#/document/myDocument?type=docCreate"
            self.driver.get(doc_create_url)
            time.sleep(3)
            
            logger.info("  ✅ 文档创建页面已成功加载")
            
        except Exception as e:
            logger.error(f"❌ 导航到文档创建页面失败: {str(e)}")
            raise
    
    def return_to_main_page(self):
        """返回主页面 - 关闭新窗口并回到主页面"""
        try:
            logger.info("🏠 返回主页面...")
            
            # 获取所有窗口句柄
            window_handles = self.driver.window_handles
            logger.info(f"📱 当前窗口数量: {len(window_handles)}")
            
            # 无论有几个窗口，都直接导航到DMS主页
            try:
                dms_url = self.config.get('DMS_URL', 'https://gcy.byd.com/dms/#/dashboard')
                
                # 如果有多个窗口，等待30秒后关闭额外窗口
                if len(window_handles) > 1:
                    time.sleep(30)
                    
                    # 关闭除第一个窗口外的所有窗口
                    for handle in window_handles[1:]:
                        try:
                            self.driver.switch_to.window(handle)
                            time.sleep(1)
                            self.driver.close()
                            time.sleep(2)
                            logger.info(f"✅ 关闭窗口: {handle}")
                        except Exception as e:
                            logger.debug(f"关闭窗口失败: {str(e)}")
                    
                    # 切换回第一个窗口
                    self.driver.switch_to.window(window_handles[0])
                    time.sleep(3)
                
                # 导航到DMS主页
                self.driver.get(dms_url)
                time.sleep(3)
                logger.info("✅ 返回到DMS主页")
                
            except Exception as e:
                logger.error(f"❌ 返回主页失败: {str(e)}")
                # 备用方案：尝试刷新当前页面
                try:
                    self.driver.refresh()
                    time.sleep(3)
                    logger.info("✅ 刷新页面作为备用方案")
                except Exception:
                    pass
                except Exception as e:
                    logger.debug(f"导航到主页失败: {str(e)}")
                    # 尝试刷新页面
                    self.driver.refresh()
                    time.sleep(3)
            
            logger.info("✅ 返回主页面完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 返回主页面失败: {str(e)}")
            return False
    
    def quick_login(self):
        """快速登录 - 只需点击登录按钮"""
        try:
            logger.info("🔐 执行快速登录...")
            
            # 查找登录按钮
            login_selectors = [
                "//button[contains(text(), '登录') or contains(text(), '登陆')]",
                "//button[contains(@class, 'login') or contains(@class, 'signin')]",
                "//input[@type='submit' and (@value='登录' or @value='登陆')]",
                "//a[contains(text(), '登录') or contains(text(), '登陆')]",
                "//div[contains(@class, 'login')]//button",
                "//form//button[@type='submit']"
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            login_button = button
                            logger.info(f"✅ 找到登录按钮: {selector}")
                            break
                    if login_button:
                        break
                except Exception as e:
                    logger.debug(f"登录按钮选择器失败 {selector}: {str(e)}")
                    continue
            
            if login_button:
                # 点击登录按钮
                try:
                    login_button.click()
                    time.sleep(3)
                    logger.info("✅ 点击登录按钮成功")
                except Exception as e:
                    # 使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", login_button)
                    time.sleep(3)
                    logger.info("✅ JavaScript点击登录按钮成功")
                
                # 等待登录完成
                time.sleep(5)
                
                # 关闭可能的弹窗
                self.close_popups()
                
                logger.info("✅ 快速登录完成")
                return True
            else:
                logger.info("ℹ️ 未找到登录按钮，可能已经登录")
                time.sleep(3)
                return True
            
        except Exception as e:
            logger.error(f"❌ 快速登录失败: {str(e)}")
            return False
    
    def quit(self):
        """关闭浏览器"""
        if self.driver:
            try:
                # 关闭所有窗口
                self.driver.quit()
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {str(e)}")
                # 强制终止浏览器进程
                self._force_kill_browser()
            finally:
                self.driver = None
                self.wait = None
    
    def _force_kill_browser(self):
        """强制终止浏览器进程"""
        try:
            import subprocess
            import platform
            
            # 根据操作系统选择命令
            if platform.system() == "Windows":
                # Windows系统
                subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                             capture_output=True, text=True)
                subprocess.run(['taskkill', '/F', '/IM', 'chromedriver.exe'], 
                             capture_output=True, text=True)
                logger.info("强制终止Chrome浏览器进程")
            else:
                # Linux/Mac系统
                subprocess.run(['pkill', '-f', 'chrome'], 
                             capture_output=True, text=True)
                subprocess.run(['pkill', '-f', 'chromedriver'], 
                             capture_output=True, text=True)
                logger.info("强制终止Chrome浏览器进程")
        except Exception as e:
            logger.error(f"强制终止浏览器进程失败: {str(e)}")
    
    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        self.quit()
