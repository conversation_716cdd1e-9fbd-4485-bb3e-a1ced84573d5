# 上传审批自动化程序使用指南

## 程序概述

这个selenium自动化程序可以根据您提供的JSON文件、HTML文件和现有的申请编号程序，自动完成文档的上传审批流程。程序支持DVP、PPL、FN三种类型的文档，并能自动识别文档类型、填写相关信息、上传文件、选择评审人并设置审批流程。

## 核心功能

### 1. 自动文档识别
- 从文件名中提取文档编号（如：HYHB_FN_A19-000011、HC2_PPL_A19-000001、SY_DVP_A19-000056）
- 自动识别文档类型（DVP、PPL、FN）
- 匹配对应的源文件和PDF文件

### 2. 完整上传流程
- **第一页面**：文档创建页面
  - 填写文档编号并选择浮动选项
  - 设置所属项目阶段（B版）
  - 选择交付物级别（项目级）
  - 填写文件内容简要
  - 上传源文件
  - 删除自动转换的PDF
  - 上传正确的PDF文件
  - 点击"保存并发起评审"

- **第二页面**：评审设置页面
  - 根据文档类型选择对应的评审人方案
  - 为每个评审角色填写审批人（支持多人）
  - 从Excel数据文件中读取人员邮箱/工号
  - 设置截止日期（当前日期+7天）
  - 提交审批（测试模式下不真正提交）

### 3. 智能元素定位
- 使用多种定位方式确保稳定性
- 自动处理登录后的弹窗
- 适应动态生成的元素ID
- 错误重试机制

## 文件结构说明

```
Upload/
├── upload_approval_automation.py  # 主程序
├── config.py                     # 配置文件
├── requirements.txt              # Python依赖包
├── create_sample_data.py         # 示例数据创建脚本
├── start.bat                     # 启动脚本
├── README.md                     # 说明文档
├── Data/                         # 数据文件夹
│   └── Fill_Template_Data.xlsx   # 人员信息Excel文件
├── Final_Approval_Documents/     # 待处理文档文件夹
│   ├── HYHB_FN_A19-000011-接口定义.docx
│   ├── HYHB_FN_A19-000011-接口定义.pdf
│   ├── HC2_PPL_A19-000001-匹配计划.xlsx
│   └── HC2_PPL_A19-000001-匹配计划.pdf
└── 参考文件/
    ├── 上传审批dvp.json         # DVP流程参考
    ├── 上传审批fn.json          # FN流程参考
    ├── 上传审批ppl.json         # PPL流程参考
    ├── 上传审批第一个界面.html   # 页面结构参考
    ├── 上传审批第二个界面.html   # 评审页面参考
    └── apply_id_automation.py   # 登录流程参考
```

## 快速开始

### 1. 环境准备
1. **运行启动脚本**：双击 `start.bat`，它会自动：
   - 检查Python环境
   - 安装依赖包
   - 创建必要的文件夹
   - 生成示例数据文件

2. **下载ChromeDriver**：
   - 访问 https://chromedriver.chromium.org/
   - 下载与Chrome浏览器版本匹配的ChromeDriver
   - 将 `chromedriver.exe` 放在程序目录下

### 2. 配置设置

1. **编辑config.py**：
```python
# 登录信息（可选，留空会提示手动登录）
USERNAME = 'your_username'  # 填写您的用户名
PASSWORD = 'your_password'  # 填写您的密码

# 测试模式（建议首次运行时保持True）
TEST_MODE = True  # True: 测试模式，False: 正式提交
```

2. **准备人员数据**：
   - 编辑 `Data/Fill_Template_Data.xlsx` 文件
   - 在 `Info` sheet中填写正确的人员信息
   - 确保邮箱和工号准确无误

### 3. 准备文档文件

1. **文件命名规范**：
   - 源文件：`HYHB_FN_A19-000011-接口定义通知单.docx`
   - PDF文件：`HYHB_FN_A19-000011-接口定义通知单.pdf`
   - 文档编号必须在文件名最前面

2. **支持的文档类型**：
   - DVP类型：`*_DVP_*`
   - PPL类型：`*_PPL_*`
   - FN类型：`*_FN_*`

3. **放置文档**：
   - 将文档对（源文件+PDF）放入 `Final_Approval_Documents` 文件夹

### 4. 运行程序

1. **测试运行**：
```bash
python upload_approval_automation.py
```

2. **程序流程**：
   - 自动启动Chrome浏览器（最大化）
   - 导航到DMS系统并登录
   - 关闭登录后的弹窗
   - 扫描文档文件夹
   - 依次处理每个文档对
   - 生成详细日志

## 评审人方案配置

### DVP文档评审角色
- 部门相关方（DVP_Signatory）
- 系统专家（DVP_Expert）

### PPL文档评审角色
- 相关方（PPL_Signatory）
- 数据管理员（PPL_DataManager）
- 科长（PPL_SectionChief）

### FN文档评审角色
- 数据管理员（FN_DataManager）
- 科长（FN_SectionChief）
- 相关方（FN_Stakeholder）
- 项目主管（FN_ProjectManager）

## 测试模式说明

**重要**：首次运行请保持 `TEST_MODE = True`

测试模式特点：
- 会执行完整流程直到最后提交步骤
- 不会真正点击"提交"按钮
- 等待10秒后自动返回主页面
- 避免产生无效的审批流程

正式模式：
- 将 `config.py` 中的 `TEST_MODE` 改为 `False`
- 会真正提交审批流程
- 请确保所有信息准确无误

## 错误处理

### 常见问题

1. **ChromeDriver版本不匹配**
   - 检查Chrome浏览器版本
   - 下载对应版本的ChromeDriver

2. **登录失败**
   - 检查用户名密码是否正确
   - 可以选择手动登录模式

3. **元素定位失败**
   - 网页结构可能已更新
   - 查看日志文件了解详细错误

4. **文件上传失败**
   - 检查文件路径和文件格式
   - 确保文件大小符合系统要求

### 日志文件

程序运行时会生成 `upload_approval.log` 文件，记录：
- 详细的操作步骤
- 错误信息和堆栈跟踪
- 元素定位的多种尝试
- 文件处理状态

## 注意事项

1. **网络环境**：确保能正常访问DMS系统
2. **文件完整性**：每个文档编号必须有对应的源文件和PDF文件
3. **数据准确性**：Excel中的人员信息必须准确
4. **操作延时**：程序在各操作间有2-3秒延时，确保网页响应
5. **测试先行**：强烈建议首次运行使用测试模式

## 技术实现

### 基于JSON流程的元素定位
程序参考了您提供的三个JSON文件：
- `上传审批dvp.json`：DVP文档的完整操作序列
- `上传审批fn.json`：FN文档的评审人设置
- `上传审批ppl.json`：PPL文档的评审人配置

### 多种定位策略
```python
# 示例：文档创建按钮定位
selectors = [
    "//span[contains(text(), '文档创建')]",
    "//*[@id='main']/div/div/div/div/div/div/span",
    "//div[contains(@class, 'DocCreate')]//span",
    "xpath=//div/div/div/div/span"
]
```

### 智能重试机制
- 元素查找失败时自动尝试其他定位方式
- 网络超时时自动重试
- 最多重试3次后报告错误

## 扩展和维护

如需扩展程序功能：

1. **添加新的文档类型**：
   - 在 `file_types` 配置中添加新类型
   - 在 `get_file_type()` 方法中添加识别逻辑
   - 在人员映射中添加对应角色

2. **修改评审流程**：
   - 更新 `select_review_scheme()` 方法
   - 调整 `fill_reviewers()` 中的角色映射

3. **适应页面变化**：
   - 更新元素定位器
   - 调整操作流程顺序

## 联系支持

遇到问题时，请：
1. 查看 `upload_approval.log` 日志文件
2. 检查配置文件和数据文件
3. 确认网络连接和系统访问权限
4. 联系技术支持并提供日志文件
