from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import logging
from pathlib import Path
import time

logger = logging.getLogger(__name__)


def login(driver, wait, username, password, timeout=30):
    """
    执行登录操作。

    Args:
        driver: Selenium WebDriver 实例。
        wait: WebDriverWait 实例。
        username (str): 用户名。
        password (str): 密码。
        timeout (int): 等待时间（秒）。

    Raises:
        Exception: 如果登录失败。
    """
    try:
        # 确保 screenshots 目录存在
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)

        logger.info("开始登录...")
        # 等待用户名输入框加载并可操作
        username_field = wait.until(EC.element_to_be_clickable(
            (By.XPATH, "//input[@placeholder='用户名（工号或登录名）']")
        ))
        username_field.send_keys(username)
        logger.info("已填写用户名")

        # 等待密码输入框加载并可操作
        password_field = wait.until(EC.element_to_be_clickable(
            (By.XPATH, "//input[@placeholder='密码同OA密码']")
        ))
        password_field.send_keys(password)
        logger.info("已填写密码")

        # 等待登录按钮加载并可点击
        login_button = wait.until(EC.element_to_be_clickable(
            (By.XPATH, "//button[contains(@class, 'login-submit') and .//span[text()='立即登录']]")
        ))
        login_button.click()
        logger.info("已点击登录按钮")

        # 检查页面是否加载（以弹窗元素为标志）
        max_attempts = 3
        attempt = 1
        while attempt <= max_attempts:
            try:
                # 等待 15 秒，检查弹窗元素
                wait = WebDriverWait(driver, 15)
                close_button = wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//div[contains(@class, 'maxkb-close')] | //button[text()='我知道了']")
                ))
                # 找到弹窗元素，页面加载成功
                if "maxkb-close" in close_button.get_attribute("class"):
                    close_button.click()
                    logger.info("已关闭弹窗（叉号）")
                else:
                    close_button.click()
                    logger.info("已关闭弹窗（我知道了）")

                # 等待 2 秒以稳定页面
                logger.info("等待 2 秒以稳定页面...")
                time.sleep(2)

                # 点击 workbench-side-setting 元素以触发浮动框
                setting_button = wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//*[@id='root']/div/div[3]/div[1]/div/div[3]")
                ))
                setting_button.click()
                logger.info("已点击 workbench-side-setting 元素以触发浮动框")

                break  # 成功关闭弹窗并点击元素，退出循环
            except TimeoutException:
                logger.warning(f"第 {attempt} 次尝试：页面未加载，刷新页面...")
                if attempt == max_attempts:
                    logger.error("页面加载失败，已尝试 3 次")
                    driver.save_screenshot("screenshots/login_page_load_error.png")
                    raise TimeoutException("页面加载失败，尝试 3 次后仍未加载")
                driver.refresh()
                logger.info("页面已刷新，等待下一次检查...")
                attempt += 1

    except Exception as e:
        logger.error(f"登录失败，错误信息: {str(e)}")
        driver.save_screenshot("screenshots/login_error.png")
        raise