"""
启动脚本
运行上传审批自动化程序
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_controller import main
    
    if __name__ == "__main__":
        print("🚀 启动上传审批自动化程序...")
        print("=" * 50)
        
        # 执行主程序
        main()
        
        print("=" * 50)
        print("✅ 程序执行完成")
        
        # 静默模式下直接退出，窗口模式下稍等片刻
        try:
            import config
            if getattr(config, 'HEADLESS_MODE', False):
                print("🔄 静默模式，程序即将自动退出...")
                time.sleep(2)
            else:
                print("🔄 程序将在5秒后自动退出...")
                time.sleep(5)
        except:
            time.sleep(3)
        
        print("🔚 程序已自动退出")
        
except ImportError as e:
    print(f"❌ 导入模块失败: {str(e)}")
    print("请确保所有依赖文件都在当前目录下")
    time.sleep(3)
except Exception as e:
    print(f"❌ 程序执行出错: {str(e)}")
    time.sleep(3)
finally:
    # 确保程序完全退出
    sys.exit(0)
