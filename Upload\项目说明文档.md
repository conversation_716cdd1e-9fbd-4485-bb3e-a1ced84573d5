# 上传审批自动化系统

## 🎯 项目概述

本项目是一个基于Selenium的Web自动化系统，用于自动化处理文档上传审批流程。系统能够自动识别文档类型（DVP、FN、PPL），填写审批信息，选择合适的评审人方案，并完成整个审批提交流程。

## 🏗️ 系统架构

```
上传审批自动化系统
├── 主控制器 (main_controller.py)
├── 浏览器管理 (browser_manager.py)
├── 第一页处理器 (first_page_handler.py)
├── 第二页处理器 (second_page_handler.py)
├── 工具函数 (utils.py)
└── 配置管理 (config.py)
```

## 📋 核心功能

### 1. 文档扫描与识别
- 自动扫描指定目录下的文档文件
- 支持 Word (.docx) 和 PDF (.pdf) 文件
- 根据文件名模式自动识别文档类型：
  - **DVP**: 数据验证计划文档
  - **FN**: 功能接口定义通知单
  - **PPL**: 项目计划文档

### 2. 第一页处理
- 自动填写文档基本信息
- 上传源文件和PDF文件
- 处理文件上传确认和页面跳转

### 3. 第二页处理
- 根据文档类型自动选择评审人方案
- 从Excel模板读取人员信息
- 自动填写各角色的评审人员（支持多人员）
- 智能浮动项定位和选择
- 自动填写截止时间（当前时间+1周）
- 提交审批申请

### 4. 多文件批处理
- 支持批量处理多个文档
- 每个文档处理完成后自动返回主页
- 智能窗口管理和页面导航

## 🔧 技术特点

### 智能元素定位
- 多层次选择器策略（XPath、CSS、ID）
- 基于UI.Vision录制的精确定位
- 动态元素智能识别和等待

### 健壮性设计
- 多种备选方案和容错机制
- 详细的日志记录和错误处理
- 操作间智能等待时间控制

### 数据处理
- Excel多分隔符和横向扩展支持
- 动态角色映射和人员信息提取
- 输入文本有效性验证（过滤中文等无效字符）

## 📊 支持的文档类型

### DVP (数据验证计划)
- **评审人方案**: 数据管理员、部门相关方、系统专家、项目主管、车型品质主管、车型研发品质经理
- **角色映射**: DVP_Signatory、Experts、ChassisProjectManager等

### FN (功能接口定义通知单)
- **评审人方案**: 数据管理员、科长、相关方、项目主管
- **动态识别**: 根据文件名自动识别具体系统（DiSus-A、VCU、ESP等）
- **角色映射**: Data_Manager、SectionHead、FN_DiSus_A等

### PPL (项目计划)
- **评审人方案**: 数据管理员、科长、相关方
- **角色映射**: PPL_Signatory、Data_Manager、SectionHead

## 🚀 快速集成指南

### 1. 环境准备
```bash
pip install -r requirements.txt
```

### 2. 配置设置
```python
# config.py 中的关键配置
DMS_URL = 'https://gcy.byd.com/dms/#/dashboard'
HEADLESS = False  # 是否无头模式
TIMEOUT = 20      # 默认超时时间
```

### 3. 数据准备
- 准备 `Data/Fill_Template_Data.xlsx` 人员信息模板
- 在 `Info` sheet中配置角色和人员邮箱信息

### 4. 启动使用
```python
from main_controller import UploadApprovalController

# 创建控制器实例
controller = UploadApprovalController()

# 处理所有文档
results = controller.process_all_documents()
```

### 5. 批处理模式
```bash
python run.py
# 或
start.bat
```

## 🔌 API接口

### 主控制器
```python
class UploadApprovalController:
    def process_all_documents() -> Dict[str, int]
    def process_single_document(main_file: Dict, excel_file: Dict) -> bool
```

### 页面处理器
```python
class FirstPageHandler:
    def fill_document_info(doc_id: str, source_file: Dict, file_type: str, excel_file: Dict) -> bool

class SecondPageHandler:
    def handle_second_page(file_type: str, filename: str) -> bool
```

### 工具函数
```python
class DocumentUtils:
    def scan_documents() -> List[Tuple[Dict, Dict]]
    def identify_file_type(filename: str) -> str
```

## 📈 性能特点

- **处理速度**: 单个文档处理时间约2-5分钟
- **成功率**: 在稳定网络环境下成功率>95%
- **容错性**: 多重备选策略，单点失败不影响整体流程
- **可扩展性**: 模块化设计，易于添加新的文档类型和处理逻辑

## 🛠️ 自定义扩展

### 添加新文档类型
1. 在 `config.py` 中添加文档类型配置
2. 在 `second_page_handler.py` 中添加评审人方案
3. 在 `utils.py` 中添加文件识别规则

### 修改评审人方案
1. 更新 `second_page_handler.py` 中的 `reviewer_schemes`
2. 调整Excel模板中的角色列配置
3. 测试新的角色映射关系

### 集成到更大系统
1. 将项目作为子模块导入
2. 通过 `main_controller.py` 的API调用功能
3. 监控日志输出获取处理状态和结果

## 📝 注意事项

1. **网络环境**: 需要稳定的网络连接访问目标系统
2. **浏览器版本**: 确保Chrome浏览器和ChromeDriver版本兼容
3. **权限设置**: 确保程序有文件读写权限
4. **Excel格式**: 严格按照模板格式配置人员信息
5. **等待时间**: 根据网络环境可能需要调整等待时间参数

## 🔍 故障排除

- **元素定位失败**: 检查页面结构是否变化，更新选择器
- **文件上传失败**: 检查文件路径和格式，确认网络连接
- **人员选择失败**: 验证Excel数据格式和人员信息完整性
- **浮动项点击失败**: 增加等待时间，检查页面加载状态

## 📞 技术支持

项目采用模块化设计，每个组件都有详细的日志记录。在集成时遇到问题，可以通过日志文件分析具体的失败原因和解决方案。
