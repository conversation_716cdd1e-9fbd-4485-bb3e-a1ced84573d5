import sys
import os
import json
import shutil
import subprocess
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox,
    QGroupBox, QListWidget, QTextEdit, QTabWidget, QFileDialog,
    QMessageBox, QProgressBar, QSplitter, QFrame, QListWidgetItem,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QScrollArea,
    QButtonGroup, QRadioButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap


class VehicleManagementGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("车型文件管理系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置路径
        self.base_path = Path(__file__).parent
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.fillin_path = self.base_path / "Fillin"
        self.final_approval_path = self.base_path / "Final_Approval_Documents"
        self.update_templates_path = self.base_path / "Update_Templates"
        self.logs_path = self.base_path / "logs"
        
        # 创建必要的目录
        self.vehicles_path.mkdir(exist_ok=True)
        self.final_approval_path.mkdir(exist_ok=True)
        self.logs_path.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 初始化UI
        self.init_ui()
        
        # 设置样式
        self.setup_style()
        
        # 当前选择的车型代号
        self.current_vehicle_code = ""
        
        # 初始化车型列表
        self.load_vehicle_codes()

    def setup_logging(self):
        """设置日志"""
        log_file = self.logs_path / f"vehicle_management_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建主要工作区域
        self.create_main_workspace(main_layout)
        
        # 创建状态栏
        self.create_status_bar()

    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setFixedHeight(80)
        
        title_layout = QHBoxLayout(title_frame)
        
        # 标题
        title_label = QLabel("车型文件管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
            }
        """)
        
        # 车型选择区域
        vehicle_group = QGroupBox("车型设置")
        vehicle_layout = QHBoxLayout(vehicle_group)
        
        vehicle_layout.addWidget(QLabel("车型代号:"))
        self.vehicle_combo = QComboBox()
        self.vehicle_combo.setEditable(True)
        self.vehicle_combo.setMinimumWidth(120)
        self.vehicle_combo.currentTextChanged.connect(self.on_vehicle_changed)
        vehicle_layout.addWidget(self.vehicle_combo)
        
        self.setup_vehicle_btn = QPushButton("设置车型信息")
        self.setup_vehicle_btn.clicked.connect(self.setup_vehicle_info)
        vehicle_layout.addWidget(self.setup_vehicle_btn)
        
        # 快捷按钮区域
        quick_actions_group = QGroupBox("快捷操作")
        quick_layout = QHBoxLayout(quick_actions_group)
        
        self.open_final_approval_btn = QPushButton("打开审批文件夹")
        self.open_final_approval_btn.clicked.connect(self.open_final_approval_folder)
        quick_layout.addWidget(self.open_final_approval_btn)
        
        self.open_logs_btn = QPushButton("打开日志文件夹")
        self.open_logs_btn.clicked.connect(self.open_logs_folder)
        quick_layout.addWidget(self.open_logs_btn)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(vehicle_group)
        title_layout.addWidget(quick_actions_group)
        
        parent_layout.addWidget(title_frame)

    def create_main_workspace(self, parent_layout):
        """创建主工作区域"""
        # 创建选项卡widget
        self.tab_widget = QTabWidget()
        
        # 文件操作选项卡
        self.create_file_operations_tab()
        
        # 申请审批选项卡
        self.create_application_approval_tab()
        
        # 上传审批选项卡
        self.create_upload_approval_tab()
        
        # 日志监控选项卡
        self.create_log_monitor_tab()
        
        parent_layout.addWidget(self.tab_widget)

    def create_file_operations_tab(self):
        """创建文件操作选项卡"""
        file_ops_widget = QWidget()
        layout = QVBoxLayout(file_ops_widget)
        
        # 操作类型选择
        operations_group = QGroupBox("操作类型选择")
        operations_layout = QHBoxLayout(operations_group)
        
        self.apply_id_cb = QCheckBox("申请文件编号")
        self.fill_content_cb = QCheckBox("填写文件内容")
        operations_layout.addWidget(self.apply_id_cb)
        operations_layout.addWidget(self.fill_content_cb)
        operations_layout.addStretch()
        
        layout.addWidget(operations_group)
        
        # 文件选择区域
        file_selection_group = QGroupBox("文件选择")
        file_selection_layout = QVBoxLayout(file_selection_group)
        
        # 文件类型分组
        file_types_layout = QHBoxLayout()
        
        # DVP文件
        dvp_group = QGroupBox("DVP文件")
        dvp_layout = QVBoxLayout(dvp_group)
        self.dvp_cb = QCheckBox("DVP-系统设计验证计划")
        dvp_layout.addWidget(self.dvp_cb)
        file_types_layout.addWidget(dvp_group)
        
        # PPL文件
        ppl_group = QGroupBox("PPL文件")
        ppl_layout = QVBoxLayout(ppl_group)
        self.ppl_cb = QCheckBox("PPL-车辆匹配计划")
        ppl_layout.addWidget(self.ppl_cb)
        file_types_layout.addWidget(ppl_group)
        
        # FN文件
        fn_group = QGroupBox("FN文件")
        fn_layout = QVBoxLayout(fn_group)
        
        # 创建滚动区域以显示多个FN文件
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        self.fn_checkboxes = {}
        fn_files = [
            "FN_IMU", "FN_VCU", "FN_IPB", "FN_ESP_BWA", "FN_EPS", 
            "FN_EPSA", "FN_EPB", "FN_DISUS_A", "FN_DISUS_C", 
            "FN_DISUS_P", "FN_DISUS_X", "FN_DISUS_M", "FN_域控"
        ]
        
        for fn_file in fn_files:
            cb = QCheckBox(fn_file.replace("_", "-"))
            self.fn_checkboxes[fn_file] = cb
            scroll_layout.addWidget(cb)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        
        fn_layout.addWidget(scroll_area)
        file_types_layout.addWidget(fn_group)
        
        file_selection_layout.addLayout(file_types_layout)
        
        # 全选/取消全选按钮
        select_buttons_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_files)
        self.deselect_all_btn = QPushButton("取消全选")
        self.deselect_all_btn.clicked.connect(self.deselect_all_files)
        
        select_buttons_layout.addWidget(self.select_all_btn)
        select_buttons_layout.addWidget(self.deselect_all_btn)
        select_buttons_layout.addStretch()
        
        file_selection_layout.addLayout(select_buttons_layout)
        layout.addWidget(file_selection_group)
        
        # 执行按钮
        execute_layout = QHBoxLayout()
        self.execute_btn = QPushButton("执行操作")
        self.execute_btn.clicked.connect(self.execute_file_operations)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        execute_layout.addStretch()
        execute_layout.addWidget(self.execute_btn)
        execute_layout.addStretch()
        
        layout.addLayout(execute_layout)
        
        self.tab_widget.addTab(file_ops_widget, "文件操作")

    def create_application_approval_tab(self):
        """创建申请审批选项卡"""
        app_approval_widget = QWidget()
        layout = QVBoxLayout(app_approval_widget)
        
        # 登录信息设置
        login_group = QGroupBox("登录设置")
        login_layout = QFormLayout(login_group)
        
        self.username_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        
        login_layout.addRow("用户名:", self.username_edit)
        login_layout.addRow("密码:", self.password_edit)
        
        layout.addWidget(login_group)
        
        # 申请选项
        options_group = QGroupBox("申请选项")
        options_layout = QHBoxLayout(options_group)
        
        self.silent_mode_cb = QCheckBox("静默模式")
        self.test_mode_cb = QCheckBox("测试模式")
        
        options_layout.addWidget(self.silent_mode_cb)
        options_layout.addWidget(self.test_mode_cb)
        options_layout.addStretch()
        
        layout.addWidget(options_group)
        
        # 执行申请按钮
        apply_layout = QHBoxLayout()
        self.apply_id_btn = QPushButton("申请文件编号")
        self.apply_id_btn.clicked.connect(self.apply_file_ids)
        self.apply_id_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        
        apply_layout.addStretch()
        apply_layout.addWidget(self.apply_id_btn)
        apply_layout.addStretch()
        
        layout.addLayout(apply_layout)
        
        # 进度显示
        self.apply_progress = QProgressBar()
        self.apply_progress.setVisible(False)
        layout.addWidget(self.apply_progress)
        
        layout.addStretch()
        
        self.tab_widget.addTab(app_approval_widget, "申请编号")

    def create_upload_approval_tab(self):
        """创建上传审批选项卡"""
        upload_approval_widget = QWidget()
        layout = QVBoxLayout(upload_approval_widget)
        
        # 上传选项
        upload_options_group = QGroupBox("上传选项")
        upload_options_layout = QHBoxLayout(upload_options_group)
        
        self.upload_silent_mode_cb = QCheckBox("静默模式")
        self.upload_test_mode_cb = QCheckBox("测试模式")
        
        upload_options_layout.addWidget(self.upload_silent_mode_cb)
        upload_options_layout.addWidget(self.upload_test_mode_cb)
        upload_options_layout.addStretch()
        
        layout.addWidget(upload_options_group)
        
        # 上传状态表格
        status_group = QGroupBox("文件上传状态")
        status_layout = QVBoxLayout(status_group)
        
        self.upload_status_table = QTableWidget()
        self.upload_status_table.setColumnCount(5)
        self.upload_status_table.setHorizontalHeaderLabels([
            "文件名", "编号", "完整文件名", "已填写", "已上传"
        ])
        self.upload_status_table.horizontalHeader().setStretchLastSection(True)
        
        status_layout.addWidget(self.upload_status_table)
        layout.addWidget(status_group)
        
        # 执行上传按钮
        upload_layout = QHBoxLayout()
        self.upload_approval_btn = QPushButton("上传审批文件")
        self.upload_approval_btn.clicked.connect(self.upload_approval_files)
        self.upload_approval_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        upload_layout.addStretch()
        upload_layout.addWidget(self.upload_approval_btn)
        upload_layout.addStretch()
        
        layout.addLayout(upload_layout)
        
        # 进度显示
        self.upload_progress = QProgressBar()
        self.upload_progress.setVisible(False)
        layout.addWidget(self.upload_progress)
        
        self.tab_widget.addTab(upload_approval_widget, "上传审批")

    def create_log_monitor_tab(self):
        """创建日志监控选项卡"""
        log_monitor_widget = QWidget()
        layout = QVBoxLayout(log_monitor_widget)
        
        # 日志控制按钮
        log_controls_layout = QHBoxLayout()
        
        self.open_main_log_btn = QPushButton("主程序日志")
        self.open_main_log_btn.clicked.connect(self.open_main_log)
        
        self.open_subprocess_log_btn = QPushButton("子进程日志")
        self.open_subprocess_log_btn.clicked.connect(self.open_subprocess_log)
        
        self.open_update_templates_btn = QPushButton("更新模板文件夹")
        self.open_update_templates_btn.clicked.connect(self.open_update_templates_folder)
        
        self.replace_templates_btn = QPushButton("替换模板")
        self.replace_templates_btn.clicked.connect(self.replace_templates)
        
        log_controls_layout.addWidget(self.open_main_log_btn)
        log_controls_layout.addWidget(self.open_subprocess_log_btn)
        log_controls_layout.addWidget(self.open_update_templates_btn)
        log_controls_layout.addWidget(self.replace_templates_btn)
        log_controls_layout.addStretch()
        
        layout.addLayout(log_controls_layout)
        
        # 实时日志显示
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        
        layout.addWidget(QLabel("实时日志:"))
        layout.addWidget(self.log_display)
        
        self.tab_widget.addTab(log_monitor_widget, "日志监控")

    def create_status_bar(self):
        """创建状态栏"""
        self.statusBar().showMessage("准备就绪")
        
        # 添加当前时间显示
        self.time_label = QLabel()
        self.statusBar().addPermanentWidget(self.time_label)
        
        # 定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def setup_style(self):
        """设置应用程序样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
            QPushButton:pressed {
                background-color: #1a252f;
            }
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            }
            QLineEdit {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            }
            QCheckBox {
                spacing: 5px;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #95a5a6;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #34495e;
            }
            QTabBar::tab:hover {
                background-color: #7f8c8d;
            }
        """)

    def load_vehicle_codes(self):
        """加载车型代号列表"""
        # 从Vehicles目录读取已有的车型代号
        if self.vehicles_path.exists():
            vehicle_codes = [d.name for d in self.vehicles_path.iterdir() if d.is_dir()]
            self.vehicle_combo.addItems(vehicle_codes)
        
        # 添加一些常用的车型代号
        common_codes = ["HAHB", "QYHA", "GXHD", "SYHC"]
        for code in common_codes:
            if self.vehicle_combo.findText(code) == -1:
                self.vehicle_combo.addItem(code)

    def on_vehicle_changed(self, vehicle_code):
        """车型代号改变时的处理"""
        self.current_vehicle_code = vehicle_code.strip().upper()
        if self.current_vehicle_code:
            self.statusBar().showMessage(f"当前车型: {self.current_vehicle_code}")
            self.load_vehicle_file_status()

    def setup_vehicle_info(self):
        """设置车型信息"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择或输入车型代号!")
            return
        
        try:
            # 创建车型文件夹结构
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            info_dir = vehicle_dir / "information"
            info_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制模板文件
            template_file = self.templates_path / "Fill_Template_Data.xlsx"
            target_file = info_dir / f"{self.current_vehicle_code}_Fill_Template_Data.xlsx"
            
            if not target_file.exists():
                shutil.copy2(template_file, target_file)
                
                # 更新Excel中的车型代号
                self.update_vehicle_code_in_excel(target_file)
                
                QMessageBox.information(self, "成功", f"车型 {self.current_vehicle_code} 初始化完成!")
            else:
                QMessageBox.information(self, "提示", f"车型 {self.current_vehicle_code} 已存在，跳过初始化。")
            
            # 打开Excel文件供用户编辑
            os.startfile(str(target_file))
            
        except Exception as e:
            self.logger.error(f"设置车型信息失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"设置车型信息失败: {str(e)}")

    def update_vehicle_code_in_excel(self, excel_file):
        """更新Excel文件中的车型代号"""
        try:
            df = pd.read_excel(excel_file, sheet_name="people")
            df.loc[df['角色'] == '车型代号', 'people'] = self.current_vehicle_code
            
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name="people", index=False)
                
        except Exception as e:
            self.logger.error(f"更新Excel中车型代号失败: {str(e)}")

    def load_vehicle_file_status(self):
        """加载车型文件状态"""
        try:
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            info_dir = vehicle_dir / "information"
            excel_file = info_dir / f"{self.current_vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                df = pd.read_excel(excel_file, sheet_name="File_Status")
                
                # 更新上传状态表格
                self.upload_status_table.setRowCount(len(df))
                for i, row in df.iterrows():
                    self.upload_status_table.setItem(i, 0, QTableWidgetItem(str(row.get('file_name', ''))))
                    self.upload_status_table.setItem(i, 1, QTableWidgetItem(str(row.get('code', ''))))
                    self.upload_status_table.setItem(i, 2, QTableWidgetItem(str(row.get('numbered_file', ''))))
                    self.upload_status_table.setItem(i, 3, QTableWidgetItem(str(row.get('is_fillin', ''))))
                    self.upload_status_table.setItem(i, 4, QTableWidgetItem(str(row.get('is_upload', ''))))
                
        except Exception as e:
            self.logger.error(f"加载车型文件状态失败: {str(e)}")    def select_all_files(self):
        """全选所有文件"""
        self.dvp_cb.setChecked(True)
        self.ppl_cb.setChecked(True)
        for cb in self.fn_checkboxes.values():
            cb.setChecked(True)

    def deselect_all_files(self):
        """取消全选"""
        self.dvp_cb.setChecked(False)
        self.ppl_cb.setChecked(False)
        for cb in self.fn_checkboxes.values():
            cb.setChecked(False)

    def execute_file_operations(self):
        """执行文件操作"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        if not (self.apply_id_cb.isChecked() or self.fill_content_cb.isChecked()):
            QMessageBox.warning(self, "警告", "请选择至少一种操作类型!")
            return
        
        # 收集选中的文件
        selected_files = []
        if self.dvp_cb.isChecked():
            selected_files.append("DVP")
        if self.ppl_cb.isChecked():
            selected_files.append("PPL")
        for fn_type, cb in self.fn_checkboxes.items():
            if cb.isChecked():
                selected_files.append(fn_type)
        
        if not selected_files:
            QMessageBox.warning(self, "警告", "请选择至少一个文件!")
            return
        
        try:
            from file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            # 复制模板文件到车型文件夹
            copied_files = file_manager.copy_template_files(self.current_vehicle_code, selected_files)
            
            operations_completed = []
            
            if self.apply_id_cb.isChecked():
                success = self.apply_file_numbers_with_manager(file_manager, selected_files)
                if success:
                    operations_completed.append("申请编号")
            
            if self.fill_content_cb.isChecked():
                success = self.fill_file_contents_with_manager(file_manager, selected_files)
                if success:
                    operations_completed.append("填写内容")
            
            message = f"文件操作完成!\n复制文件: {len(copied_files)} 个\n完成操作: {', '.join(operations_completed)}"
            QMessageBox.information(self, "成功", message)
            
            # 刷新文件状态表
            self.load_vehicle_file_status()
            
        except Exception as e:
            self.logger.error(f"执行文件操作失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行文件操作失败: {str(e)}")

    def copy_template_files(self, selected_files):
        """复制模板文件到车型文件夹"""
        vehicle_dir = self.vehicles_path / self.current_vehicle_code
        vehicle_dir.mkdir(exist_ok=True)
        
        file_mapping = {
            "DVP": "软件设计验证计划.xlsx",
            "PPL": "软件开发匹配测试计划.xlsx",
            "FN_IMU": "XX项目VSE系统 to 气囊系统接口定义通知单.docx",
            # 添加更多文件映射...
        }
        
        for file_type in selected_files:
            if file_type in file_mapping:
                template_name = file_mapping[file_type]
                template_path = self.templates_path / template_name
                if template_path.exists():
                    target_name = f"{self.current_vehicle_code}项目{template_name}"
                    target_path = vehicle_dir / target_name
                    if not target_path.exists():
                        shutil.copy2(template_path, target_path)
                        self.logger.info(f"复制文件: {template_name} -> {target_name}")

    def apply_file_numbers(self, selected_files):
        """申请文件编号"""
        # 这里调用申请编号的功能
        self.logger.info(f"申请文件编号: {selected_files}")
        # TODO: 实现申请编号的具体逻辑

    def fill_file_contents(self, selected_files):
        """填写文件内容"""
        # 这里调用填写文件内容的功能
        self.logger.info(f"填写文件内容: {selected_files}")
        # TODO: 实现填写文件内容的具体逻辑

    def apply_file_ids(self):
        """申请文件编号"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        # TODO: 实现申请编号功能
        QMessageBox.information(self, "提示", "申请编号功能正在开发中...")

    def upload_approval_files(self):
        """上传审批文件"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        # TODO: 实现上传审批功能
        QMessageBox.information(self, "提示", "上传审批功能正在开发中...")

    def open_final_approval_folder(self):
        """打开最终审批文件夹"""
        try:
            os.startfile(str(self.final_approval_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_logs_folder(self):
        """打开日志文件夹"""
        try:
            os.startfile(str(self.logs_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_main_log(self):
        """打开主程序日志"""
        # TODO: 实现打开主程序日志
        pass

    def open_subprocess_log(self):
        """打开子进程日志"""
        # TODO: 实现打开子进程日志
        pass

    def open_update_templates_folder(self):
        """打开更新模板文件夹"""
        try:
            os.startfile(str(self.update_templates_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def replace_templates(self):
        """替换模板"""
        # TODO: 实现替换模板功能
        QMessageBox.information(self, "提示", "替换模板功能正在开发中...")

    def update_time(self):
        """更新状态栏时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def closeEvent(self, event):
        """程序关闭时的处理"""
        # 清空密码
        self.password_edit.clear()
        event.accept()


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("车型文件管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 设置应用程序图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.png"))
    
    window = VehicleManagementGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
