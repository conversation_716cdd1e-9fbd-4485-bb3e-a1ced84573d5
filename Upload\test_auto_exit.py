"""
测试自动退出功能
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from browser_manager import BrowserManager
import config

def test_auto_exit():
    """测试自动退出功能"""
    print("🧪 测试自动退出功能...")
    
    # 创建配置字典
    config_dict = {}
    for attr in dir(config):
        if not attr.startswith('_'):
            config_dict[attr] = getattr(config, attr)
    
    # 创建浏览器管理器
    browser_manager = BrowserManager(config_dict)
    
    try:
        print("📱 启动浏览器...")
        browser_manager.setup_browser()
        print("✅ 浏览器启动成功")
        
        # 模拟一些操作
        print("⏱️  模拟执行任务...")
        time.sleep(2)
        
        print("🔄 测试正常退出...")
        browser_manager.quit()
        print("✅ 浏览器已正常关闭")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        browser_manager.quit()
    
    print("✅ 自动退出测试完成")

if __name__ == "__main__":
    test_auto_exit()
    print("🔚 测试脚本即将退出...")
    time.sleep(2)
    sys.exit(0)
