# 日志管理说明

## 概述
为了防止`upload_approval.log`文件过大影响性能，项目提供了多种日志清理方案。

## 自动日志管理（推荐）

### 方案1: 程序集成自动轮转
程序已集成自动日志管理功能，在每次启动时自动检查：
- **触发条件**: 日志文件超过5MB
- **处理方式**: 自动轮转，创建带时间戳的备份文件
- **备份保留**: 最多保留3个备份文件
- **使用方法**: 无需手动操作，程序自动处理

### 方案2: 启动脚本集成清理
使用 `start_with_log_cleanup.bat` 启动程序：
```bash
start_with_log_cleanup.bat
```
- 启动前检查日志大小
- 询问是否需要清理
- 然后启动主程序

## 手动日志管理

### 简单清理工具
```bash
# 检查日志状态
python simple_log_cleanup.py status

# 手动清理日志
python simple_log_cleanup.py clean
```

### 高级日志管理工具
```bash
# 检查并自动清理
python log_manager.py check

# 手动轮转日志
python log_manager.py rotate

# 截断日志（保留最后1000行）
python log_manager.py truncate

# 完全清空日志
python log_manager.py clear

# 查看日志状态
python log_manager.py status
```

### 批处理脚本清理
```bash
cleanup_logs.bat
```
- 检查文件大小
- 自动轮转超大文件
- 清理7天前的备份
- 限制备份文件数量

## 配置参数

### 默认设置
- **最大文件大小**: 5MB
- **保留备份数量**: 3个
- **备份保留天数**: 7天
- **截断保留行数**: 1000行

### 自定义配置
修改相应脚本中的参数：

**utils.py中的自动轮转**:
```python
check_and_rotate_log(
    log_file='upload_approval.log',
    max_size_mb=5,      # 修改最大大小
    max_backups=3       # 修改备份数量
)
```

**log_manager.py**:
```python
log_manager = LogManager(
    log_file="upload_approval.log",
    max_size_mb=5,      # 最大大小(MB)
    max_backups=3,      # 备份数量
    max_days=7          # 保留天数
)
```

## 日志文件结构

### 主日志文件
```
upload_approval.log          # 当前运行日志
```

### 备份文件命名
```
upload_approval.log.20250701_143022    # 轮转备份
upload_approval.log.backup_20250701_143022  # 简单备份
```

### 备份存储位置
- **轮转备份**: 与主日志同目录
- **批处理备份**: `logs_backup/` 目录

## 推荐使用方式

### 日常使用
1. **自动管理**: 使用默认的自动轮转功能，无需手动干预
2. **启动检查**: 使用 `start_with_log_cleanup.bat` 启动程序

### 定期维护
1. **每周检查**: 运行 `python simple_log_cleanup.py status`
2. **手动清理**: 如需要，运行 `python log_manager.py check`

### 问题排查
1. **查看备份**: 检查轮转后的备份文件
2. **恢复日志**: 从备份文件恢复重要日志信息

## 注意事项

1. **备份重要性**: 清理前自动创建备份，确保数据安全
2. **文件权限**: 确保程序有读写日志文件的权限
3. **磁盘空间**: 定期清理过期备份，避免占用过多空间
4. **运行时清理**: 避免在程序运行时手动删除日志文件

## 故障排除

### 清理失败
- 检查文件是否被其他程序占用
- 确认有写入权限
- 检查磁盘空间是否充足

### 备份过多
- 调整 `max_backups` 参数
- 手动删除不需要的备份文件
- 运行 `cleanup_logs.bat` 清理过期备份

---
*建议使用自动日志管理功能，既能保持日志文件大小合理，又能保留重要的历史记录。*
