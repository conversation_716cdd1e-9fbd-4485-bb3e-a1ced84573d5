"""
上传审批自动化程序
根据Final_Approval_Documents文件夹中的文件自动进行上传审批
"""

import os
import time
import logging
import re
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys  
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload_approval.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class UploadApprovalAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.config = self.load_config()
        
        # 文件夹路径
        self.documents_folder = Path("Final_Approval_Documents")
        self.data_folder = Path("Data")
        
        # 文件类型配置
        self.file_types = {
            "DVP": {
                "stage": "B版",  # 所属项目阶段
                "level": "项目级",  # 交付物级别
                "reviewer_scheme": "DVP评审方案"  # 评审人方案
            },
            "PPL": {
                "stage": "B版",
                "level": "项目级", 
                "reviewer_scheme": "PPL评审方案"
            },
            "FN": {
                "stage": "B版",
                "level": "项目级",
                "reviewer_scheme": "FN评审方案"
            }
        }
        
    def load_config(self):
        """加载配置"""
        config = {
            'DMS_URL': 'https://gcy.byd.com/dms/#/dashboard',
            'USERNAME': '',  # 需要在配置文件中设置
            'PASSWORD': '',  # 需要在配置文件中设置
            'WAIT_TIMEOUT': 20,
            'OPERATION_DELAY': 3,
            'TEST_MODE': True  # 测试模式，不会真正提交
        }
        
        # 尝试从config.py文件加载配置
        try:
            import config as cfg
            if hasattr(cfg, 'DMS_URL'):
                config['DMS_URL'] = cfg.DMS_URL
            if hasattr(cfg, 'USERNAME'):
                config['USERNAME'] = cfg.USERNAME  
            if hasattr(cfg, 'PASSWORD'):
                config['PASSWORD'] = cfg.PASSWORD
            if hasattr(cfg, 'WAIT_TIMEOUT'):
                config['WAIT_TIMEOUT'] = cfg.WAIT_TIMEOUT
            if hasattr(cfg, 'OPERATION_DELAY'):
                config['OPERATION_DELAY'] = cfg.OPERATION_DELAY
            if hasattr(cfg, 'TEST_MODE'):
                config['TEST_MODE'] = cfg.TEST_MODE
        except ImportError:
            logger.warning("未找到config.py文件，使用默认配置")
            
        return config
        
    def setup_driver(self):
        """设置Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 禁用保存密码提示
        prefs = {
            "credentials_enable_service": False,
            "profile.password_manager_enabled": False
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            # 尝试找到ChromeDriver
            current_dir = Path(__file__).parent
            chromedriver_paths = [
                current_dir / "chromedriver.exe",
                current_dir.parent / "chromedriver.exe", 
                "chromedriver.exe"
            ]
            
            chromedriver_path = None
            for path in chromedriver_paths:
                if isinstance(path, Path) and path.exists():
                    chromedriver_path = str(path)
                    break
                elif isinstance(path, str):
                    import shutil
                    if shutil.which(path):
                        chromedriver_path = path
                        break
            
            if chromedriver_path:
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
                
        except Exception as e:
            logger.error(f"ChromeDriver设置失败: {str(e)}")
            raise
        
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, self.config['WAIT_TIMEOUT'])
        logger.info("浏览器已启动并最大化")
        
    def login(self):
        """登录系统"""
        try:
            logger.info("开始登录...")
            self.driver.get(self.config['DMS_URL'])
            time.sleep(self.config['OPERATION_DELAY'] + 1)
            
            # 点击登录按钮
            login_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span")
            ))
            login_btn.click()
            logger.info("已点击登录按钮")
            time.sleep(self.config['OPERATION_DELAY'])
            
            # 如果配置了用户名密码，自动填入
            if self.config['USERNAME'] and self.config['PASSWORD']:
                # 输入用户名
                username_field = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div/div/div/input")
                ))
                username_field.clear()
                username_field.send_keys(self.config['USERNAME'])
                logger.info("已输入用户名")
                time.sleep(1)
                
                # 输入密码
                password_field = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[2]/div/div/input")
                ))
                password_field.clear()
                password_field.send_keys(self.config['PASSWORD'])
                logger.info("已输入密码")
                time.sleep(1)
                
                # 点击登录
                submit_btn = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[4]/div/button")
                ))
                submit_btn.click()
                logger.info("已点击登录提交按钮")
                time.sleep(5)
            else:
                logger.info("请手动完成登录...")
                # 等待用户手动登录
                input("请完成登录后按回车键继续...")
                
            logger.info("登录成功")
            
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            raise
            
    def close_popups(self):
        """关闭登录后的弹窗"""
        try:
            logger.info("开始关闭弹窗...")
            time.sleep(2)
            
            max_attempts = 8
            check_interval = 0.8
            popup_found = False
            
            for attempt in range(max_attempts):
                logger.info(f"第 {attempt + 1}/{max_attempts} 次弹窗检测...")
                current_popup_closed = False
                
                # 尝试关闭智能小助手弹窗
                try:
                    close_btn1 = self.driver.find_element(By.CSS_SELECTOR, ".maxkb-close")
                    if close_btn1.is_displayed():
                        close_btn1.click()
                        logger.info("关闭智能小助手弹窗")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试点击"我知道了"按钮
                try:
                    know_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), '我知道了')]")
                    if know_btn.is_displayed():
                        know_btn.click()
                        logger.info("点击'我知道了'按钮")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试关闭文档管控公告弹窗
                try:
                    notification_selectors = [
                        ".el-notification .el-icon",
                        ".el-notification__closeBtn",
                        "#notification_1 .el-icon",
                        ".layout-notification .el-icon"
                    ]
                    
                    for selector in notification_selectors:
                        try:
                            close_btn2 = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if close_btn2.is_displayed():
                                close_btn2.click()
                                logger.info(f"关闭文档管控公告弹窗 (选择器: {selector})")
                                current_popup_closed = True
                                popup_found = True
                                time.sleep(0.5)
                                break
                        except (NoSuchElementException, Exception):
                            continue
                except Exception:
                    pass
                
                if not current_popup_closed:
                    if popup_found and attempt < max_attempts - 1:
                        time.sleep(check_interval)
                        continue
                    elif not popup_found and attempt >= 3:
                        logger.info("连续多次未发现弹窗，提前结束检测")
                        break
                    else:
                        time.sleep(check_interval)
                else:
                    time.sleep(0.8)
                    
            logger.info("弹窗关闭流程完成")
            
        except Exception as e:
            logger.error(f"关闭弹窗失败: {str(e)}")
            
    def get_file_pairs(self):
        """获取文件对（源文件和PDF文件）"""
        if not self.documents_folder.exists():
            logger.error(f"文件夹不存在: {self.documents_folder}")
            return []
        
        files = list(self.documents_folder.glob("*"))
        file_pairs = {}
        
        for file in files:
            if file.is_file():
                # 提取文档编号 - 修改正则表达式以正确提取文档编号
                # 文档编号格式：HYHB_FN_A19-000011, HC2_PPL_A19-000001, SY_DVP_A19-000056
                filename = file.name
                doc_id_match = re.match(r'^([A-Z0-9]+_[A-Z]+_[A-Z0-9]+-\d+)', filename)
                if doc_id_match:
                    doc_id = doc_id_match.group(1)
                    if doc_id not in file_pairs:
                        file_pairs[doc_id] = {}
                    
                    if filename.lower().endswith('.pdf'):
                        file_pairs[doc_id]['pdf'] = file
                    else:
                        file_pairs[doc_id]['source'] = file
        
        # 只返回同时有源文件和PDF文件的文档编号
        valid_pairs = []
        for doc_id, files in file_pairs.items():
            if 'source' in files and 'pdf' in files:
                valid_pairs.append({
                    'doc_id': doc_id,
                    'source_file': files['source'],
                    'pdf_file': files['pdf']
                })
            else:
                logger.warning(f"文档 {doc_id} 缺少源文件或PDF文件")
        
        logger.info(f"找到 {len(valid_pairs)} 对有效文件")
        return valid_pairs
        
    def get_file_type(self, doc_id):
        """从文档编号识别文件类型"""
        if 'DVP' in doc_id.upper():
            return 'DVP'
        elif 'PPL' in doc_id.upper():
            return 'PPL'
        elif 'FN' in doc_id.upper():
            return 'FN'
        else:
            logger.warning(f"无法识别文档类型: {doc_id}")
            return 'FN'  # 默认使用FN类型
            
    def get_personnel_data(self):
        """读取人员数据"""
        try:
            xlsx_files = list(self.data_folder.glob("*.xlsx"))
            if not xlsx_files:
                logger.error("未找到Excel数据文件")
                return {}
            
            # 使用修改日期最新的文件
            latest_file = max(xlsx_files, key=lambda x: x.stat().st_mtime)
            logger.info(f"使用数据文件: {latest_file}")
            
            # 使用openpyxl读取以获取扩展的邮箱列
            import openpyxl
            wb = openpyxl.load_workbook(latest_file)
            # 使用第一个工作表
            ws = wb.worksheets[0]
            
            personnel_data = {}
            
            # 遍历每一行（跳过标题行）
            for row_idx in range(2, ws.max_row + 1):  # 从第2行开始
                role_cn = ws.cell(row=row_idx, column=1).value  # A列：中文角色名
                role_en = ws.cell(row=row_idx, column=2).value  # B列：英文角色标识
                people = ws.cell(row=row_idx, column=3).value   # C列：人员姓名
                
                if not role_en or pd.isna(people):
                    continue
                
                # 解析人员姓名（支持多种分隔符：逗号、空格、分号等）
                import re
                people_str = str(people).strip()
                # 使用正则表达式分割，支持中文逗号、英文逗号、空格、分号、中文分号等分隔符
                names = re.split(r'[，,\s;；]+', people_str)
                names = [name.strip() for name in names if name.strip()]
                
                # 收集邮箱（从D列开始，横向扩展）
                emails = []
                for col_idx in range(4, ws.max_column + 1):  # 从D列开始
                    email = ws.cell(row=row_idx, column=col_idx).value
                    if email and pd.notna(email) and '@' in str(email):
                        emails.append(str(email).strip())
                    elif email and pd.notna(email) and str(email).isdigit():
                        # 如果是数字，可能是工号（如数据管理员）
                        emails.append(str(email).strip())
                
                # 创建人员列表
                personnel_list = []
                for i, name in enumerate(names):
                    person_data = {'name': name}
                    
                    if i < len(emails):
                        email_or_id = emails[i]
                        if '@' in email_or_id:
                            person_data['email'] = email_or_id
                            person_data['job_id'] = ''
                        else:
                            person_data['email'] = ''
                            person_data['job_id'] = email_or_id
                    else:
                        person_data['email'] = ''
                        person_data['job_id'] = ''
                    
                    personnel_list.append(person_data)
                
                if personnel_list:
                    personnel_data[role_en] = personnel_list
                    logger.info(f"加载角色 {role_en} ({role_cn}): {len(personnel_list)} 人")
            
            logger.info(f"成功加载 {len(personnel_data)} 个角色的人员数据")
            return personnel_data
            
        except Exception as e:
            logger.error(f"读取人员数据失败: {str(e)}")
            return {}
            
    def click_document_create(self):
        """点击文档创建按钮"""
        try:
            # 多种定位方式尝试点击文档创建
            selectors = [
                "//span[contains(text(), '文档创建')]",
                "//*[@id='main']/div/div/div/div/div/div/span",
                "//div[contains(@class, 'DocCreate')]//span",
                "xpath=//div/div/div/div/span"
            ]
            
            for selector in selectors:
                try:
                    if selector.startswith('xpath='):
                        element = self.wait.until(EC.element_to_be_clickable(
                            (By.XPATH, selector[6:])
                        ))
                    else:
                        element = self.wait.until(EC.element_to_be_clickable(
                            (By.XPATH, selector)
                        ))
                    element.click()
                    logger.info(f"成功点击文档创建按钮 (选择器: {selector})")
                    return True
                except:
                    continue
            
            logger.error("无法找到文档创建按钮")
            return False
            
        except Exception as e:
            logger.error(f"点击文档创建失败: {str(e)}")
            return False
            
    def fill_document_info(self, doc_id, source_file, file_type):
        """填写文档信息（第一个页面）"""
        try:
            logger.info(f"开始填写文档信息: {doc_id}")
            time.sleep(self.config['OPERATION_DELAY'])
            
            # 1. 填写文档编号 - 这是一个可搜索的下拉选择框
            logger.info("开始填写文档编号...")
            doc_number_selectors = [
                # 只保留有效的选择器
                "//div[2]/div/div/div/div/input"
            ]
            
            doc_input_success = False
            for i, selector in enumerate(doc_number_selectors):
                try:
                    logger.info(f"尝试文档编号选择器 {i+1}/{len(doc_number_selectors)}")
                    doc_number_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    doc_number_field.click()
                    time.sleep(1)
                    doc_number_field.clear()
                    doc_number_field.send_keys(doc_id)
                    logger.info(f"已输入文档编号: {doc_id}")
                    time.sleep(2)
                    
                    # 尝试点击下拉选项
                    dropdown_selectors = [
                        f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{doc_id}')]",
                        f"//div[contains(@class, 'el-select-dropdown')]//span[contains(text(), '{doc_id}')]",
                        f"//ul//li//span[contains(text(), '{doc_id}')]",
                        f"//*[contains(@id, 'el-id-') and contains(text(), '{doc_id}')]/span"
                    ]
                    
                    dropdown_clicked = False
                    for j, dropdown_selector in enumerate(dropdown_selectors):
                        try:
                            dropdown_item = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, dropdown_selector)
                            ))
                            dropdown_item.click()
                            logger.info(f"成功点击文档编号下拉选项")
                            dropdown_clicked = True
                            break
                        except:
                            continue
                    
                    if not dropdown_clicked:
                        # 按回车确认
                        doc_number_field.send_keys(Keys.ENTER)
                        time.sleep(1)
                    
                    doc_input_success = True
                    break
                    
                except Exception as e:
                    logger.warning(f"文档编号选择器 {i+1} 失败: {str(e)}")
                    continue
            
            if not doc_input_success:
                logger.error("文档编号填写失败")
                return False
            
            # 2. 填写项目阶段
            logger.info("开始填写项目阶段...")
            stage_selectors = [
                "//input[contains(@id, '1627')]",  # 基于JSON中的ID
                "//label[contains(text(), '阶段')]/following-sibling::div//input",
                "//div[5]/div/div[2]/div/div/div/div/input"
            ]
            
            for i, selector in enumerate(stage_selectors):
                try:
                    stage_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    stage_field.click()
                    time.sleep(1)
                    stage_field.clear()
                    stage_field.send_keys("B版")
                    logger.info("已输入项目阶段: B版")
                    time.sleep(1)
                    break
                except Exception as e:
                    logger.warning(f"项目阶段选择器 {i+1} 失败: {str(e)}")
                    continue
            
            # 3. 填写文档名称
            doc_name = source_file.stem.replace(doc_id, "").strip("-_").strip()
            if doc_name:
                logger.info("开始填写文档名称...")
                name_selectors = [
                    "//label[contains(text(), '文档名称')]/following-sibling::div//input",
                    "//input[@maxlength='100']"
                ]
                
                for selector in name_selectors:
                    try:
                        name_field = self.wait.until(EC.element_to_be_clickable(
                            (By.XPATH, selector)
                        ))
                        name_field.click()
                        time.sleep(0.5)
                        name_field.clear()
                        name_field.send_keys(doc_name)
                        logger.info(f"已输入文档名称: {doc_name}")
                        break
                    except Exception as e:
                        logger.warning(f"文档名称填写失败: {str(e)}")
                        continue
            
            # 4. 填写文件内容简要
            logger.info("开始填写文件内容简要...")
            summary = f"{file_type}类型文档，编号：{doc_id}"
            summary_selectors = [
                "//label[contains(text(), '内容简要')]/following-sibling::div//textarea",
                "//textarea[contains(@placeholder, '请输入内容')]",
                "//textarea[@maxlength='1000']"
            ]
            
            for selector in summary_selectors:
                try:
                    summary_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    summary_field.click()
                    time.sleep(0.5)
                    summary_field.clear()
                    summary_field.send_keys(summary)
                    logger.info(f"已输入文件内容简要: {summary}")
                    break
                except Exception as e:
                    logger.warning(f"内容简要填写失败: {str(e)}")
                    continue
            
            logger.info("文档信息填写完成")
            return True
            
        except Exception as e:
            logger.error(f"填写文档信息失败: {str(e)}")
            return False
            
        except Exception as e:
            logger.error(f"填写文档信息失败: {str(e)}")
            return False
            
    def upload_files(self, source_file, pdf_file):
        """上传文件"""
        try:
            logger.info("开始上传文件...")
            
            # 1. 上传源文件
            try:
                source_upload_input = self.wait.until(EC.presence_of_element_located(
                    (By.XPATH, "//input[@type='file' and @name='file'][1]")
                ))
                source_upload_input.send_keys(str(source_file.absolute()))
                logger.info(f"已上传源文件: {source_file.name}")
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"源文件上传失败: {str(e)}")
                return False
            
            # 2. 删除自动转换的PDF（如果存在）
            try:
                delete_btns = self.driver.find_elements(
                    By.XPATH, 
                    "//button[contains(@class, 'el-button') and .//i[contains(@class, 'el-icon')]]//span[contains(@class, 'el-icon')]"
                )
                for btn in delete_btns:
                    try:
                        btn.click()
                        logger.info("已删除自动转换的PDF")
                        time.sleep(1)
                        break
                    except:
                        continue
            except Exception as e:
                logger.warning(f"删除自动转换PDF失败: {str(e)}")
            
            time.sleep(2)
            
            # 3. 上传PDF文件
            try:
                pdf_upload_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file' and @name='file']")
                # 通常第二个input是PDF上传
                if len(pdf_upload_inputs) >= 2:
                    pdf_upload_inputs[1].send_keys(str(pdf_file.absolute()))
                else:
                    # 如果只有一个，重新查找
                    pdf_upload_input = self.wait.until(EC.presence_of_element_located(
                        (By.XPATH, "//input[@type='file' and @name='file']")
                    ))
                    pdf_upload_input.send_keys(str(pdf_file.absolute()))
                    
                logger.info(f"已上传PDF文件: {pdf_file.name}")
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"PDF文件上传失败: {str(e)}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return False
            
    def save_and_start_review(self):
        """保存并发起评审"""
        try:
            # 点击保存并发起评审按钮
            save_review_selectors = [
                "//button[contains(.//span, '保存并发起评审')]",
                "//span[contains(text(), '保存并发起评审')]/ancestor::button",
                "//*[@id='pane-docInfo']//button[contains(.//span, '保存并发起评审')]"
            ]
            
            for selector in save_review_selectors:
                try:
                    save_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    save_btn.click()
                    logger.info("已点击保存并发起评审")
                    time.sleep(3)
                    return True
                except:
                    continue
            
            logger.error("无法找到保存并发起评审按钮")
            return False
            
        except Exception as e:
            logger.error(f"保存并发起评审失败: {str(e)}")
            return False
            
    def select_review_scheme(self, file_type):
        """选择评审人方案"""
        try:
            logger.info(f"为{file_type}类型选择评审人方案...")
            
            # 根据文件类型选择不同的评审方案
            if file_type == 'DVP':
                # DVP可能需要选择特定方案
                try:
                    scheme_dropdown = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(@class, 'el-select')]//i[contains(@class, 'el-select__caret')]")
                    ))
                    scheme_dropdown.click()
                    time.sleep(1)
                    
                    # 选择包含系统专家的方案
                    dvp_scheme = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(), '部门相关方') or contains(text(), '系统专家')]")
                    ))
                    dvp_scheme.click()
                    logger.info("已选择DVP评审方案")
                    
                except Exception as e:
                    logger.warning(f"DVP评审方案选择失败: {str(e)}")
                    
            elif file_type == 'FN':
                # FN选择包含数据管理员、科长、相关方、项目主管的方案
                try:
                    scheme_dropdown = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(@class, 'el-select')]//i[contains(@class, 'el-select__caret')]")
                    ))
                    scheme_dropdown.click()
                    time.sleep(1)
                    
                    fn_scheme = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(), '数据管理员、科长、相关方、项目主管')]")
                    ))
                    fn_scheme.click()
                    logger.info("已选择FN评审方案")
                    
                except Exception as e:
                    logger.warning(f"FN评审方案选择失败: {str(e)}")
                    
            elif file_type == 'PPL':
                # PPL选择包含数据管理员、科长、相关方的方案
                try:
                    scheme_dropdown = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//div[contains(@class, 'el-select')]//i[contains(@class, 'el-select__caret')]")
                    ))
                    scheme_dropdown.click()
                    time.sleep(1)
                    
                    ppl_scheme = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//span[contains(text(), '数据管理员、科长、相关方')]")
                    ))
                    ppl_scheme.click()
                    logger.info("已选择PPL评审方案")
                    
                except Exception as e:
                    logger.warning(f"PPL评审方案选择失败: {str(e)}")
            
            time.sleep(2)
            return True
            
        except Exception as e:
            logger.error(f"选择评审人方案失败: {str(e)}")
            return False
            
    def fill_reviewers(self, file_type, personnel_data):
        """填写评审人"""
        try:
            logger.info(f"开始填写{file_type}类型的评审人...")
            
            # 获取所有评审角色行
            role_rows = self.driver.find_elements(
                By.XPATH, 
                "//table[@class]//tbody//tr"
            )
            
            for row in role_rows:
                try:
                    # 获取角色名称
                    role_element = row.find_element(By.XPATH, ".//td[2]//span")
                    role_name = role_element.text.strip()
                    logger.info(f"处理角色: {role_name}")
                    
                    # 获取对应的输入框
                    input_field = row.find_element(By.XPATH, ".//td[3]//input")
                    
                    # 根据角色名称和文件类型获取对应人员列表
                    personnel_list = self.get_personnel_for_role(role_name, file_type, personnel_data)
                    
                    if not personnel_list:
                        logger.warning(f"角色 {role_name} 未找到对应人员")
                        continue
                    
                    # 填写多个人员
                    for i, person in enumerate(personnel_list):
                        if i > 0:
                            # 多个人员时，点击输入框右侧空白处再添加
                            # 根据JSON描述：点击填写框靠右一点没有内容的位置
                            try:
                                ActionChains(self.driver).move_to_element_with_offset(
                                    input_field, input_field.size['width'] - 20, 0
                                ).click().perform()
                                time.sleep(1)
                                logger.info(f"点击输入框右侧准备添加第{i+1}个人员")
                            except Exception as e:
                                logger.warning(f"点击输入框右侧失败: {str(e)}")
                                # 如果右侧点击失败，尝试直接点击输入框
                                input_field.click()
                                time.sleep(1)
                        
                        # 输入邮箱或工号进行搜索
                        search_key = person.get('email', '') or person.get('job_id', '')
                        if search_key:
                            input_field.send_keys(search_key)
                            logger.info(f"已输入搜索关键字: {search_key}")
                            time.sleep(2)
                            
                            # 点击浮动出现的人员选项
                            try:
                                # 尝试多种选择器来找到下拉选项
                                person_selectors = [
                                    f"//div[contains(text(), '{person['name']}')]",
                                    f"//div[contains(text(), '{search_key}')]",
                                    f"//li[contains(text(), '{person['name']}')]",
                                    f"//li[contains(text(), '{search_key}')]",
                                    "//div[contains(@class, 'el-select-dropdown')]//li[1]",  # 选择第一个选项
                                    "//ul//li[1]"  # 通用的第一个选项
                                ]
                                
                                person_selected = False
                                for selector in person_selectors:
                                    try:
                                        person_option = self.wait.until(EC.element_to_be_clickable(
                                            (By.XPATH, selector)
                                        ))
                                        person_option.click()
                                        logger.info(f"已选择人员: {person['name']} (选择器: {selector})")
                                        person_selected = True
                                        time.sleep(1)
                                        break
                                    except:
                                        continue
                                
                                if not person_selected:
                                    logger.warning(f"未能选择人员: {person['name']}")
                                    # 按ESC取消当前输入
                                    input_field.send_keys(Keys.ESCAPE)
                                    time.sleep(1)
                                
                            except Exception as e:
                                logger.warning(f"选择人员失败: {str(e)}")
                        else:
                            logger.warning(f"人员 {person['name']} 没有邮箱或工号信息")
                    
                    logger.info(f"角色 {role_name} 填写完成，共填写 {len(personnel_list)} 人")
                
                except Exception as e:
                    logger.warning(f"处理角色行失败: {str(e)}")
                    continue
            
            return True
            
        except Exception as e:
            logger.error(f"填写评审人失败: {str(e)}")
            return False
            
    def get_personnel_for_role(self, role_name, file_type, personnel_data):
        """根据角色名称和文件类型获取对应人员"""
        # 基于Excel中的实际role字段进行映射
        role_mappings = {
            'DVP': {
                '部门相关方': 'DVP_Signatory',
                '相关方': 'DVP_Signatory', 
                '系统专家': 'Experts',
                '专家': 'Experts',
                '项目主管': 'ChassisProjectManager',
                '车型品质主管': 'V_QualityManager',
                '研发品质经理': 'RD_QualityManager',
                '数据管理员': 'Data_Manager'
            },
            'FN': {
                '数据管理员': 'Data_Manager',
                '科长': 'SectionHead',
                '相关方': 'FN_Stakeholder',
                '项目主管': 'ChassisProjectManager'
            },
            'PPL': {
                '相关方': 'PPL_Signatory',
                '数据管理员': 'Data_Manager', 
                '科长': 'SectionHead'
            }
        }
        
        # 先尝试直接映射
        if file_type in role_mappings and role_name in role_mappings[file_type]:
            excel_role = role_mappings[file_type][role_name]
            personnel_list = personnel_data.get(excel_role, [])
            if personnel_list:
                logger.info(f"找到角色 {role_name} -> {excel_role}: {len(personnel_list)} 人")
                return personnel_list
        
        # 如果直接映射失败，尝试根据FN的具体系统类型查找
        if file_type == 'FN' and '相关方' in role_name:
            # 尝试匹配具体的FN系统相关方
            fn_system_mappings = {
                'IMU': 'FN_IMU',
                'VCU': 'FN_VCU', 
                'IPB': 'FN_IPB',
                'ESP': 'FN_ESP_BWA',
                'BWA': 'FN_ESP_BWA',
                'EPS': 'FN_EPS',
                'EPSA': 'FN_EPSA',
                'EPB': 'FN_EPB',
                'DiSus_A': 'FN_DiSus_A',
                'DiSus_C': 'FN_DiSus_C', 
                'DiSus_P': 'FN_DiSus_P',
                'DiSus_X': 'FN_DiSus_X',
                'DiSus_M': 'FN_DiSus_M',
                '域控': 'FN_yukong'
            }
            
            for system, excel_role in fn_system_mappings.items():
                if system.lower() in role_name.lower():
                    personnel_list = personnel_data.get(excel_role, [])
                    if personnel_list:
                        logger.info(f"找到FN系统角色 {role_name} -> {excel_role}: {len(personnel_list)} 人")
                        return personnel_list
        
        # 如果还是找不到，记录警告并返回空列表
        logger.warning(f"未找到角色映射: {role_name} (文件类型: {file_type})")
        logger.info(f"可用的角色: {list(personnel_data.keys())}")
        
        return []
        
    def fill_deadline(self):
        """填写截止日期"""
        try:
            # 计算一周后的日期
            deadline = datetime.now() + timedelta(days=7)
            deadline_str = deadline.strftime("%Y-%m-%d")
            
            # 查找截止日期输入框
            deadline_selectors = [
                "//input[contains(@placeholder, '日期')]",
                "//label[contains(text(), '截止日期')]/..//input",
                "//input[@type='text' and contains(@class, 'el-input__inner')]"
            ]
            
            for selector in deadline_selectors:
                try:
                    deadline_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    deadline_field.click()
                    deadline_field.clear()
                    deadline_field.send_keys(deadline_str)
                    logger.info(f"已填写截止日期: {deadline_str}")
                    
                    # 按ESC或点击其他地方关闭日期选择器
                    deadline_field.send_keys(Keys.ESCAPE)
                    time.sleep(1)
                    return True
                    
                except Exception as e:
                    logger.warning(f"截止日期填写尝试失败: {str(e)}")
                    continue
            
            logger.warning("无法找到截止日期输入框")
            return False
            
        except Exception as e:
            logger.error(f"填写截止日期失败: {str(e)}")
            return False
            
    def submit_review(self):
        """提交评审"""
        try:
            if self.config['TEST_MODE']:
                logger.info("测试模式开启，不会真正提交")
                time.sleep(5)  # 等待5秒模拟提交过程
                return True
            
            # 查找提交按钮
            submit_selectors = [
                "//button[contains(.//span, '提交')]",
                "//span[contains(text(), '提交')]/ancestor::button"
            ]
            
            for selector in submit_selectors:
                try:
                    submit_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    submit_btn.click()
                    logger.info("已点击提交按钮")
                    time.sleep(3)
                    return True
                    
                except Exception as e:
                    logger.warning(f"提交尝试失败: {str(e)}")
                    continue
            
            logger.error("无法找到提交按钮")
            return False
            
        except Exception as e:
            logger.error(f"提交评审失败: {str(e)}")
            return False
            
    def return_to_main_page(self):
        """返回主页面"""
        try:
            # 等待一段时间后返回主页
            time.sleep(5)
            
            # 尝试点击返回按钮或直接导航到主页
            return_selectors = [
                "//a[contains(.//span, '返回上一级')]",
                "//i[contains(@class, 'el-icon')]/../span[contains(text(), '返回')]",
                "//button[contains(text(), '返回')]"
            ]
            
            for selector in return_selectors:
                try:
                    return_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    return_btn.click()
                    logger.info("已点击返回按钮")
                    time.sleep(3)
                    break
                except:
                    continue
            else:
                # 如果没有返回按钮，直接导航到主页
                self.driver.get(self.config['DMS_URL'])
                logger.info("直接导航回主页")
                time.sleep(3)
            
            # 重新登录（如果需要）
            try:
                login_btn = self.driver.find_element(
                    By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span"
                )
                if login_btn.is_displayed():
                    login_btn.click()
                    logger.info("重新点击登录")
                    time.sleep(3)
            except:
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"返回主页面失败: {str(e)}")
            return False
            
    def process_single_document(self, file_pair, personnel_data):
        """处理单个文档的上传审批"""
        try:
            doc_id = file_pair['doc_id']
            source_file = file_pair['source_file']
            pdf_file = file_pair['pdf_file']
            file_type = self.get_file_type(doc_id)
            
            logger.info(f"开始处理文档: {doc_id} (类型: {file_type})")
            
            # 1. 点击文档创建
            if not self.click_document_create():
                return False
            
            # 2. 填写文档信息
            if not self.fill_document_info(doc_id, source_file, file_type):
                return False
            
            # 3. 上传文件
            if not self.upload_files(source_file, pdf_file):
                return False
            
            # 4. 保存并发起评审
            if not self.save_and_start_review():
                return False
            
            # 等待进入评审页面
            time.sleep(5)
            
            # 5. 选择评审人方案
            if not self.select_review_scheme(file_type):
                return False
            
            # 6. 填写评审人
            if not self.fill_reviewers(file_type, personnel_data):
                return False
            
            # 7. 填写截止日期
            if not self.fill_deadline():
                return False
            
            # 8. 提交评审
            if not self.submit_review():
                return False
            
            # 9. 返回主页面
            if not self.return_to_main_page():
                return False
            
            logger.info(f"文档 {doc_id} 处理完成")
            return True
            
        except Exception as e:
            logger.error(f"处理文档 {doc_id} 失败: {str(e)}")
            return False
            
    def run(self):
        """运行主程序"""
        try:
            logger.info("开始上传审批自动化程序")
            
            # 1. 设置浏览器
            self.setup_driver()
            
            # 2. 登录
            self.login()
            
            # 3. 关闭弹窗
            self.close_popups()
            
            # 4. 获取文件对
            file_pairs = self.get_file_pairs()
            if not file_pairs:
                logger.error("未找到有效的文件对")
                return
            
            # 5. 获取人员数据
            personnel_data = self.get_personnel_data()
            if not personnel_data:
                logger.warning("未能加载人员数据，可能影响评审人填写")
            
            # 6. 处理每个文档
            success_count = 0
            for file_pair in file_pairs:
                if self.process_single_document(file_pair, personnel_data):
                    success_count += 1
                else:
                    logger.error(f"文档 {file_pair['doc_id']} 处理失败")
                
                # 每个文档之间稍作休息
                time.sleep(2)
            
            logger.info(f"程序完成，成功处理 {success_count}/{len(file_pairs)} 个文档")
            
        except Exception as e:
            logger.error(f"程序运行失败: {str(e)}")
        finally:
            if self.driver:
                input("按回车键关闭浏览器...")
                self.driver.quit()


if __name__ == "__main__":
    automation = UploadApprovalAutomation()
    automation.run()
