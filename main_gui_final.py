import sys
import os
import shutil
import logging
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QMessageBox
)
from PyQt5.QtCore import Qt

# 导入GUI_Core模块
from GUI_Core.file_manager import FileManager
from GUI_Core.ui_components import UIComponents
from GUI_Core.file_operations import FileOperations
from GUI_Core.process_manager import ProcessManager
from GUI_Core.utility_functions import UtilityFunctions


class VehicleManagementGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("车型文件管理系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置路径
        self.base_path = Path(__file__).parent
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.fillin_path = self.base_path / "Fillin"
        self.update_templates_path = self.base_path / "Update_Templates"
        self.logs_path = self.base_path / "logs"
        
        # 创建必要的目录
        self.vehicles_path.mkdir(exist_ok=True)
        self.logs_path.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 初始化各种管理器
        self.ui_components = UIComponents(self)
        self.file_operations = FileOperations(self)
        self.process_manager = ProcessManager(self)
        self.utility_functions = UtilityFunctions(self)
        
        # 初始化UI
        self.init_ui()
        
        # 设置样式
        self.utility_functions.setup_style()
        
        # 当前选择的车型代号
        self.current_vehicle_code = ""
        
        # 初始化车型列表
        self.utility_functions.load_vehicle_codes()

    def setup_logging(self):
        """设置日志"""
        log_file = self.logs_path / f"vehicle_management_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题栏（包含OA登录设置）
        self.ui_components.create_title_bar(main_layout)
        
        # 创建主要工作区域
        self.ui_components.create_main_workspace(main_layout)
        
        # 创建状态栏
        self.ui_components.create_status_bar()

    # 车型管理相关方法
    def on_vehicle_changed(self, vehicle_code):
        """车型代号改变时的处理"""
        self.utility_functions.on_vehicle_changed(vehicle_code)

    def setup_vehicle_info(self):
        """设置车型信息"""
        vehicle_code = self.vehicle_combo.currentText().strip()
        if not vehicle_code:
            QMessageBox.warning(self, "警告", "请输入车型代号!")
            return
        
        try:
            from GUI_Core.file_manager import FileManager
            file_manager = FileManager(self.base_path)
            created, target_file = file_manager.setup_vehicle_folder(vehicle_code)
            
            if created:
                QMessageBox.information(self, "成功", f"车型 {vehicle_code} 设置完成!\n配置文件: {target_file}")
                self.logger.info(f"设置车型: {vehicle_code}")
            else:
                QMessageBox.information(self, "提示", f"车型 {vehicle_code} 已存在!\n配置文件: {target_file}")
            
            # 更新车型列表和当前选择
            self.utility_functions.load_vehicle_codes()
            self.vehicle_combo.setCurrentText(vehicle_code)
            self.current_vehicle_code = vehicle_code
            self.utility_functions.load_vehicle_file_status()
            
        except Exception as e:
            self.logger.error(f"设置车型失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"设置车型失败: {str(e)}")

    def load_vehicle_file_status(self):
        """加载车型文件状态"""
        self.utility_functions.load_vehicle_file_status()

    # 文件选择相关方法
    def select_all_files(self):
        """全选文件"""
        self.utility_functions.select_all_files()

    def deselect_all_files(self):
        """取消全选文件"""
        self.utility_functions.deselect_all_files()

    # 文件操作相关方法
    def execute_file_operations(self):
        """执行文件操作"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        # 检查登录信息
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        # 收集选中的文件类型
        selected_files = []
        
        if self.dvp_cb.isChecked():
            selected_files.append("DVP")
        if self.ppl_cb.isChecked():
            selected_files.append("PPL")
        
        for file_type, checkbox in self.fn_checkboxes.items():
            if checkbox.isChecked():
                selected_files.append(file_type)
        
        if not selected_files:
            QMessageBox.warning(self, "警告", "请至少选择一种文件类型!")
            return
        
        try:
            # 禁用执行按钮，显示进度条
            self.execute_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 启动完整的文件处理流程
            self.process_manager.start_complete_file_processing(selected_files, username, password)
            
        except Exception as e:
            self.logger.error(f"执行文件操作失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行文件操作失败: {str(e)}")
            self.execute_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

    # 上传审批相关方法
    def upload_approval_files(self):
        """上传审批文件"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        # 检查登录信息
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        try:
            # 禁用上传按钮
            self.upload_approval_btn.setEnabled(False)
            self.upload_progress.setVisible(True)
            self.upload_progress.setValue(0)
            
            self.statusBar().showMessage("正在准备上传文件...")
            
            # 第一步：复制模板数据文件到根目录
            success = self.file_operations.copy_template_data_to_root()
            if not success:
                QMessageBox.warning(self, "警告", "无法找到车型数据文件，请先设置车型信息!")
                self.upload_approval_btn.setEnabled(True)
                self.upload_progress.setVisible(False)
                return
            
            self.upload_progress.setValue(20)
            
            # 第二步：检查审批文件夹并复制文件
            final_approval_dir = self.base_path / "Final_Approval_Documents"
            if not final_approval_dir.exists() or not any(final_approval_dir.iterdir()):
                QMessageBox.warning(self, "警告",
                    "审批文件夹为空!\n\n"
                    "请先将要上传的文件放入Final_Approval_Documents文件夹中")
                self.upload_approval_btn.setEnabled(True)
                self.upload_progress.setVisible(False)
                return

            # 复制文件到车型文件夹的Final_Files子文件夹
            vehicle_final_files_dir = self.vehicles_path / self.current_vehicle_code / "Final_Files"
            vehicle_final_files_dir.mkdir(parents=True, exist_ok=True)

            # 复制文件到Upload/Final_Approval_Documents（先清空）
            upload_final_approval_dir = self.base_path / "Upload" / "Final_Approval_Documents"
            self.file_operations.clear_directory(upload_final_approval_dir)

            # 执行文件复制
            copied_files = []
            for file_path in final_approval_dir.iterdir():
                if file_path.is_file():
                    # 复制到车型文件夹的Final_Files
                    target_file_1 = vehicle_final_files_dir / file_path.name
                    shutil.copy2(file_path, target_file_1)

                    # 复制到Upload/Final_Approval_Documents
                    target_file_2 = upload_final_approval_dir / file_path.name
                    shutil.copy2(file_path, target_file_2)

                    copied_files.append(file_path.name)
                    self.logger.info(f"已复制审批文件: {file_path.name}")

            if copied_files:
                self.logger.info(f"成功复制 {len(copied_files)} 个审批文件到目标位置")
            else:
                QMessageBox.warning(self, "警告", "审批文件夹中没有找到有效文件!")
                self.upload_approval_btn.setEnabled(True)
                self.upload_progress.setVisible(False)
                return

            self.upload_progress.setValue(40)
            self.statusBar().showMessage("正在配置Upload模块...")
            
            # 第三步：配置Upload模块
            self.process_manager.configure_upload_config(username, password)
            
            self.upload_progress.setValue(60)
            self.statusBar().showMessage("正在执行上传...")
            
            # 第四步：运行上传进程
            self.process_manager.run_upload_process()
            
        except Exception as e:
            self.logger.error(f"上传审批失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"上传审批失败: {str(e)}")
            self.upload_approval_btn.setEnabled(True)
            self.upload_progress.setVisible(False)

    # 文件夹打开相关方法
    def open_numbered_filled_folder(self):
        """打开编号填写后的文件夹"""
        self.utility_functions.open_numbered_filled_folder()

    def open_final_approval_folder(self):
        """打开最终审批文件夹"""
        self.utility_functions.open_final_approval_folder()

    def open_vehicles_folder(self):
        """打开车型文件夹"""
        self.utility_functions.open_vehicles_folder()

    def open_templates_folder(self):
        """打开模板文件夹"""
        self.utility_functions.open_templates_folder()

    def open_logs_folder(self):
        """打开日志文件夹"""
        self.utility_functions.open_logs_folder()

    def open_main_log(self):
        """打开主程序日志"""
        self.utility_functions.open_main_log()

    def open_subprocess_log(self):
        """打开子进程日志"""
        self.utility_functions.open_subprocess_log()

    def open_update_templates_folder(self):
        """打开更新模板文件夹"""
        self.utility_functions.open_update_templates_folder()

    def replace_templates(self):
        """替换模板"""
        self.utility_functions.replace_templates()

    def open_operation_log(self):
        """打开操作日志"""
        self.utility_functions.open_operation_log()

    def open_admin_list_image(self):
        """打开数据管理员名单图片"""
        self.utility_functions.open_admin_list_image()

    # 状态栏相关方法
    def update_time(self):
        """更新状态栏时间"""
        self.utility_functions.update_time()

    def closeEvent(self, event):
        """程序关闭时的处理"""
        self.utility_functions.close_event_handler(event)


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("车型文件管理系统")
    app.setApplicationVersion("1.0.0")
    
    window = VehicleManagementGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
