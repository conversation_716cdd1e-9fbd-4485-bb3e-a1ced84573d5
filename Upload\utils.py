"""
通用工具模块
包含文件操作、数据处理、日志配置等通用功能
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pandas as pd
from datetime import datetime, timedelta
import shutil


def check_and_rotate_log(log_file: str = 'upload_approval.log', max_size_mb: float = 5, max_backups: int = 3):
    """
    检查并轮转日志文件
    
    Args:
        log_file: 日志文件路径
        max_size_mb: 最大文件大小(MB)
        max_backups: 保留的备份数量
    """
    if not os.path.exists(log_file):
        return
    
    # 检查文件大小
    file_size = os.path.getsize(log_file)
    max_size = max_size_mb * 1024 * 1024
    
    if file_size > max_size:
        # 创建备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{log_file}.{timestamp}"
        
        try:
            # 轮转日志
            shutil.move(log_file, backup_file)
            
            # 创建新日志文件
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 日志轮转 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            
            print(f"日志文件已轮转: {log_file} -> {backup_file}")
            
            # 清理旧备份
            _clean_old_log_backups(log_file, max_backups)
            
        except Exception as e:
            print(f"日志轮转失败: {e}")


def _clean_old_log_backups(log_file: str, max_backups: int):
    """清理旧的日志备份文件"""
    dir_path = os.path.dirname(log_file) or "."
    base_name = os.path.basename(log_file)
    
    # 找到所有备份文件
    backup_files = []
    for file in os.listdir(dir_path):
        if file.startswith(base_name + ".") and file != base_name:
            file_path = os.path.join(dir_path, file)
            if os.path.isfile(file_path):
                mtime = os.path.getmtime(file_path)
                backup_files.append((file_path, mtime))
    
    # 按时间排序（最新的在前）
    backup_files.sort(key=lambda x: x[1], reverse=True)
    
    # 删除超过数量限制的备份
    if len(backup_files) > max_backups:
        for file_path, _ in backup_files[max_backups:]:
            try:
                os.remove(file_path)
                print(f"删除旧备份: {file_path}")
            except Exception as e:
                print(f"删除备份失败: {e}")


def setup_logging(log_file: str = 'upload_approval.log') -> logging.Logger:
    """设置日志配置，包含自动轮转功能"""
    # 在设置日志前检查并轮转
    check_and_rotate_log(log_file)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class FileProcessor:
    """文件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_document_id(self, filename: str) -> Optional[str]:
        """
        从文件名中提取文档编号
        使用正则表达式提取标准格式的编号，如：HYHB_FN_A19-000011、HC2_PPL_A19-000001
        """
        # 使用优化的正则表达式，只提取标准编号
        pattern = r'^([A-Z0-9]+_[A-Z]+_[A-Z0-9]+-\d+)'
        
        match = re.search(pattern, filename)
        if match:
            doc_id = match.group(1)
            self.logger.info(f"从文件名 '{filename}' 提取文档编号: {doc_id}")
            return doc_id
        else:
            self.logger.warning(f"无法从文件名 '{filename}' 提取文档编号")
            return None
    
    def identify_file_type(self, filename: str) -> str:
        """根据文件名识别文件类型"""
        filename_lower = filename.lower()
        
        if "dvp" in filename_lower or "设计验证计划" in filename:
            return "DVP"
        elif "ppl" in filename_lower or "匹配" in filename:
            return "PPL"
        elif any(keyword in filename for keyword in ["接口定义", "通知单", "FN", "fn"]):
            return "FN"
        else:
            # 默认为FN类型
            return "FN"
    
    def scan_files_for_processing(self, documents_folder: Path) -> Dict[str, List[Dict]]:
        """
        扫描文件夹，获取需要处理的文件信息
        返回按文件类型分组的文件列表
        """
        if not documents_folder.exists():
            self.logger.error(f"文档文件夹不存在: {documents_folder}")
            return {}
        
        files_by_type = {"DVP": [], "PPL": [], "FN": []}
        
        # 支持的文件扩展名
        supported_extensions = ['.docx', '.xlsx', '.pdf', '.doc', '.xls']
        
        for file_path in documents_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                # 提取文档编号
                doc_id = self.extract_document_id(file_path.name)
                if not doc_id:
                    self.logger.warning(f"跳过文件 {file_path.name}（无法提取文档编号）")
                    continue
                
                # 识别文件类型
                file_type = self.identify_file_type(file_path.name)
                
                file_info = {
                    'path': file_path,
                    'name': file_path.name,
                    'doc_id': doc_id,
                    'type': file_type
                }
                
                files_by_type[file_type].append(file_info)
                self.logger.info(f"添加文件: {file_path.name} -> {file_type} ({doc_id})")
        
        # 统计信息
        total_files = sum(len(files) for files in files_by_type.values())
        self.logger.info(f"文件扫描完成，共找到 {total_files} 个文件:")
        for file_type, files in files_by_type.items():
            self.logger.info(f"  {file_type}: {len(files)} 个文件")
        
        return files_by_type
    
    def get_document_pairs(self, files_by_type: Dict[str, List[Dict]]) -> List[Tuple[Dict, Dict]]:
        """
        获取文档对配对逻辑：
        1. 源文件(docx/xlsx) + PDF配对
        2. 源文件单独处理
        3. PDF单独处理
        """
        document_pairs = []
        
        for file_type, files in files_by_type.items():
            # 按文档编号分组
            files_by_doc_id = {}
            for file_info in files:
                doc_id = file_info['doc_id']
                if doc_id not in files_by_doc_id:
                    files_by_doc_id[doc_id] = []
                files_by_doc_id[doc_id].append(file_info)
            
            # 处理每个文档编号的文件
            for doc_id, doc_files in files_by_doc_id.items():
                if len(doc_files) == 1:
                    # 单个文件
                    document_pairs.append((doc_files[0], None))
                    self.logger.info(f"找到单个文档: {doc_id} ({doc_files[0]['name']})")
                else:
                    # 多个文件，进行配对
                    source_files = []  # 源文件 (docx, doc, xlsx, xls)
                    pdf_files = []     # PDF文件
                    
                    for file_info in doc_files:
                        suffix = file_info['path'].suffix.lower()
                        if suffix in ['.docx', '.doc', '.xlsx', '.xls']:
                            source_files.append(file_info)
                        elif suffix == '.pdf':
                            pdf_files.append(file_info)
                    
                    # 配对逻辑：源文件+PDF
                    paired_pdf_indices = set()  # 使用索引而不是字典对象
                    
                    for source_file in source_files:
                        # 为每个源文件找对应的PDF
                        paired_pdf = None
                        for i, pdf_file in enumerate(pdf_files):
                            if i not in paired_pdf_indices and self._is_same_document(source_file['name'], pdf_file['name']):
                                paired_pdf = pdf_file
                                paired_pdf_indices.add(i)
                                break
                        
                        if paired_pdf:
                            document_pairs.append((source_file, paired_pdf))
                            self.logger.info(f"找到源文件+PDF配对: {doc_id} ({source_file['name']} + {paired_pdf['name']})")
                        else:
                            document_pairs.append((source_file, None))
                            self.logger.info(f"找到源文件: {doc_id} ({source_file['name']})")
                    
                    # 处理未配对的PDF文件
                    for i, pdf_file in enumerate(pdf_files):
                        if i not in paired_pdf_indices:
                            document_pairs.append((pdf_file, None))
                            self.logger.info(f"找到独立PDF: {doc_id} ({pdf_file['name']})")
        
        return document_pairs
    
    def _is_same_document(self, source_name: str, pdf_name: str) -> bool:
        """判断源文件和PDF是否是同一个文档"""
        # 去掉扩展名进行比较
        source_stem = Path(source_name).stem
        pdf_stem = Path(pdf_name).stem
        
        # 比较去掉扩展名后的文件名是否相同或相似
        return source_stem == pdf_stem or source_stem in pdf_name or pdf_stem in source_name


class DataManager:
    """数据管理器"""
    
    def __init__(self, data_folder: Path):
        self.data_folder = data_folder
        self.logger = logging.getLogger(__name__)
    
    def load_personnel_data(self) -> Optional[pd.DataFrame]:
        """加载人员数据"""
        try:
            excel_file = self.data_folder / "Fill_Template_Data.xlsx"
            if not excel_file.exists():
                self.logger.error(f"人员数据文件不存在: {excel_file}")
                return None
            
            # 读取Excel文件
            df = pd.read_excel(excel_file, sheet_name=0)  # 读取第一个工作表
            self.logger.info(f"成功加载人员数据，共 {len(df)} 条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"加载人员数据失败: {str(e)}")
            return None
    
    def get_reviewers_by_type(self, file_type: str, personnel_df: pd.DataFrame) -> List[str]:
        """根据文件类型获取评审人列表"""
        if personnel_df is None:
            return []
        
        try:
            # 根据文件类型筛选评审人
            # 这里需要根据实际的Excel文件结构调整
            if f"{file_type}_评审人" in personnel_df.columns:
                reviewers = personnel_df[f"{file_type}_评审人"].dropna().tolist()
            elif "评审人" in personnel_df.columns:
                reviewers = personnel_df["评审人"].dropna().tolist()
            else:
                # 如果没有专门的评审人列，使用第一列作为默认
                reviewers = personnel_df.iloc[:, 0].dropna().tolist()
            
            self.logger.info(f"获取到 {file_type} 类型的评审人 {len(reviewers)} 个")
            return reviewers
            
        except Exception as e:
            self.logger.error(f"获取评审人列表失败: {str(e)}")
            return []


class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    def load_config() -> Dict:
        """加载配置"""
        default_config = {
            'DMS_URL': 'https://gcy.byd.com/dms/#/dashboard',
            'USERNAME': '',
            'PASSWORD': '',
            'WAIT_TIMEOUT': 20,
            'OPERATION_DELAY': 3,
            'HEADLESS_MODE': False,
            'MAX_RETRY_ATTEMPTS': 3,
            'POPUP_CHECK_TIMEOUT': 10
        }
        
        try:
            import config as cfg
            for key in default_config.keys():
                if hasattr(cfg, key):
                    default_config[key] = getattr(cfg, key)
        except ImportError:
            logging.warning("未找到config.py文件，使用默认配置")
        
        return default_config


def validate_config(config: Dict) -> bool:
    """验证配置是否完整"""
    required_fields = ['USERNAME', 'PASSWORD']
    
    for field in required_fields:
        if not config.get(field):
            logging.error(f"配置缺失: {field}")
            return False
    
    return True
