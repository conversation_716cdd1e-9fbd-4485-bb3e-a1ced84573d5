"""
配置示例文件 - 请根据实际情况修改config.py
"""

# ===== 基础配置 =====
USERNAME = 'your_username'  # 您的用户名
PASSWORD = 'your_password'  # 您的密码

# ===== 运行模式配置 =====
# 静默模式：True = 后台运行（推荐熟悉后使用），False = 显示浏览器窗口（推荐初次使用）
HEADLESS_MODE = False

# ===== 高级配置（一般无需修改） =====
DMS_URL = 'https://gcy.byd.com/dms/#/dashboard'
WAIT_TIMEOUT = 20
OPERATION_DELAY = 3
DOCUMENTS_FOLDER = "Final_Approval_Documents"
DATA_FOLDER = "Data"
CHROMEDRIVER_PATH = ""
MAX_RETRY_ATTEMPTS = 3
POPUP_CHECK_TIMEOUT = 10

# ===== 配置说明 =====
"""
1. 必填项：
   - USERNAME: 登录用户名
   - PASSWORD: 登录密码

2. 运行模式：
   - HEADLESS_MODE = False: 显示浏览器，可以看到操作过程（推荐初次使用）
   - HEADLESS_MODE = True:  静默模式，后台运行，不显示浏览器（推荐熟悉后使用）

3. 使用建议：
   - 首次使用：保持 HEADLESS_MODE = False，熟悉程序操作
   - 日常使用：设置 HEADLESS_MODE = True，实现后台自动化处理
   - 出现问题：临时改为 HEADLESS_MODE = False 进行调试

4. 其他参数一般无需修改，如有特殊需求可以调整等待时间等参数
"""
