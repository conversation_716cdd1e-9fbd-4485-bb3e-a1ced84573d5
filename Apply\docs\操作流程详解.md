# 申请编号操作流程详解

## 📋 基于JSON录制文件的精确操作序列

根据`申请编号new.json`文件中记录的操作序列，程序执行以下8个精确步骤：

### 🔐 登录流程
1. **打开DMS系统**: `https://gcy.byd.com/dms/#/home`
2. **点击登录按钮**: `#app > section > header > div > div[2] > button > span`
3. **输入用户名**: 在用户名输入框中填写
4. **输入密码**: 在密码输入框中填写  
5. **点击登录提交**: 完成登录过程

### 🎯 申请编号主流程

#### 步骤1: 打开申请编号模态框
```
选择器: #main > div > div > div:nth-child(1) > div:nth-child(1) > div > div.skip.NumberApplication > span
说明: 点击申请编号按钮，右侧弹出模态框
```

#### 步骤2: 文件类型选择
```
选择器: #el-collapse-content-14 > div > form > div > div.el-form-item__content > div > div > div.el-select__suffix > i > svg
说明: 点击文件类型下拉框
```

#### 步骤3: 选择项目文件
```
选择器: #el-id-7049-24
说明: 在下拉框中选择"项目文件"选项
```

#### 步骤4: 项目文档下拉框
```
选择器: #el-collapse-content-14 > div > form > div:nth-child(2) > div > div.el-form-item__content > span > div > div > div.el-select__suffix > i > svg  
说明: 点击项目文档下拉框
```

#### 步骤5: 项目文档输入
```
选择器: #el-id-7049-681
说明: 点击项目文档输入框，并输入文档类型关键字(DVP/PPL/FN)
```

#### 步骤6: 选择具体文档类型
```
DVP选择器: #el-id-7049-95 > span (DVP-系统设计验证计划)
PPL选择器: 通用xpath查找 (PPL-车辆匹配计划)
FN选择器: 通用xpath查找 (FN-接口定义/功能输入通知单)
```

#### 步骤7: 项目代号输入
```
选择器: #el-id-7049-685
说明: 点击项目代号输入框，输入车型代号(如HYHB)
```

#### 步骤8: 选择项目代号
```
选择器: #el-id-7049-691 > span
说明: 在下拉列表中选择对应的项目代号
```

### 📝 新增文档编号流程

#### 步骤9: 新增行
```
选择器: #el-collapse-content-19 > div > button > span
说明: 点击"新增行"按钮，为每个文件添加一行
```

#### 步骤10: 填写文档名称
```
选择器: #el-id-7049-707 (首次)
说明: 在文档名称输入框中填写"项目代号+项目+文件名"
```

#### 步骤11: 生成编号
```
选择器: #el-collapse-content-19 > div > form > div > div > div[3] > div > div > div > table > tbody > tr > td[3] > div > button > span
说明: 点击"生成编号"按钮
```

#### 步骤12: 获取编号
```
选择器: #el-collapse-content-19 > div > form > div > div > div[3] > div > div > div > table > tbody > tr > td[4] > div > span
说明: 从编号列获取生成的编号文本
```

#### 步骤13: 提交申请
```
选择器: #main > div > div > div.drawer-wrap.container > div > div > div.el-drawer__footer > div > button.el-button.el-button--primary > span
说明: 点击"提交"按钮完成申请
```

## 🔄 批处理逻辑

### 同类型文件处理
- 相同文档类型的文件在同一个模态框中批量处理
- 通过多次点击"新增行"为每个文件创建申请行
- 一次性提交所有同类型文件的编号申请

### 不同类型文件处理  
- 不同文档类型需要重新打开申请编号模态框
- 重新填写基本信息(文件类型、项目文档、项目代号)
- 分别为每种类型提交申请

## ⚙️ 技术实现细节

### 元素等待策略
```python
# 显式等待元素可点击
element = WebDriverWait(driver, 30).until(
    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
)
```

### 操作间隔时间
- 点击操作后等待2秒
- 输入操作后等待1秒  
- 下拉框选择后等待2秒
- 生成编号后等待3秒

### 错误处理
- 主选择器失效时使用备用选择器
- 超时异常时截图保存现场
- 详细日志记录每个步骤的执行情况

## 🎯 成功标志
1. **登录成功**: 页面跳转到主界面
2. **模态框打开**: 申请编号抽屉显示
3. **基本信息填写完成**: 所有必填字段已选择
4. **编号生成成功**: 编号列显示具体编号
5. **提交成功**: 模态框关闭，返回主界面

---
*此文档基于UI.Vision RPA录制的实际操作序列编写，确保程序行为与人工操作完全一致。*
