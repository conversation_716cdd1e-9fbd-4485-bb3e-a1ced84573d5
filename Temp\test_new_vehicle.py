#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试全新车型的文件复制功能
"""

import sys
import os
import shutil
from pathlib import Path
import pandas as pd

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from file_manager import FileManager

def test_new_vehicle():
    """测试全新车型的文件复制功能"""
    print("开始测试全新车型的文件复制功能...")
    
    base_path = Path(__file__).parent
    file_manager = FileManager(base_path)
    
    # 使用新的测试车型代号
    test_vehicle = "TEST99"
    
    # 清理可能存在的旧测试数据
    vehicle_dir = file_manager.vehicles_path / test_vehicle
    if vehicle_dir.exists():
        shutil.rmtree(vehicle_dir)
        print(f"清理旧测试数据: {vehicle_dir}")
    
    try:
        # 首先设置车型文件夹
        print(f"\n设置车型文件夹: {test_vehicle}")
        created, target_file = file_manager.setup_vehicle_folder(test_vehicle)
        
        if created:
            print(f"✅ 成功创建车型文件夹和模板文件")
        
        # 测试复制DVP和PPL文件
        selected_files = ["DVP", "PPL"]
        print(f"\n测试复制文件: {selected_files}")
        
        copied_files = file_manager.copy_template_files(test_vehicle, selected_files)
        
        print(f"复制的文件: {copied_files}")
        
        # 检查文件名是否正确
        print(f"\n检查复制的文件:")
        for file_name in copied_files:
            file_path = vehicle_dir / file_name
            if file_path.exists():
                print(f"✅ 文件存在: {file_name}")
                if "VSE" in file_name and test_vehicle in file_name:
                    print(f"  ✅ 文件名格式正确")
                else:
                    print(f"  ❌ 文件名格式有问题")
            else:
                print(f"❌ 文件不存在: {file_name}")
        
        # 检查File_Status表内容
        print(f"\n检查File_Status表:")
        info_file = vehicle_dir / "information" / f"{test_vehicle}_Fill_Template_Data.xlsx"
        if info_file.exists():
            df = pd.read_excel(info_file, sheet_name="File_Status")
            print(f"File_Status表内容:")
            print(df)
        
        # 测试get_files_for_apply_id方法
        print(f"\n测试get_files_for_apply_id方法:")
        apply_files = file_manager.get_files_for_apply_id(test_vehicle)
        print(f"需要申请编号的文件数量: {len(apply_files)}")
        for file_info in apply_files:
            print(f"  - {file_info['file_name']} (车型: {file_info['vehicle_code']})")
        
        if len(apply_files) == len(copied_files):
            print("✅ get_files_for_apply_id方法工作正常")
        else:
            print(f"⚠️  期望 {len(copied_files)} 个文件，实际找到 {len(apply_files)} 个")
        
        print("✅ 全新车型的文件复制功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_new_vehicle()
