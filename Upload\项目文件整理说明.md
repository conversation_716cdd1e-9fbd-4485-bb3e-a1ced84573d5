# 上传审批自动化项目文件整理

## 🔥 核心必需文件

### 主程序文件
- **main_controller.py** - 主控制器，协调整个流程
- **browser_manager.py** - 浏览器管理，处理窗口和导航
- **first_page_handler.py** - 第一页处理器，填写文档信息和上传文件
- **second_page_handler.py** - 第二页处理器，选择评审人方案和填写人员
- **utils.py** - 工具函数，文件扫描和匹配
- **config.py** - 配置文件，系统参数设置
- **requirements.txt** - Python依赖包列表

### 数据文件
- **Data/Fill_Template_Data.xlsx** - 人员信息Excel模板
- **Final_Approval_Documents/** - 处理完成的文档存放目录

### 启动文件
- **run.py** - 程序入口文件
- **start.bat** - Windows批处理启动脚本

## 📋 辅助参考文件

### HTML页面结构参考
- **上传审批第一个界面.html** - 第一页面结构参考
- **上传审批第二个界面.html** - 第二页面结构参考
- **dvp_shenpi.html** - DVP评审人页面结构
- **fn_shenpi.html** - FN评审人页面结构  
- **ppl_shenpi.html** - PPL评审人页面结构
- **time.html** - 时间填写组件结构

### UI.Vision录制文件
- **上传审批dvp.json** - DVP文件处理操作序列
- **上传审批fn.json** - FN文件处理操作序列
- **上传审批ppl.json** - PPL文件处理操作序列

## 🧪 测试和调试文件（可选）

### 测试脚本
- **test_fn_parsing.py** - FN文件名解析测试
- **test_second_page_enhanced.py** - 第二页处理功能测试

### 日志文件
- **upload_approval.log** - 程序运行日志
- **test_second_page_enhanced.log** - 测试日志

## 🗑️ 可删除的过时文件

### 旧版本文件
- **browser_manager_new.py** - 旧版浏览器管理器
- **first_page_handler_new.py** - 旧版第一页处理器
- **first_page_handler_optimized.py** - 优化版第一页处理器（已合并）
- **second_page_handler_complete.py** - 完整版第二页处理器（已合并）
- **second_page_handler_new.py** - 新版第二页处理器（已合并）
- **upload_approval_automation.py** - 旧版主程序
- **upload_approval_automation_new.py** - 新版主程序（已合并）
- **utils_new.py** - 新版工具函数（已合并）

### 分析和调试文件
- **analyze_excel.py** - Excel分析脚本
- **analyze_role_mapping.py** - 角色映射分析
- **apply_id_automation.py** - ID申请自动化
- **data_processor.py** - 数据处理器
- **element_locators.py** - 元素定位器
- **file_handler.py** - 文件处理器
- **form_handler.py** - 表单处理器
- **page_operations.py** - 页面操作
- **personnel_manager.py** - 人员管理器
- **read_excel_data.py** - Excel读取脚本
- **create_sample_data.py** - 示例数据创建
- **detailed_analysis.py** - 详细分析脚本
- **run_automation.py** - 自动化运行脚本

### 测试文件
- **test_doc_id_debug.py**
- **test_doc_id_extraction.py**
- **test_excel_reading.py**
- **test_file_scanning.py**
- **test_file_type_extraction.py**
- **test_name_parsing.py**
- **test_optimized.py**
- **test_second_page.py**
- **test_second_page_type_detection.py**
- **test_structure.py**

### 文档和说明文件
- **README_OPTIMIZED.md**
- **README_优化说明.md**
- **README_完善版.md**
- **README_第二页处理完善说明.md**
- **优化说明.md**
- **使用指南.md**
- **使用指南_更新版.md**
- **方案输入框和浮动项.txt**

### 其他文件
- **deletepdf.html** - 删除PDF页面
- **scrollbar.html** - 滚动条样式
- **Upload.zip** - 项目压缩包
- **__pycache__/** - Python缓存目录

## 📦 最小化部署文件列表

如果要部署最精简版本，只需要以下文件：

```
项目根目录/
├── main_controller.py          # 主控制器
├── browser_manager.py          # 浏览器管理
├── first_page_handler.py       # 第一页处理
├── second_page_handler.py      # 第二页处理
├── utils.py                    # 工具函数
├── config.py                   # 配置文件
├── requirements.txt            # 依赖包
├── run.py                      # 程序入口
├── start.bat                   # 启动脚本
├── Data/
│   └── Fill_Template_Data.xlsx # 人员信息模板
└── Final_Approval_Documents/   # 输出目录
```

## 🔧 清理建议

1. **保留核心文件**：保留上述最小化部署文件列表中的文件
2. **备份参考文件**：HTML和JSON文件可以移到单独的参考目录
3. **删除过时文件**：删除所有旧版本和测试文件
4. **整理文档**：保留一个主要的README.md文件即可
