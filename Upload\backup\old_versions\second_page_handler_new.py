"""
第二个页面处理模块
处理评审人选择、截止日期设置、提交评审等功能
"""

import time
import logging
from datetime import datetime, timedelta
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)

class SecondPageHandler:
    """第二个页面处理器"""
    
    def __init__(self, driver, wait, config):
        self.driver = driver
        self.wait = wait
        self.config = config
        
        # 文件类型对应的评审人方案
        self.reviewer_schemes = {
            "DVP": "DVP评审方案",
            "PPL": "PPL评审方案", 
            "FN": "FN评审方案"
        }
        
    def handle_second_page(self, file_type):
        """处理第二个页面的所有操作"""
        try:
            logger.info(f"📋 开始处理第二个页面 ({file_type})")
            time.sleep(2)
            
            # 1. 选择评审人方案
            if not self._select_reviewer_scheme(file_type):
                return False
            
            # 2. 填写评审人
            if not self._fill_reviewers(file_type):
                return False
            
            # 3. 设置截止日期
            if not self._set_deadline():
                return False
            
            # 4. 提交评审
            if not self._submit_review():
                return False
            
            # 5. 返回主页面
            if not self._return_to_main():
                return False
            
            logger.info(f"✅ 第二个页面处理完成 ({file_type})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 第二个页面处理失败: {str(e)}")
            return False
    
    def _select_reviewer_scheme(self, file_type):
        """选择评审人方案"""
        try:
            logger.info(f"📝 选择{file_type}评审人方案...")
            
            # 点击评审人方案下拉按钮
            dropdown_selectors = [
                "//div[contains(@class, 'el-select__suffix')]//i[contains(@class, 'el-select__caret')]",
                "//*[@id='el-collapse-content-25']/div/form/div/div/div/div/div/div/div[2]/i/svg",
                "//div[contains(@class, 'el-select')]//i[contains(@class, 'arrow-down')]"
            ]
            
            for selector in dropdown_selectors:
                try:
                    dropdown_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    dropdown_btn.click()
                    logger.info("✅ 成功点击评审人方案下拉按钮")
                    break
                except Exception as e:
                    logger.warning(f"下拉按钮选择器失败: {str(e)}")
                    continue
            else:
                logger.error("❌ 无法点击评审人方案下拉按钮")
                return False
            
            time.sleep(1)
            
            # 根据文件类型选择对应的方案
            scheme_name = self.reviewer_schemes.get(file_type, f"{file_type}评审方案")
            
            scheme_selectors = [
                f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{scheme_name}')]",
                f"//div[contains(@class, 'el-select-dropdown')]//span[contains(text(), '{scheme_name}')]",
                f"//li//span[contains(text(), '{scheme_name}')]"
            ]
            
            for selector in scheme_selectors:
                try:
                    scheme_option = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    scheme_option.click()
                    logger.info(f"✅ 已选择评审人方案: {scheme_name}")
                    time.sleep(2)
                    return True
                except Exception as e:
                    logger.warning(f"方案选择器失败: {str(e)}")
                    continue
            
            logger.error(f"❌ 无法选择评审人方案: {scheme_name}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 选择评审人方案异常: {str(e)}")
            return False
    
    def _fill_reviewers(self, file_type):
        """填写评审人"""
        try:
            logger.info("👥 填写评审人...")
            
            # 默认评审人列表（可以从配置文件或数据库读取）
            default_reviewers = {
                "DVP": ["张三", "李四", "王五"],
                "PPL": ["赵六", "钱七", "孙八"],
                "FN": ["周九", "吴十", "郑十一"]
            }
            
            reviewers = default_reviewers.get(file_type, ["默认评审人"])
            
            # 查找评审人输入区域
            reviewer_selectors = [
                "//div[contains(@class, 'reviewer-section')]//input",
                "//label[contains(text(), '评审人')]/following-sibling::div//input",
                "//div[contains(@class, 'el-form-item')]//input[contains(@placeholder, '评审')]"
            ]
            
            for selector in reviewer_selectors:
                try:
                    reviewer_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    
                    # 输入评审人
                    for i, reviewer in enumerate(reviewers):
                        if i > 0:
                            reviewer_field.send_keys("; ")  # 多个评审人用分号分隔
                        reviewer_field.send_keys(reviewer)
                        time.sleep(0.5)
                    
                    logger.info(f"✅ 已输入评审人: {', '.join(reviewers)}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"评审人输入选择器失败: {str(e)}")
                    continue
            
            logger.warning("⚠️ 未找到评审人输入框，跳过此步骤")
            return True  # 即使没有找到也继续执行
            
        except Exception as e:
            logger.error(f"❌ 填写评审人异常: {str(e)}")
            return False
    
    def _set_deadline(self):
        """设置截止日期"""
        try:
            logger.info("📅 设置截止日期...")
            
            # 计算截止日期（当前日期+7天）
            deadline = datetime.now() + timedelta(days=7)
            deadline_str = deadline.strftime("%Y-%m-%d")
            
            # 查找日期输入框
            date_selectors = [
                "//input[contains(@placeholder, '日期')]",
                "//div[contains(@class, 'el-date-editor')]//input",
                "//input[contains(@id, 'date')]"
            ]
            
            for selector in date_selectors:
                try:
                    date_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    date_field.click()
                    time.sleep(1)
                    date_field.clear()
                    date_field.send_keys(deadline_str)
                    logger.info(f"✅ 已设置截止日期: {deadline_str}")
                    time.sleep(1)
                    
                    # 点击其他地方关闭日期选择器
                    self.driver.find_element(By.TAG_NAME, "body").click()
                    return True
                    
                except Exception as e:
                    logger.warning(f"日期选择器失败: {str(e)}")
                    continue
            
            logger.warning("⚠️ 未找到日期输入框，使用默认日期")
            return True  # 即使没有设置日期也继续执行
            
        except Exception as e:
            logger.error(f"❌ 设置截止日期异常: {str(e)}")
            return False
    
    def _submit_review(self):
        """提交评审"""
        try:
            logger.info("🚀 提交评审...")
            
            # 检查是否为测试模式
            if self.config.get('TEST_MODE', True):
                logger.info("⚠️ 测试模式：跳过实际提交")
                time.sleep(2)  # 模拟提交时间
                return True
            
            # 查找提交按钮
            submit_selectors = [
                "//button[contains(text(), '提交评审')]",
                "//span[contains(text(), '提交评审')]/parent::button",
                "//button[contains(@class, 'el-button--primary') and contains(text(), '提交')]",
                "//div[contains(@class, 'submit-area')]//button"
            ]
            
            for selector in submit_selectors:
                try:
                    submit_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    submit_btn.click()
                    logger.info("✅ 已点击提交评审按钮")
                    time.sleep(3)  # 等待提交完成
                    return True
                    
                except Exception as e:
                    logger.warning(f"提交按钮选择器失败: {str(e)}")
                    continue
            
            logger.error("❌ 无法找到提交按钮")
            return False
            
        except Exception as e:
            logger.error(f"❌ 提交评审异常: {str(e)}")
            return False
    
    def _return_to_main(self):
        """返回主页面"""
        try:
            logger.info("🏠 返回主页面...")
            
            # 查找返回按钮
            return_selectors = [
                "//button[contains(text(), '返回')]",
                "//span[contains(text(), '返回')]/parent::button",
                "//a[contains(text(), '返回')]",
                "//div[contains(@class, 'back-btn')]//button",
                "//i[contains(@class, 'el-icon-back')]"
            ]
            
            for selector in return_selectors:
                try:
                    return_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    return_btn.click()
                    logger.info("✅ 已点击返回按钮")
                    time.sleep(2)  # 等待页面跳转
                    return True
                    
                except Exception as e:
                    logger.warning(f"返回按钮选择器失败: {str(e)}")
                    continue
            
            # 如果没有找到返回按钮，尝试刷新页面或导航到主页
            logger.warning("⚠️ 未找到返回按钮，尝试导航到主页")
            try:
                self.driver.get(self.config.get('DMS_URL', 'https://gcy.byd.com/dms/#/dashboard'))
                time.sleep(3)
                logger.info("✅ 已导航到主页")
                return True
            except Exception as e:
                logger.error(f"导航到主页失败: {str(e)}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 返回主页面异常: {str(e)}")
            return False
    
    def handle_confirmation_dialog(self):
        """处理确认对话框"""
        try:
            # 查找确认按钮
            confirm_selectors = [
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), '确认')]",
                "//span[contains(text(), '确定')]/parent::button",
                "//div[contains(@class, 'el-message-box')]//button[contains(@class, 'el-button--primary')]"
            ]
            
            for selector in confirm_selectors:
                try:
                    confirm_btn = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    confirm_btn.click()
                    logger.info("✅ 已处理确认对话框")
                    time.sleep(1)
                    return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.warning(f"处理确认对话框失败: {str(e)}")
            return False
