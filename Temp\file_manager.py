import os
import shutil
import logging
from pathlib import Path
import pandas as pd
from datetime import datetime
import subprocess
import sys


class FileManager:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.fillin_path = self.base_path / "Fillin"
        self.logger = logging.getLogger(__name__)
        
        # 更新的文件类型映射，使用关键字模式匹配
        self.file_mappings = {
            "DVP": {
                "template": "XX项目VSE软件设计验证计划（DVP）.xlsx",
                "pattern": "软件设计验证计划",
                "extension": ".xlsx"
            },
            "PPL": {
                "template": "XX项目VSE软件开发匹配测试计划.xlsx", 
                "pattern": "软件开发匹配测试计划",
                "extension": ".xlsx"
            },
            "FN_IMU": {
                "template": "XX项目VSE系统 to 安全气囊节点接口定义通知单.docx",
                "pattern": "安全气囊节点接口定义通知单",
                "extension": ".docx"
            },
            "FN_VCU": {
                "template": "XX项目VSE系统 to VCU系统接口定义通知单.docx",
                "pattern": "VCU系统接口定义通知单", 
                "extension": ".docx"
            },
            "FN_IPB": {
                "template": "XX项目VSE系统 to IPB系统接口定义通知单.docx",
                "pattern": "IPB系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_ESP_BWA": {
                "template": "XX项目VSE系统 to ESP+BWA系统接口定义通知单.docx",
                "pattern": "ESP+BWA系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_EPS": {
                "template": "XX项目VSE系统 to EPS系统接口定义通知单.docx",
                "pattern": "EPS系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_EPSA": {
                "template": "XX项目VSE系统 to EPSA系统接口定义通知单.docx",
                "pattern": "EPSA系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_EPB": {
                "template": "XX项目VSE系统 to EPB系统接口定义通知单.docx",
                "pattern": "EPB系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_A": {
                "template": "XX项目VSE系统 to DiSus-A系统接口定义通知单.docx",
                "pattern": "DiSus-A系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_C": {
                "template": "XX项目VSE系统 to DiSus-C系统接口定义通知单.docx",
                "pattern": "DiSus-C系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_P": {
                "template": "XX项目VSE系统 to DiSus-P系统接口定义通知单.docx",
                "pattern": "DiSus-P系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_X": {
                "template": "XX项目VSE系统 to DiSus-X系统接口定义通知单.docx",
                "pattern": "DiSus-X系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_DISUS_M": {
                "template": "XX项目VSE系统 to DiSus-M系统接口定义通知单.docx",
                "pattern": "DiSus-M系统接口定义通知单",
                "extension": ".docx"
            },
            "FN_域控": {
                "template": "XX项目VSE系统 to 跨域计算平台接口定义通知单.docx",
                "pattern": "跨域计算平台接口定义通知单",
                "extension": ".docx"
            }
        }

    def find_template_file(self, file_type):
        """根据文件类型查找模板文件"""
        if file_type not in self.file_mappings:
            return None
            
        mapping = self.file_mappings[file_type]
        pattern = mapping.get("pattern", "")
        extension = mapping.get("extension", "")
        
        # 在Templates文件夹中查找包含关键字的文件
        for template_file in self.templates_path.glob(f"*{extension}"):
            if pattern in template_file.name:
                self.logger.info(f"找到匹配的模板文件: {template_file.name} (关键字: {pattern})")
                return template_file
        
        # 如果按关键字没找到，尝试精确匹配原来的文件名
        exact_file = self.templates_path / mapping["template"]
        if exact_file.exists():
            self.logger.info(f"使用精确匹配的模板文件: {exact_file.name}")
            return exact_file
            
        self.logger.warning(f"未找到匹配的模板文件，关键字: {pattern}, 扩展名: {extension}")
        return None

    def setup_vehicle_folder(self, vehicle_code):
        """设置车型文件夹结构"""
        try:
            vehicle_dir = self.vehicles_path / vehicle_code
            info_dir = vehicle_dir / "information"
            info_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制并重命名Fill_Template_Data.xlsx
            template_file = self.templates_path / "Fill_Template_Data.xlsx"
            target_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not target_file.exists():
                shutil.copy2(template_file, target_file)
                self.update_vehicle_code_in_excel(target_file, vehicle_code)
                self.logger.info(f"创建车型模板文件: {target_file}")
                return True, target_file
            else:
                self.logger.info(f"车型模板文件已存在: {target_file}")
                return False, target_file
                
        except Exception as e:
            self.logger.error(f"设置车型文件夹失败: {str(e)}")
            raise

    def update_vehicle_code_in_excel(self, excel_file, vehicle_code):
        """更新Excel文件中的车型代号"""
        try:
            # 读取Info sheet
            df = pd.read_excel(excel_file, sheet_name="Info")
            df.loc[df['角色'] == '车型代号', 'people'] = vehicle_code
            
            # 写回Excel文件
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name="Info", index=False)
            
            self.logger.info(f"更新Excel中车型代号为: {vehicle_code}")
            
        except Exception as e:
            self.logger.error(f"更新Excel中车型代号失败: {str(e)}")
            raise

    def copy_template_files(self, vehicle_code, selected_files):
        """复制模板文件到车型文件夹"""
        vehicle_dir = self.vehicles_path / vehicle_code
        vehicle_dir.mkdir(exist_ok=True)
        
        copied_files = []
        new_files = []
        
        for file_type in selected_files:
            if file_type in self.file_mappings:
                mapping = self.file_mappings[file_type]
                  # 使用新的查找方法
                template_file = self.find_template_file(file_type)
                
                if template_file and template_file.exists():
                    # 生成目标文件名，直接替换原文件名中的XX为车型代号
                    original_name = template_file.name
                    target_name = original_name.replace("XX", vehicle_code)
                    target_path = vehicle_dir / target_name
                    
                    if not target_path.exists():
                        # 复制文件
                        shutil.copy2(template_file, target_path)
                        
                        # 修改文件内容中的XX为车型代号
                        self.replace_vehicle_code_in_file(target_path, vehicle_code)
                        
                        copied_files.append(target_name)
                        new_files.append(target_name)
                        self.logger.info(f"复制并修改模板文件: {template_file.name} -> {target_name}")
                    else:
                        self.logger.info(f"文件已存在，跳过复制: {target_name}")
                else:
                    self.logger.warning(f"未找到模板文件，文件类型: {file_type}")
        
        # 更新File_Status表
        if new_files:
            self.update_file_status(vehicle_code, new_files)
        
        return copied_files

    def replace_vehicle_code_in_file(self, file_path, vehicle_code):
        """替换文件内容中的XX为车型代号"""
        try:
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.xlsx':
                # 处理Excel文件
                self.replace_in_excel(file_path, vehicle_code)
            elif file_extension == '.docx':
                # 处理Word文件
                self.replace_in_word(file_path, vehicle_code)
            else:
                self.logger.warning(f"不支持的文件类型: {file_extension}")
                
        except Exception as e:
            self.logger.error(f"替换文件内容失败: {str(e)}")

    def replace_in_excel(self, file_path, vehicle_code):
        """替换Excel文件中的XX"""
        try:
            # 读取Excel文件的所有sheet
            excel_file = pd.ExcelFile(file_path, engine='openpyxl')
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for sheet_name in excel_file.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, engine='openpyxl')
                    
                    # 替换所有包含XX的单元格
                    for col in df.columns:
                        if df[col].dtype == 'object':
                            df[col] = df[col].astype(str).str.replace('XX', vehicle_code, regex=False)
                    
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.logger.info(f"已替换Excel文件中的XX为{vehicle_code}: {file_path.name}")
            
        except Exception as e:
            self.logger.error(f"替换Excel文件内容失败: {str(e)}")

    def replace_in_word(self, file_path, vehicle_code):
        """替换Word文件中的XX"""
        try:
            # 这里需要使用python-docx库来处理Word文件
            # 由于这是一个简化版本，暂时跳过Word文件的内容替换
            self.logger.info(f"Word文件内容替换功能待实现: {file_path.name}")
            
        except Exception as e:
            self.logger.error(f"替换Word文件内容失败: {str(e)}")

    def update_file_status(self, vehicle_code, new_files):
        """更新File_Status表"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                # 读取现有的File_Status
                try:
                    df = pd.read_excel(excel_file, sheet_name="File_Status")
                except:
                    # 如果sheet不存在，创建新的
                    df = pd.DataFrame(columns=['file_name', 'code', 'numbered_file', 'is_fillin', 'is_upload'])                # 添加新文件
                for file_name in new_files:
                    # 去掉扩展名，得到完整的文件名（包含车型代号）
                    name_without_ext = Path(file_name).stem
                    
                    # 检查是否已存在（使用完整文件名检查）
                    if name_without_ext not in df['file_name'].values:
                        new_row = {
                            'file_name': name_without_ext,  # 存储完整文件名（含车型代号）
                            'code': '',                     # 文件编号（待申请）
                            'numbered_file': '',            # 带编号的文件名（待生成）
                            'is_fillin': 'N',
                            'is_upload': 'N'
                        }
                        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
                
                # 写回Excel文件
                with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                    df.to_excel(writer, sheet_name="File_Status", index=False)
                
                self.logger.info(f"更新File_Status表，添加{len(new_files)}个文件")
                
        except Exception as e:
            self.logger.error(f"更新File_Status表失败: {str(e)}")

    def get_vehicle_files_info(self, vehicle_code):
        """获取车型文件信息"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not excel_file.exists():
                return []
            
            # 读取File_Status
            df = pd.read_excel(excel_file, sheet_name="File_Status")
            files_info = []
            
            for _, row in df.iterrows():
                file_info = {
                    'file_name': row.get('file_name', ''),
                    'code': row.get('code', ''),
                    'numbered_file': row.get('numbered_file', ''),
                    'is_fillin': row.get('is_fillin', 'N'),
                    'is_upload': row.get('is_upload', 'N')
                }
                files_info.append(file_info)
            
            return files_info
            
        except Exception as e:
            self.logger.error(f"获取车型文件信息失败: {str(e)}")
            return []

    def get_files_for_apply_id(self, vehicle_code):
        """获取需要申请编号的文件信息"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not excel_file.exists():
                self.logger.warning(f"车型配置文件不存在: {excel_file}")
                return []
            
            # 读取File_Status表
            df = pd.read_excel(excel_file, sheet_name="File_Status")
            files_for_apply = []
            
            for _, row in df.iterrows():
                file_name = row.get('file_name', '')
                code = row.get('code', '')
                
                # 只返回还没有申请编号的文件（检查空值、NaN和空字符串）
                if file_name and (pd.isna(code) or code == '' or str(code).strip() == ''):
                    files_for_apply.append({
                        'file_name': file_name,
                        'vehicle_code': vehicle_code
                    })
            
            self.logger.info(f"找到 {len(files_for_apply)} 个需要申请编号的文件")
            return files_for_apply
            
        except Exception as e:
            self.logger.error(f"获取申请编号文件列表失败: {str(e)}")
            return []

    def update_file_with_id(self, vehicle_code, file_name, file_id):
        """更新文件编号"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                df = pd.read_excel(excel_file, sheet_name="File_Status")
                  # 查找并更新对应文件的编号
                mask = df['file_name'] == file_name
                if mask.any():
                    df.loc[mask, 'code'] = file_id
                    # 生成带编号的文件名格式：编号-车型代号其他内容
                    df.loc[mask, 'numbered_file'] = f"{file_id}-{file_name}"
                    
                    # 写回Excel文件
                    with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                        df.to_excel(writer, sheet_name="File_Status", index=False)
                    
                    self.logger.info(f"更新文件编号: {file_name} -> {file_id}")
                else:
                    self.logger.warning(f"未找到文件: {file_name}")
                    
        except Exception as e:
            self.logger.error(f"更新文件编号失败: {str(e)}")

    def update_fill_status(self, vehicle_code, file_name, status='Y'):
        """更新填写状态"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                df = pd.read_excel(excel_file, sheet_name="File_Status")
                
                # 查找并更新对应文件的填写状态
                mask = df['file_name'] == file_name
                if mask.any():
                    df.loc[mask, 'is_fillin'] = status
                    
                    # 写回Excel文件
                    with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                        df.to_excel(writer, sheet_name="File_Status", index=False)
                    
                    self.logger.info(f"更新填写状态: {file_name} -> {status}")
                else:
                    self.logger.warning(f"未找到文件: {file_name}")
                    
        except Exception as e:
            self.logger.error(f"更新填写状态失败: {str(e)}")

    def get_reviewer_info(self, vehicle_code):
        """获取审批人员信息"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            df = pd.read_excel(excel_file, sheet_name="Info")
            
            reviewers = {
                'data_managers': [],
                'section_chief': '',
                'related_parties': []
            }
            
            for _, row in df.iterrows():
                role = row.get('角色', '')
                email = row.get('邮箱', '')
                work_id = row.get('工号', '')
                
                if '数据管理员' in role and work_id:
                    reviewers['data_managers'].append(work_id)
                elif '科长' in role and email:
                    reviewers['section_chief'] = email
                elif '相关方' in role and email:
                    reviewers['related_parties'].append(email)
            
            return reviewers
            
        except Exception as e:
            self.logger.error(f"获取审批人员信息失败: {str(e)}")
            return {}

    def get_upload_files(self, vehicle_code):
        """获取需要上传的文件"""
        try:
            info_dir = self.vehicles_path / vehicle_code / "information"
            excel_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not excel_file.exists():
                return []
            
            df = pd.read_excel(excel_file, sheet_name="File_Status")
            upload_files = []
            
            vehicle_dir = self.vehicles_path / vehicle_code
            
            for _, row in df.iterrows():
                file_name = row.get('file_name', '')
                code = row.get('code', '')
                is_fillin = row.get('is_fillin', 'N')
                
                if file_name and code and is_fillin == 'Y':
                    # 查找实际文件
                    pattern = f"{vehicle_code}项目*{file_name.split('项目')[-1] if '项目' in file_name else file_name}*"
                    matching_files = list(vehicle_dir.glob(pattern))
                    
                    if matching_files:
                        upload_files.append({
                            'file_name': file_name,
                            'code': code,
                            'file_path': str(matching_files[0])
                        })
            
            return upload_files
            
        except Exception as e:
            self.logger.error(f"获取上传文件列表失败: {str(e)}")
            return []
