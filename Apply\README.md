# DMS申请编号自动化程序

本程序是一个基于Selenium的自动化工具，用于在DMS（文档管理系统）中批量申请文档编号。程序能够自动识别文件类型（DVP、PPL、FN），登录系统，填写申请表单，生成编号，并将编号写入文件内容。

## 功能特性

- ✅ **自动登录** - 自动登录DMS系统
- ✅ **智能弹窗处理** - 自动关闭各种系统弹窗
- ✅ **文件类型识别** - 自动识别DVP、PPL、FN三种文档类型
- ✅ **批量申请编号** - 支持同时处理多个文件
- ✅ **编号写入文件** - 自动将编号写入Word/Excel文档内容
- ✅ **安全文件处理** - 输出到单独文件夹，不影响原文件
- ✅ **容错机制** - 多重选择器策略，适应页面变化
- ✅ **实时保存** - 每种类型处理完立即保存，防止数据丢失

## 支持的文件类型

| 文件类型 | 说明 | 支持格式 |
|---------|------|---------|
| DVP | 系统设计验证计划 | .xlsx |
| PPL | 车辆匹配计划 | .xlsx |
| FN | 接口定义/功能输入通知单 | .docx |

✅ **全自动化处理**：无需手动输入，完全自动化操作  
✅ **智能文件识别**：自动识别DVP、PPL、FN等文件类型  
✅ **批量处理**：支持多文件同时处理  
✅ **文件名更新**：自动更新文件名包含申请编号  
✅ **内容更新**：自动更新Word/Excel文件内部编号  
✅ **错误恢复**：遇到问题时自动截图便于调试  
✅ **日志记录**：详细的操作日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置设置

2. 编辑 `config.py` 文件，填写您的信息：
   ```python
   USERNAME = "您的用户名"
   PASSWORD = "您的密码"
   PROJECT_CODE = "项目代号"  # 例如: "HYHB"
   DMS_URL = "https://gcy.byd.com/dms/#/home"
   HEADLESS_MODE = False  # 静默模式：True=后台运行，False=显示浏览器
   ```

### 3. 准备文件

1. 将需要申请编号的文件放入 `input_files` 文件夹
2. 支持的文件格式：`.docx`、`.xlsx`
3. 程序会自动识别文件类型

### 4. 运行程序

```bash
python run_apply_id.py
```

## 静默模式

程序支持静默模式，可以在后台运行而不显示浏览器窗口：

- **显示模式**（`HEADLESS_MODE = False`）：默认模式，显示浏览器窗口，可以观察程序运行过程
- **静默模式**（`HEADLESS_MODE = True`）：后台运行，不显示浏览器窗口，用户可以在前台做其他工作

**说明**：无论哪种模式，程序执行完成后都会自动关闭浏览器并退出，无需手动操作。

**建议**：首次使用建议设置为显示模式，确认程序运行正常后再启用静默模式。
```

### 3. 查看结果
- 程序会自动更新文件名和内容
- 查看日志文件 `apply_id_automation.log` 了解详细过程
- 如有错误，检查截图文件 `fill_basic_info_error.png`

## 文件类型识别

程序会根据文件名自动识别文件类型：

| 文件类型 | 识别关键字 | 申请类型 |
|----------|------------|----------|
| DVP | "dvp", "设计验证计划" | DVP-系统设计验证计划 |
| PPL | "ppl", "匹配" | PPL-车辆匹配计划 |
| FN | "接口定义", "通知单", "FN", "fn" | FN-接口定义/功能输入通知单 |

## 项目结构

```
Apply/
├── apply_id_automation.py    # 主自动化程序
├── run_apply_id.py          # 入口程序
├── file_number_filler.py    # 文件内容更新模块
├── config.py                # 配置文件
├── config_example.py        # 配置示例
├── requirements.txt         # 依赖包列表
├── input_files/             # 输入文件夹
├── README.md               # 说明文档
├── 使用说明.md             # 详细使用说明
├── 操作流程说明.md         # 操作流程说明
├── 修复说明.md             # 问题修复说明
└── 优化说明_20250625.md    # 最新优化说明
```

## 常见问题

### 1. ChromeDriver问题
如果提示找不到ChromeDriver，请：
- 下载对应Chrome版本的ChromeDriver
- 将chromedriver.exe放在Apply文件夹中
- 或添加到系统PATH环境变量

### 2. 登录问题
- 确保config.py中的用户名密码正确
- 检查网络连接是否正常
- 确认DMS系统可以正常访问

### 3. 页面元素找不到
- 程序已优化为智能适应页面变化
- 如仍有问题，查看错误截图进行分析
- 联系开发者获取最新版本

### 4. 文件处理失败
- 确保文件格式正确（.docx或.xlsx）
- 检查文件是否被其他程序占用
- 查看日志文件了解具体错误原因

## 性能优化

当前版本的主要优化：

1. **快速选择策略**：优先使用文本选择器，避免ID依赖
2. **智能等待机制**：动态检测页面状态，减少不必要等待
3. **并行处理**：支持多文件类型同时处理
4. **错误恢复**：遇到问题时自动重试和恢复

## 版本历史

### v2.1.0 (2025-06-25) - 重大性能优化
- 文件类型选择速度提升90%
- 弹窗关闭逻辑优化，提升50%效率
- 增强页面结构变化适应性
- 优化日志格式，便于追踪

### v2.0.0 (2025-06-24) - 功能完善
- 实现完全自动化，移除手动确认
- 支持批量文件处理
- 增加文件内容自动更新
- 完善错误处理和日志记录

### v1.0.0 (2025-06-23) - 初始版本
- 基础自动化功能
- 支持DVP、PPL、FN文件类型
- 基本的申请编号流程

## 支持与联系

如有问题或建议，请联系开发者或查看相关文档。

---

**注意**：本程序仅供内部使用，请确保遵守公司相关规定和数据安全要求。
