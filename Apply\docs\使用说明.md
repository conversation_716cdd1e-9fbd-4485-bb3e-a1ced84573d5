# 使用说明

## 首次使用

### 1. 环境配置

1. **安装Python 3.7+**
2. **安装依赖包**：
   ```bash
   pip install -r requirements.txt
   ```
3. **下载ChromeDriver**：
   - 访问 https://chromedriver.chromium.org/
   - 下载与您的Chrome浏览器版本匹配的ChromeDriver
   - 将 `chromedriver.exe` 放在项目根目录

### 2. 配置设置

1. **复制配置文件**：
   ```bash
   copy config_example.py config.py
   ```

2. **编辑配置文件**：
   打开 `config.py`，填写以下信息：
   ```python
   USERNAME = "您的用户名"      # 例如: "6604331"
   PASSWORD = "您的密码"       # 您的DMS登录密码
   PROJECT_CODE = "项目代号"   # 例如: "HYHB"
   HEADLESS_MODE = False       # 静默模式：True=后台运行，False=显示浏览器
   ```

### 静默模式说明

- **False（默认）**：显示浏览器窗口，可以看到操作过程
- **True**：后台运行，不显示浏览器窗口，用户可以在前台做其他工作

**注意**：
- 首次使用建议设置为 `False` 以便观察程序运行状态
- 无论哪种模式，程序执行完成后都会自动关闭浏览器并退出

### 3. 准备文件

1. 将需要申请编号的文件放入 `input_files` 文件夹
2. 支持的文件类型：
   - `.docx` - Word文档（FN类型）
   - `.xlsx` - Excel文档（DVP、PPL类型）

## 运行程序

```bash
python run_apply_id.py
```

## 程序执行过程

1. **检查配置** - 验证配置信息是否完整
2. **启动浏览器** - 自动启动Chrome浏览器
3. **登录系统** - 自动登录DMS系统
4. **关闭弹窗** - 自动关闭系统弹窗
5. **识别文件** - 自动识别文件类型
6. **申请编号** - 按类型批量申请编号
7. **保存文件** - 将结果保存到output_files文件夹

## 输出结果

### 文件命名规则
```
{编号}-{原文件名}
```

例如：
- `HYHB_DVP_A19-100005-XX项目VSE软件设计验证计划（DVP）.xlsx`
- `HYHB_FN_A19-100001-XX项目VSE系统接口定义通知单.docx`

### 编号写入位置

#### Word文档（FN类型）
- **首页**：编号字段后面
- **页眉表格**：文件编号单元格

#### Excel文档（DVP类型）
- **DVP sheet**：DVP编号字段后面

#### Excel文档（PPL类型）
- **所有相关sheet**：编号字段后面

## 常见问题解决

### 1. ChromeDriver版本错误
```
SessionNotCreatedException: session not created: This version of ChromeDriver only supports Chrome version XX
```
**解决方法**：下载匹配的ChromeDriver版本

### 2. 登录失败
```
TimeoutException: Message: 无法找到登录按钮
```
**解决方法**：
- 检查用户名密码是否正确
- 确认网络连接是否正常
- 检查DMS网址是否可访问

### 3. 页面元素未找到
```
TimeoutException: Message: 无法找到申请编号按钮
```
**解决方法**：
- 程序已内置多重选择器，通常会自动适应
- 检查是否有新的弹窗未关闭
- 查看日志文件定位具体问题

### 4. 编号获取失败
```
未获取到有效编号
```
**解决方法**：
- 检查项目代号是否正确
- 确认文件名称格式是否符合要求
- 检查网络连接稳定性

## 日志查看

程序运行时会生成详细日志文件：`apply_id_automation.log`

日志级别：
- `INFO` - 正常操作信息
- `WARNING` - 警告信息
- `ERROR` - 错误信息

## 高级设置

### 修改等待时间

编辑 `config.py` 文件：
```python
WAIT_TIMEOUT = 15     # 增加等待超时时间
PAGE_LOAD_WAIT = 5    # 增加页面加载等待时间
```

### 调试模式

如需查看浏览器操作过程，可以注释掉浏览器的headless模式设置。

## 技术支持

如遇到问题：
1. 首先查看日志文件
2. 检查配置是否正确
3. 确认网络和系统环境
4. 联系开发团队
