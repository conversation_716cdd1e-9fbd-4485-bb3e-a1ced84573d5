\documentclass[11pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{geometry}
\usepackage{setspace}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{array}
\usepackage{longtable}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{graphicx}
\usepackage{float}

% 页面设置
\geometry{a4paper,left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}
\setstretch{1.25}

% 字体设置
\setCJKmainfont{Noto Serif CJK SC}

% 标题间距设置
\titlespacing*{\section}{0pt}{8pt}{4pt}
\titlespacing*{\subsection}{0pt}{6pt}{3pt}
\titlespacing*{\subsubsection}{0pt}{4pt}{2pt}

% 列表环境间距设置
\setlist{topsep=2pt,parsep=2pt,itemsep=1pt}

% 代码样式设置
\lstset{
    basicstyle=\fontsize{10.5pt}{10pt}\selectfont\ttfamily,
    breaklines=true,
    frame=single,
    backgroundcolor=\color{gray!10},
    keywordstyle=\color{blue},
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    showstringspaces=false,
    numbers=left,
    numberstyle=\tiny,
    xleftmargin=2em,
    framexleftmargin=1.5em
}

\title{\textbf{车型文件管理系统使用说明书}}
\author{}
\date{\today}

\begin{document}

\maketitle

\section{程序简介}

\subsection{系统概述}
车型文件管理系统是一个专为汽车行业设计的桌面应用程序，旨在自动化处理车型相关文档的管理流程。系统基于PyQt5框架开发，集成了Selenium网页自动化技术，能够实现从模板文件管理到审批上传的全流程自动化操作。

\subsection{主要功能}
系统提供以下核心功能：
\begin{itemize}
    \item \textbf{车型信息管理}：创建和管理车型文件夹结构，配置车型基础信息
    \item \textbf{模板文件处理}：智能复制和重命名模板文件，自动替换车型代号
    \item \textbf{编号申请自动化}：通过网页自动化技术批量申请文件编号
    \item \textbf{内容自动填写}：基于模板数据自动填写Excel和Word文档内容
    \item \textbf{审批上传功能}：自动上传文件到OA系统并启动审批流程
    \item \textbf{状态跟踪管理}：实时跟踪文件处理状态和进度
\end{itemize}

\subsection{适用场景}
本系统适用于以下工作场景：
\begin{itemize}
    \item 汽车行业车型文档标准化管理
    \item 大批量文件的编号申请和内容填写
    \item 需要频繁与OA系统交互的文档审批流程
    \item 多车型项目的文件管理和状态跟踪
\end{itemize}

\subsection{技术架构}
系统采用模块化设计，主要技术组件包括：

\begin{table}[H]
\centering
\begin{tabular}{|c|c|c|}
\hline
\textbf{技术组件} & \textbf{版本} & \textbf{功能描述} \\
\hline
PyQt5 & 5.15.10 & 图形用户界面框架 \\
\hline
Selenium & 4.15.2 & 网页自动化操作 \\
\hline
Pandas & 2.1.4 & 数据处理和Excel操作 \\
\hline
openpyxl & 3.1.2 & Excel文件读写 \\
\hline
python-docx & 1.1.0 & Word文档处理 \\
\hline
\end{tabular}
\caption{系统技术栈}
\end{table}

图1 系统架构示意图 应插入此处

\section{安装与配置步骤}

\subsection{系统要求}

\subsubsection{硬件要求}
\begin{itemize}
    \item 操作系统：Windows 10/11 (64位)
    \item 内存：8GB以上（推荐16GB）
    \item 硬盘空间：2GB以上可用空间
    \item 网络：稳定的互联网连接（用于OA系统访问）
\end{itemize}

\subsubsection{软件要求}
\begin{itemize}
    \item Python 3.8或更高版本
    \item Google Chrome浏览器（最新版本）
    \item ChromeDriver（与Chrome版本匹配）
    \item Microsoft Office或WPS Office（用于查看和编辑文档）
\end{itemize}

\subsection{安装步骤}

\subsubsection{方法一：使用安装脚本（推荐）}
\begin{enumerate}
    \item 解压系统文件到目标目录
    \item 双击运行 \texttt{Scripts/install.bat}
    \item 等待依赖包自动安装完成
    \item 安装完成后，双击 \texttt{Scripts/run\_gui.bat} 启动系统
\end{enumerate}

\subsubsection{方法二：手动安装}
\begin{enumerate}
    \item 确保Python 3.8+已正确安装
    \item 打开命令提示符，切换到系统目录
    \item 执行安装命令：
    \begin{lstlisting}[language=bash]
pip install -r requirements.txt
    \end{lstlisting}
    \item 下载并配置ChromeDriver到系统PATH
    \item 运行主程序：
    \begin{lstlisting}[language=bash]
python main_gui_final.py
    \end{lstlisting}
\end{enumerate}

\subsection{初始配置}

\subsubsection{模板文件准备}
在 \texttt{Templates} 文件夹中放置以下模板文件：
\begin{itemize}
    \item \texttt{Fill\_Template\_Data.xlsx}：车型信息模板
    \item \texttt{XX项目VSE软件设计验证计划.xlsx}：DVP模板
    \item \texttt{XX项目VSE软件开发匹配测试计划.xlsx}：PPL模板
    \item \texttt{XX项目VSE\_*接口定义通知单.docx}：各类FN模板文件
    \item \texttt{数据管理员名单.png}：管理员名单图片
\end{itemize}

\subsubsection{目录结构验证}
确保以下目录结构存在：
\begin{lstlisting}[language=bash]
项目根目录/
├── Templates/          # 模板文件夹
├── Vehicles/          # 车型数据文件夹
├── Apply/             # 申请编号模块
├── Fillin/            # 填写功能模块
├── Upload/            # 上传审批模块
├── Final_Approval_Documents/  # 审批文档
├── logs/              # 日志文件
└── GUI_Core/          # 核心GUI模块
\end{lstlisting}

图2 系统目录结构图 应插入此处

\section{输入输出说明}

\subsection{输入数据格式}

\subsubsection{车型代号}
\begin{itemize}
    \item 格式：字母数字组合，建议4-8位
    \item 示例：\texttt{HYHB}、\texttt{TEST01}、\texttt{DEMO\_A}
    \item 注意：避免使用特殊字符和空格
\end{itemize}

\subsubsection{登录凭据}
\begin{itemize}
    \item 用户名：OA系统有效用户名
    \item 密码：对应的登录密码
    \item 权限：需要具备文件申请和上传权限
\end{itemize}

\subsubsection{模板数据}
车型信息Excel文件包含以下工作表：

\begin{table}[H]
\centering
\begin{tabular}{|c|c|c|}
\hline
\textbf{工作表名} & \textbf{用途} & \textbf{主要字段} \\
\hline
Info & 基础信息 & 车型代号、项目名称、人员信息 \\
\hline
File\_Code & 文件编码 & 文件类型、编码规则 \\
\hline
File\_Status & 状态跟踪 & 文件名、编号、处理状态 \\
\hline
\end{tabular}
\caption{模板数据结构}
\end{table}

\subsection{输出数据格式}

\subsubsection{处理后文件}
\begin{itemize}
    \item 文件命名：\texttt{[编号]-[车型代号]项目VSE[文件类型].xlsx/docx}
    \item 存储位置：\texttt{Vehicles/[车型代号]/Numbered\_and\_Filled/}
    \item 内容：已填写车型信息的完整文档
\end{itemize}

\subsubsection{状态记录}
\begin{itemize}
    \item 格式：Excel表格中的Y/N标记
    \item 字段：\texttt{is\_fillin}（填写状态）、\texttt{is\_upload}（上传状态）
    \item 更新：实时更新处理进度
\end{itemize}

\subsubsection{日志文件}
\begin{itemize}
    \item 格式：\texttt{vehicle\_management\_YYYYMMDD\_HHMMSS.log}
    \item 内容：时间戳-级别-操作消息
    \item 位置：\texttt{logs/} 文件夹
\end{itemize}

\section{操作流程与使用方法}

\subsection{系统启动}

\subsubsection{启动程序}
\begin{enumerate}
    \item 双击 \texttt{Scripts/run\_gui.bat} 启动系统
    \item 等待程序加载完成，出现主界面
    \item 检查状态栏显示当前时间，确认程序正常运行
\end{enumerate}

\subsubsection{界面布局说明}
主界面包含以下区域：
\begin{itemize}
    \item \textbf{顶部登录区}：用户名、密码输入框和数据管理员名单按钮
    \item \textbf{车型设置区}：车型代号选择和设置按钮
    \item \textbf{选项卡区域}：文件操作、上传审批、日志监控三个选项卡
    \item \textbf{快捷操作区}：常用文件夹快速访问按钮
    \item \textbf{状态栏}：显示当前时间和操作状态
\end{itemize}

图3 程序主界面示意图 应插入此处

\subsection{车型信息设置}

\subsubsection{新建车型}
\begin{enumerate}
    \item 在车型代号下拉框中输入新的车型代号
    \item 点击"设置车型"按钮
    \item 系统自动创建车型文件夹结构
    \item 弹出成功提示后，系统会自动打开数据管理员名单图片
    \item 编辑自动打开的Excel配置文件，填写相关人员信息
\end{enumerate}

\subsubsection{选择现有车型}
\begin{enumerate}
    \item 点击车型代号下拉框
    \item 从列表中选择已存在的车型代号
    \item 系统自动加载该车型的文件状态信息
    \item 在文件状态表中查看当前处理进度
\end{enumerate}

\subsubsection{车型信息配置}
在打开的Excel文件中配置以下信息：

\begin{table}[H]
\centering
\begin{tabular}{|c|c|c|}
\hline
\textbf{角色} & \textbf{说明} & \textbf{必填} \\
\hline
车型代号 & 当前车型的标识代码 & 是 \\
\hline
数据管理员 & 负责数据管理的人员 & 是 \\
\hline
科长 & 部门负责人 & 是 \\
\hline
相关方 & 项目相关人员 & 否 \\
\hline
\end{tabular}
\caption{车型信息配置项}
\end{table}

\subsection{文件处理操作}

\subsubsection{选择文件类型}
在"文件操作"选项卡中：
\begin{enumerate}
    \item 选择需要处理的DVP文件（软件设计验证计划）
    \item 选择需要处理的PPL文件（软件开发匹配测试计划）
    \item 在FN文件区域选择相应的接口定义通知单：
    \begin{itemize}
        \item IMU、VCU、IPB等车载控制器接口
        \item ESP+BWA、EPS、EPSA、EPB等底盘系统接口
        \item DiSus系列悬架系统接口
        \item 跨域计算平台接口
    \end{itemize}
    \item 可使用"全选"和"取消全选"按钮快速操作
\end{enumerate}

\subsubsection{执行文件处理}
\begin{enumerate}
    \item 确保已输入正确的用户名和密码
    \item 确认已选择要处理的文件类型
    \item 点击"执行文件处理"按钮
    \item 观察进度条和状态信息：
    \begin{itemize}
        \item 10\%：准备工作环境
        \item 20\%：复制模板文件
        \item 30\%：配置Apply模块
        \item 40\%：申请文件编号
        \item 60\%：分发文件到Fillin模块
        \item 70\%：填写文件内容
        \item 90\%：收集处理结果
        \item 100\%：清理敏感信息，处理完成
    \end{itemize}
    \item 处理完成后，点击"查看处理结果"按钮查看输出文件
\end{enumerate}

\subsubsection{处理结果验证}
\begin{enumerate}
    \item 检查 \texttt{Vehicles/[车型代号]/Numbered\_and\_Filled/} 文件夹
    \item 验证文件命名格式：\texttt{[编号]-[完整文件名]}
    \item 打开文件确认内容已正确填写车型信息
    \item 在文件状态表中确认处理状态已更新为"Y"
\end{enumerate}

图4 文件处理流程图 应插入此处

\subsection{上传审批操作}

\subsubsection{准备审批文件}
\begin{enumerate}
    \item 将需要上传的最终文件复制到 \texttt{Final\_Approval\_Documents} 文件夹
    \item 确保文件名格式正确，包含编号信息
    \item 检查文件内容完整性和准确性
\end{enumerate}

\subsubsection{执行上传操作}
\begin{enumerate}
    \item 切换到"上传审批"选项卡
    \item 确认登录信息已正确填写
    \item 点击"上传审批文件"按钮
    \item 系统执行以下步骤：
    \begin{itemize}
        \item 20\%：复制模板数据文件到根目录
        \item 40\%：检查审批文件夹
        \item 60\%：配置Upload模块
        \item 80\%：执行上传操作
        \item 100\%：完成审批流程提交
    \end{itemize}
    \item 上传完成后，在OA系统中确认审批流程已启动
\end{enumerate}

\subsubsection{审批状态跟踪}
\begin{enumerate}
    \item 在"日志监控"选项卡查看上传详细日志
    \item 登录OA系统查看审批进度
    \item 根据需要联系相关审批人员
\end{enumerate}

\section{常见问题与注意事项}

\subsection{安装配置问题}

\subsubsection{Python环境问题}
\textbf{问题}：提示"未找到Python环境"
\textbf{解决方案}：
\begin{enumerate}
    \item 确认Python 3.8+已正确安装
    \item 检查Python是否已添加到系统PATH
    \item 在命令行执行 \texttt{python --version} 验证安装
\end{enumerate}

\subsubsection{依赖包安装失败}
\textbf{问题}：pip安装依赖包时出现错误
\textbf{解决方案}：
\begin{enumerate}
    \item 升级pip：\texttt{python -m pip install --upgrade pip}
    \item 使用国内镜像：\texttt{pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt}
    \item 逐个安装失败的包，查看具体错误信息
\end{enumerate}

\subsubsection{ChromeDriver配置问题}
\textbf{问题}：Selenium无法启动Chrome浏览器
\textbf{解决方案}：
\begin{enumerate}
    \item 确认Chrome浏览器版本
    \item 下载匹配版本的ChromeDriver
    \item 将ChromeDriver.exe放置在系统PATH目录中
    \item 或将ChromeDriver.exe放在程序根目录
\end{enumerate}

\subsection{操作使用问题}

\subsubsection{车型设置失败}
\textbf{问题}：点击"设置车型"后出现错误
\textbf{解决方案}：
\begin{enumerate}
    \item 检查车型代号格式，避免特殊字符
    \item 确认Templates文件夹中存在Fill\_Template\_Data.xlsx
    \item 检查文件权限，确保可以创建和修改文件
    \item 查看日志文件获取详细错误信息
\end{enumerate}

\subsubsection{文件处理中断}
\textbf{问题}：文件处理过程中程序停止响应
\textbf{解决方案}：
\begin{enumerate}
    \item 检查网络连接是否稳定
    \item 确认OA系统登录凭据正确
    \item 重启程序，重新执行处理操作
    \item 如果问题持续，可分步骤执行（先申请编号，再填写内容）
\end{enumerate}

\subsubsection{上传审批失败}
\textbf{问题}：上传文件到OA系统时失败
\textbf{解决方案}：
\begin{enumerate}
    \item 确认网络可以正常访问OA系统
    \item 检查用户账号是否具有上传权限
    \item 验证审批文件夹中的文件格式和大小
    \item 查看Upload模块的详细日志
\end{enumerate}

\subsection{性能优化建议}

\subsubsection{提高处理速度}
\begin{itemize}
    \item 关闭不必要的后台程序，释放系统资源
    \item 使用静默模式减少界面渲染开销
    \item 分批处理大量文件，避免一次性处理过多
    \item 定期清理临时文件和日志文件
\end{itemize}

\subsubsection{减少错误发生}
\begin{itemize}
    \item 使用稳定的网络环境
    \item 定期备份重要的配置文件和模板
    \item 避免在处理过程中操作其他程序
    \item 保持Chrome浏览器和ChromeDriver版本同步
\end{itemize}

\subsection{数据安全注意事项}

\subsubsection{敏感信息保护}
\begin{itemize}
    \item 系统会自动清理处理过程中的敏感信息
    \item 不要在共享计算机上保存登录凭据
    \item 定期更改OA系统密码
    \item 及时删除不需要的临时文件
\end{itemize}

\subsubsection{数据备份建议}
\begin{itemize}
    \item 定期备份Vehicles文件夹中的重要数据
    \item 保存模板文件的副本
    \item 备份系统配置文件
    \item 建立版本控制机制，跟踪文件变更
\end{itemize}

图5 系统维护流程图 应插入此处

\subsection{技术支持}

\subsubsection{日志分析}
当遇到问题时，可通过以下方式获取详细信息：
\begin{enumerate}
    \item 查看"日志监控"选项卡的实时日志
    \item 打开logs文件夹中的日志文件
    \item 分析错误信息的时间戳和详细描述
    \item 根据错误类型采取相应的解决措施
\end{enumerate}

\subsubsection{问题报告}
如需技术支持，请提供以下信息：
\begin{itemize}
    \item 操作系统版本和Python版本
    \item 具体的操作步骤和错误现象
    \item 相关的日志文件内容
    \item 系统配置和环境信息
\end{itemize}

通过遵循本使用说明书的指导，用户可以高效地使用车型文件管理系统，实现文档管理流程的自动化，显著提高工作效率。如有任何疑问，请参考常见问题部分或联系技术支持。

\end{document}
