#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修正后的文件复制功能
"""

import sys
import os
from pathlib import Path
import pandas as pd

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from file_manager import FileManager

def test_fixed_file_copying():
    """测试修正后的文件复制功能"""
    print("开始测试修正后的文件复制功能...")
    
    base_path = Path(__file__).parent
    file_manager = FileManager(base_path)
    
    # 测试车型代号
    test_vehicle = "QYHB"
    
    try:
        # 首先设置车型文件夹
        print(f"设置车型文件夹: {test_vehicle}")
        created, target_file = file_manager.setup_vehicle_folder(test_vehicle)
        
        # 测试复制DVP文件
        selected_files = ["DVP"]
        print(f"\n测试复制文件: {selected_files}")
        
        copied_files = file_manager.copy_template_files(test_vehicle, selected_files)
        
        print(f"✅ 成功复制文件: {copied_files}")
        
        # 检查文件名是否正确
        vehicle_dir = file_manager.vehicles_path / test_vehicle
        print(f"\n检查复制的文件:")
        for file_name in copied_files:
            file_path = vehicle_dir / file_name
            if file_path.exists():
                print(f"✅ 文件存在: {file_name}")
                if "VSE" in file_name:
                    print(f"  ✅ 文件名包含VSE")
                else:
                    print(f"  ❌ 文件名缺少VSE")
            else:
                print(f"❌ 文件不存在: {file_name}")
        
        # 测试get_files_for_apply_id方法
        print(f"\n测试get_files_for_apply_id方法:")
        apply_files = file_manager.get_files_for_apply_id(test_vehicle)
        print(f"需要申请编号的文件数量: {len(apply_files)}")
        for file_info in apply_files:
            print(f"  - {file_info['file_name']} (车型: {file_info['vehicle_code']})")
        
        if apply_files:
            print("✅ get_files_for_apply_id方法工作正常")
        else:
            print("⚠️  没有找到需要申请编号的文件")
        
        print("✅ 修正后的文件复制功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixed_file_copying()
