import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import re
import os


class DMSAutomator:
    """比亚迪DMS系统自动化操作类"""
    
    def __init__(self, silent_mode=False, test_mode=False, timeout=20):
        self.silent_mode = silent_mode
        self.test_mode = test_mode
        self.timeout = timeout
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)
        
        # DMS系统URL
        self.base_url = "https://gcy.byd.com/dms/#/home"
        self.dashboard_url = "https://gcy.byd.com/dms/#/dashboard"
        
        # 文件类型映射
        self.document_type_mapping = {
            'DVP': 'DVP-系统设计验证计划',
            'PPL': 'PPL-软件开发匹配测试计划', 
            'FN': 'FN-接口定义通知单'        }
        
    def setup_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            if self.silent_mode:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 窗口最大化
            self.driver.maximize_window()
            self.logger.info("浏览器窗口已最大化")
            
            self.wait = WebDriverWait(self.driver, self.timeout)
            self.logger.info("Chrome驱动初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"初始化Chrome驱动失败: {str(e)}")
            return False
    
    def close_driver(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.wait = None
            self.logger.info("Chrome驱动已关闭")
    
    def safe_click(self, locator, timeout=None):
        """安全点击元素"""
        try:
            if timeout is None:
                timeout = self.timeout
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable(locator)
            )
            # 使用ActionChains确保元素可见并点击
            actions = ActionChains(self.driver)
            actions.move_to_element(element).click().perform()
            time.sleep(1)  # 短暂等待
            return True
        except Exception as e:
            self.logger.error(f"点击元素失败 {locator}: {str(e)}")
            return False
    
    def safe_type(self, locator, text, clear_first=True, timeout=None):
        """安全输入文本"""
        try:
            if timeout is None:
                timeout = self.timeout
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            if clear_first:
                element.clear()
            element.send_keys(text)
            time.sleep(0.5)  # 短暂等待
            return True
        except Exception as e:
            self.logger.error(f"输入文本失败 {locator}: {str(e)}")
            return False
    
    def wait_for_page_load(self, timeout=10):
        """等待页面加载完成"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            time.sleep(2)  # 额外等待确保页面完全加载
            return True
        except Exception as e:
            self.logger.error(f"等待页面加载失败: {str(e)}")
            return False
    
    def login(self, username: str, password: str) -> bool:
        """登录DMS系统
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            登录成功返回True，失败返回False
        """
        try:
            self.logger.info("开始登录DMS系统")
            
            # 打开DMS主页
            self.driver.get(self.base_url)
            self.wait_for_page_load()
            
            # 点击登录按钮
            login_button_locator = (By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span")
            if not self.safe_click(login_button_locator):
                self.logger.error("无法点击登录按钮")
                return False
            
            self.wait_for_page_load()
            
            # 输入用户名
            username_locator = (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div/div/div/input")
            if not self.safe_type(username_locator, username):
                self.logger.error("无法输入用户名")
                return False
            
            # 输入密码
            password_locator = (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[2]/div/div/input")
            if not self.safe_type(password_locator, password):
                self.logger.error("无法输入密码")
                return False
            
            # 点击登录按钮
            submit_button_locator = (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[4]/div/button")
            if not self.safe_click(submit_button_locator):
                self.logger.error("无法点击提交登录按钮")
                return False
            
            # 等待登录完成，检查是否跳转到主页
            time.sleep(5)
            current_url = self.driver.current_url
            if "/home" in current_url or "/dashboard" in current_url:
                self.logger.info("登录成功")
                # 自动关闭登录后的弹窗
                self.close_all_popups()
                return True
            else:
                self.logger.error(f"登录失败，当前URL: {current_url}")
                return False
                
        except Exception as e:
            self.logger.error(f"登录过程出错: {str(e)}")
            return False
    
    def close_all_popups(self):
        """自动关闭所有弹窗"""
        try:
            self.logger.info("开始检查并关闭弹窗")
            popup_closed = False
            
            # 检查并关闭智能助手弹窗（弹窗1）
            popup_selectors = [
                # 智能助手弹窗关闭按钮
                "div.maxkb-tips div.maxkb-close",
                ".maxkb-close",
                
                # 智能助手弹窗确认按钮
                "div.maxkb-tips div.maxkb-button button",
                ".maxkb-button button",
                
                # 通知公告弹窗关闭按钮（弹窗2）
                "div#notification_1 i.el-notification__closeBtn",
                ".el-notification__closeBtn",
                "div.el-notification i.el-icon",
                
                # 通用弹窗关闭按钮
                ".el-drawer__close-btn",
                ".el-dialog__close",
                ".el-message-box__close",
                "[aria-label='关闭此对话框']",
                "[aria-label='Close']"
            ]
            
            for selector in popup_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            element.click()
                            self.logger.info(f"关闭弹窗: {selector}")
                            popup_closed = True
                            time.sleep(1)
                except Exception as e:
                    self.logger.debug(f"尝试关闭弹窗失败 {selector}: {str(e)}")
                    continue
            
            # 使用JavaScript强制关闭弹窗
            try:
                # 关闭maxkb智能助手
                js_close_maxkb = """
                var maxkbTips = document.querySelector('.maxkb-tips');
                if (maxkbTips) {
                    maxkbTips.style.display = 'none';
                    maxkbTips.remove();
                }
                """
                self.driver.execute_script(js_close_maxkb)
                
                # 关闭通知弹窗
                js_close_notification = """
                var notifications = document.querySelectorAll('.el-notification');
                notifications.forEach(function(notification) {
                    notification.style.display = 'none';
                    notification.remove();
                });
                """
                self.driver.execute_script(js_close_notification)
                
                self.logger.info("使用JavaScript强制关闭弹窗")
                popup_closed = True
                
            except Exception as e:
                self.logger.debug(f"JavaScript关闭弹窗失败: {str(e)}")
            
            if popup_closed:
                time.sleep(2)  # 等待弹窗关闭完成
                self.logger.info("弹窗关闭完成")
            else:
                self.logger.info("未发现需要关闭的弹窗")
                
        except Exception as e:
            self.logger.error(f"关闭弹窗过程出错: {str(e)}")
    
    def click_apply_number_button(self):
        """点击申请编号按钮，使用更精确的定位"""
        try:
            # 基于HTML结构的多种定位策略
            apply_button_selectors = [
                # 基于文本内容定位
                "//button[contains(., '申请编号')]",
                "//span[contains(text(), '申请编号')]/..",
                
                # 基于class和结构定位
                "button.el-button.el-button--primary:contains('申请编号')",
                ".el-button:contains('申请编号')",
                
                # 原有的xpath定位（作为后备）
                "//*[@id='main']/div/div/div/div/div/div[3]/span"
            ]
            
            for selector in apply_button_selectors:
                try:
                    if selector.startswith("//") or selector.startswith("/"):
                        # XPath定位
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        # CSS定位
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    
                    # 点击元素
                    element.click()
                    self.logger.info(f"成功点击申请编号按钮，使用定位器: {selector}")
                    time.sleep(2)
                    return True
                    
                except Exception as e:
                    self.logger.debug(f"定位器 {selector} 失败: {str(e)}")
                    continue
            
            self.logger.error("所有申请编号按钮定位策略都失败了")
            return False
            
        except Exception as e:
            self.logger.error(f"点击申请编号按钮失败: {str(e)}")
            return False
      def apply_file_numbers(self, file_list: List[Dict], project_code: str) -> List[Dict]:
        """申请文件编号
        
        Args:
            file_list: 文件信息列表，每个包含文件名、类型等信息
            project_code: 项目代号，如"HYHB"
            
        Returns:
            包含申请到的编号的文件信息列表
        """
        try:
            self.logger.info(f"开始为项目 {project_code} 申请文件编号")
            
            # 确保在主页面
            if "/home" not in self.driver.current_url:
                self.driver.get(self.base_url)
                self.wait_for_page_load()
                # 关闭可能的弹窗
                self.close_all_popups()
            
            results = []
            
            # 按文件类型分组处理
            file_groups = self._group_files_by_type(file_list)
            
            for file_type, files in file_groups.items():
                self.logger.info(f"处理文件类型: {file_type}, 文件数量: {len(files)}")
                
                for file_info in files:
                    file_name = file_info.get('file_name', '').replace('.xlsx', '').replace('.xls', '')
                    self.logger.info(f"处理文件: {file_name}")
                    
                    # 点击申请编号按钮
                    if not self.click_apply_number_button():
                        self.logger.error(f"无法打开申请编号界面，跳过文件: {file_name}")
                        file_info['status'] = '申请失败'
                        file_info['error'] = '无法打开申请编号界面'
                        results.append(file_info)
                        continue
                    
                    # 处理申请编号模态框
                    generated_number = self.handle_apply_number_modal(file_type, file_name, project_code)
                    
                    if generated_number:
                        file_info['file_code'] = generated_number
                        file_info['status'] = '申请成功'
                        self.logger.info(f"文件 {file_name} 申请编号成功: {generated_number}")
                    else:
                        file_info['status'] = '申请失败'
                        file_info['error'] = '无法生成编号'
                        self.logger.error(f"文件 {file_name} 申请编号失败")
                    
                    results.append(file_info)
                    
                    # 短暂等待，避免操作过快
                    time.sleep(2)
            
            self.logger.info(f"申请编号完成，共处理 {len(results)} 个文件")
            return results
            
        except Exception as e:
            self.logger.error(f"申请文件编号过程出错: {str(e)}")
            return file_list  # 返回原始列表
                    self.logger.error("无法选择项目文件选项")
                    continue
                
                # 输入文档类型
                doc_type = self.document_type_mapping.get(file_type, file_type)
                doc_type_input = (By.ID, "el-id-7049-681")  # 这个ID可能会变化，需要根据实际情况调整
                if not self.safe_type(doc_type_input, doc_type):
                    self.logger.error(f"无法输入文档类型: {doc_type}")
                    continue
                
                # 等待下拉选项出现并点击匹配的选项
                time.sleep(2)
                doc_option_xpath = f"//li/span[contains(text(), '{doc_type}')]"
                doc_option = (By.XPATH, doc_option_xpath)
                if not self.safe_click(doc_option):
                    self.logger.error(f"无法选择文档类型选项: {doc_type}")
                    continue
                
                # 输入项目代号
                project_code_input = (By.ID, "el-id-7049-685")  # 这个ID可能会变化
                if not self.safe_type(project_code_input, project_code):
                    self.logger.error(f"无法输入项目代号: {project_code}")
                    continue
                
                # 等待并选择项目代号选项
                time.sleep(2)
                project_option = (By.XPATH, f"//span[contains(text(), '{project_code}')]")
                if not self.safe_click(project_option):
                    self.logger.error(f"无法选择项目代号选项: {project_code}")
                    continue
                
                # 为每个文件添加行并申请编号
                for file_info in files:
                    # 点击新增行按钮
                    add_row_button = (By.XPATH, "//*[@id='el-collapse-content-19']/div/button/span")
                    if not self.safe_click(add_row_button):
                        self.logger.error("无法点击新增行按钮")
                        continue
                    
                    time.sleep(1)
                    
                    # 输入文档名称（不含扩展名）
                    file_name = Path(file_info['file_name']).stem
                    doc_name_input = (By.XPATH, "//table/tbody/tr[last()]//td[2]//input")
                    if not self.safe_type(doc_name_input, file_name):
                        self.logger.error(f"无法输入文档名称: {file_name}")
                        continue
                    
                    # 点击生成编号按钮
                    generate_button = (By.XPATH, "//table/tbody/tr[last()]//td[3]//button")
                    if not self.safe_click(generate_button):
                        self.logger.error("无法点击生成编号按钮")
                        continue
                    
                    time.sleep(2)  # 等待编号生成
                    
                    # 获取生成的编号
                    try:
                        number_element = self.driver.find_element(By.XPATH, "//table/tbody/tr[last()]//td[4]//span")
                        generated_number = number_element.text.strip()
                        if generated_number:
                            file_info['file_code'] = generated_number
                            self.logger.info(f"为文件 {file_name} 生成编号: {generated_number}")
                        else:
                            self.logger.error(f"生成的编号为空: {file_name}")
                    except Exception as e:
                        self.logger.error(f"无法获取生成的编号: {str(e)}")
                
                # 提交申请
                submit_button = (By.XPATH, "//*[@id='main']/div/div/div[2]/div/div/div[2]/div/button[@class='el-button el-button--primary']/span")
                if not self.safe_click(submit_button):
                    self.logger.error("无法点击提交按钮")
                
                time.sleep(3)  # 等待提交完成
                
                results.extend(files)
            
            self.logger.info(f"完成申请编号，共处理 {len(results)} 个文件")
            return results
            
        except Exception as e:
            self.logger.error(f"申请编号过程出错: {str(e)}")
            return []
    
    def upload_for_approval(self, file_info: Dict, file_path: str, approvers: Dict) -> bool:
        """上传文件进行审批
        
        Args:
            file_info: 文件信息字典，包含file_code等
            file_path: 要上传的文件路径
            approvers: 审批人信息字典，包含各类别审批人
            
        Returns:
            上传成功返回True，失败返回False
        """
        try:
            self.logger.info(f"开始上传文件进行审批: {file_info.get('file_code', 'unknown')}")
            
            # 确保在主页面或仪表板页面
            if "/dashboard" not in self.driver.current_url:
                self.driver.get(self.dashboard_url)
                self.wait_for_page_load()
            
            # 点击文档创建功能
            doc_create_button = (By.XPATH, "//*[@id='main']/div/div/div/div/div/div/span[contains(text(), '文档创建')]")
            if not self.safe_click(doc_create_button):
                self.logger.error("无法点击文档创建按钮")
                return False
            
            self.wait_for_page_load()
            
            # 输入文档编号
            file_code = file_info.get('file_code', '')
            if not file_code:
                self.logger.error("文件编号为空")
                return False
            
            doc_number_input = (By.XPATH, "//input[@placeholder='请输入文档编号' or contains(@id, '文档编号')]")
            if not self.safe_type(doc_number_input, file_code):
                self.logger.error(f"无法输入文档编号: {file_code}")
                return False
            
            # 输入文档名称
            file_name = Path(file_path).stem
            doc_name_input = (By.XPATH, "//input[@placeholder='请输入文档名称' or contains(@id, '文档名称')]")
            if not self.safe_type(doc_name_input, file_name):
                self.logger.error(f"无法输入文档名称: {file_name}")
                return False
            
            # 选择文档类型
            doc_type = file_info.get('file_type', '')
            if doc_type:
                doc_type_mapped = self.document_type_mapping.get(doc_type, doc_type)
                doc_type_dropdown = (By.XPATH, "//input[@placeholder='请选择文档类型']/..//i")
                if self.safe_click(doc_type_dropdown):
                    time.sleep(1)
                    doc_type_option = (By.XPATH, f"//li/span[contains(text(), '{doc_type_mapped}')]")
                    self.safe_click(doc_type_option)
            
            # 填写审批人信息
            self._fill_approvers(approvers)
            
            # 上传文件
            if not self._upload_file(file_path):
                self.logger.error("文件上传失败")
                return False
            
            # 提交审批申请
            submit_button = (By.XPATH, "//button[contains(text(), '提交') or contains(text(), '确认')]")
            if not self.safe_click(submit_button):
                self.logger.error("无法点击提交按钮")
                return False
            
            time.sleep(3)  # 等待提交完成
            
            self.logger.info(f"文件上传审批成功: {file_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"上传审批过程出错: {str(e)}")
            return False
    
    def _group_files_by_type(self, file_list: List[Dict]) -> Dict[str, List[Dict]]:
        """按文件类型分组文件"""
        groups = {}
        for file_info in file_list:
            file_type = file_info.get('file_type', 'Unknown')
            if file_type not in groups:
                groups[file_type] = []
            groups[file_type].append(file_info)
        return groups
    
    def _fill_approvers(self, approvers: Dict) -> bool:
        """填写审批人信息"""
        try:
            # 根据approvers字典填写各类审批人
            # 这里需要根据实际的页面结构来实现
            for approver_type, approver_name in approvers.items():
                if approver_name:
                    # 找到对应的审批人输入框并填写
                    approver_input = (By.XPATH, f"//input[contains(@placeholder, '{approver_type}')]")
                    self.safe_type(approver_input, approver_name)
            
            return True
        except Exception as e:
            self.logger.error(f"填写审批人信息失败: {str(e)}")
            return False
    
    def _upload_file(self, file_path: str) -> bool:
        """上传文件"""
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"文件不存在: {file_path}")
                return False
            
            # 找到文件上传控件
            file_input = self.driver.find_element(By.XPATH, "//input[@type='file']")
            file_input.send_keys(file_path)
            
            time.sleep(2)  # 等待文件上传
            
            self.logger.info(f"文件上传成功: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"文件上传失败: {str(e)}")
            return False
    
    def handle_apply_number_modal(self, file_type: str, file_name: str, project_code: str) -> Optional[str]:
        """处理申请编号模态框，基于HTML结构优化
        
        Args:
            file_type: 文件类型，如'DVP', 'PPL', 'FN'
            file_name: 文件名称
            project_code: 项目代号，如'HYHB'
            
        Returns:
            申请到的编号，失败返回None
        """
        try:
            self.logger.info(f"处理申请编号模态框: {file_type} - {file_name}")
            
            # 等待申请编号模态框出现
            modal_selector = "div.el-drawer[aria-modal='true'][aria-labelledby*='3055']"
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, modal_selector))
            )
            self.logger.info("申请编号模态框已出现")
            
            # 基本信息部分已经预设，检查文件类型
            file_type_display = self.driver.find_element(By.CSS_SELECTOR, 
                "div.el-select__selected-item.el-select__placeholder span").text
            if "项目文件" not in file_type_display:
                self.logger.warning(f"文件类型显示异常: {file_type_display}")
            
            # 检查项目文档类型
            doc_type_mapping = {
                'DVP': 'DVP-系统设计验证计划',
                'PPL': 'PPL-软件开发匹配测试计划',
                'FN': 'FN-接口定义通知单'
            }
            
            expected_doc_type = doc_type_mapping.get(file_type, f"{file_type}-未知类型")
            doc_type_display_selector = "div.el-select__selected-item.el-select__placeholder span"
            doc_type_elements = self.driver.find_elements(By.CSS_SELECTOR, doc_type_display_selector)
            
            if len(doc_type_elements) >= 2:
                actual_doc_type = doc_type_elements[1].text
                if expected_doc_type not in actual_doc_type:
                    self.logger.warning(f"文档类型不匹配，期望: {expected_doc_type}, 实际: {actual_doc_type}")
            
            # 检查项目代号
            project_code_display_selectors = [
                "div.el-select__selected-item.el-select__placeholder span",
                "input[id*='3055'][disabled]"
            ]
            
            for selector in project_code_display_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if project_code in element.text or element.get_attribute('value') == project_code:
                            self.logger.info(f"项目代号匹配: {project_code}")
                            break
                except:
                    continue
            
            # 滚动到新增文档编号部分
            new_doc_section = self.driver.find_element(By.ID, "el-collapse-content-216")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", new_doc_section)
            time.sleep(1)
            
            # 点击新增行按钮
            add_row_selectors = [
                "button.el-button.el-button--primary[style*='background-color: rgb(1, 69, 255)'] span:contains('新增行')",
                "button:contains('新增行')",
                "//button[contains(., '新增行')]"
            ]
            
            add_row_clicked = False
            for selector in add_row_selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    element.click()
                    self.logger.info("成功点击新增行按钮")
                    add_row_clicked = True
                    time.sleep(2)
                    break
                except Exception as e:
                    self.logger.debug(f"新增行按钮定位失败 {selector}: {str(e)}")
                    continue
            
            if not add_row_clicked:
                self.logger.error("无法点击新增行按钮")
                return None
            
            # 输入文档名称
            doc_name_selectors = [
                "input.el-input__inner[maxlength='100'][id*='3055']",
                "td.el-table_3_column_21 input.el-input__inner",
                "//input[@maxlength='100' and @type='text']"
            ]
            
            doc_name_filled = False
            for selector in doc_name_selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    
                    element.clear()
                    element.send_keys(file_name)
                    self.logger.info(f"成功输入文档名称: {file_name}")
                    doc_name_filled = True
                    time.sleep(1)
                    break
                except Exception as e:
                    self.logger.debug(f"文档名称输入失败 {selector}: {str(e)}")
                    continue
            
            if not doc_name_filled:
                self.logger.error("无法输入文档名称")
                return None
            
            # 点击生成编号按钮
            generate_btn_selectors = [
                "button:contains('生成编号')",
                "//button[contains(., '生成编号')]",
                "td.el-table_3_column_22 button.el-button"
            ]
            
            generate_clicked = False
            for selector in generate_btn_selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    element.click()
                    self.logger.info("成功点击生成编号按钮")
                    generate_clicked = True
                    time.sleep(3)  # 等待编号生成
                    break
                except Exception as e:
                    self.logger.debug(f"生成编号按钮点击失败 {selector}: {str(e)}")
                    continue
            
            if not generate_clicked:
                self.logger.error("无法点击生成编号按钮")
                return None
            
            # 获取生成的编号
            generated_number = None
            number_selectors = [
                "td.el-table_3_column_23 span",
                "//td[contains(@class, 'el-table_3_column_23')]//span",
                "span:contains('_')"  # 编号通常包含下划线
            ]
            
            for selector in number_selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    
                    text = element.text.strip()
                    if text and '_' in text and len(text) > 5:  # 基本验证编号格式
                        generated_number = text
                        self.logger.info(f"获取到生成的编号: {generated_number}")
                        break
                except Exception as e:
                    self.logger.debug(f"获取编号失败 {selector}: {str(e)}")
                    continue
            
            if not generated_number:
                self.logger.error("无法获取生成的编号")
                return None
            
            # 点击提交按钮
            submit_selectors = [
                "div.el-drawer__footer button.el-button--primary span:contains('提交')",
                "//button[contains(., '提交')]",
                "div.dialog-footer button.el-button--primary"
            ]
            
            submit_clicked = False
            for selector in submit_selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    element.click()
                    self.logger.info("成功点击提交按钮")
                    submit_clicked = True
                    time.sleep(3)  # 等待提交完成
                    break
                except Exception as e:
                    self.logger.debug(f"提交按钮点击失败 {selector}: {str(e)}")
                    continue
            
            if not submit_clicked:
                self.logger.error("无法点击提交按钮")
                return None
            
            # 等待模态框关闭
            try:
                WebDriverWait(self.driver, 10).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, modal_selector))
                )
                self.logger.info("申请编号模态框已关闭")
            except TimeoutException:
                self.logger.warning("模态框未完全关闭，但继续处理")
            
            return generated_number
            
        except Exception as e:
            self.logger.error(f"处理申请编号模态框失败: {str(e)}")
            return None


# 保持向后兼容性的别名
SeleniumAutomator = DMSAutomator


class LoginAutomator(DMSAutomator):
    """登录专用自动化类"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def login_interactive(self) -> Tuple[bool, str, str]:
        """交互式登录，返回用户名和密码但不保存
        
        Returns:
            (success, username, password)
        """
        try:
            # 这里可以集成GUI输入框来获取用户名密码
            # 目前返回空值，由调用者处理
            return False, "", ""
        except Exception as e:
            self.logger.error(f"交互式登录失败: {str(e)}")
            return False, "", ""


class ApplyIDAutomator(DMSAutomator):
    """申请编号专用自动化类"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def batch_apply_file_numbers(self, file_groups: Dict[str, List[Dict]], project_code: str) -> Dict[str, List[Dict]]:
        """批量申请文件编号，按类型分批处理
        
        Args:
            file_groups: 按类型分组的文件字典 
            project_code: 项目代号
            
        Returns:
            包含申请结果的文件分组字典
        """
        results = {}
        
        for file_type, files in file_groups.items():
            self.logger.info(f"开始申请 {file_type} 类型文件编号")
            result_files = self.apply_file_numbers(files, project_code)
            results[file_type] = result_files
            
            # 每个类型之间稍作等待
            time.sleep(3)
        
        return results


class UploadApprovalAutomator(DMSAutomator):
    """上传审批专用自动化类"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def batch_upload_for_approval(self, file_list: List[Dict], file_paths: List[str], approvers: Dict) -> List[bool]:
        """批量上传文件进行审批
        
        Args:
            file_list: 文件信息列表
            file_paths: 文件路径列表  
            approvers: 审批人信息
            
        Returns:
            每个文件的上传结果列表
        """
        results = []
        
        for i, (file_info, file_path) in enumerate(zip(file_list, file_paths)):
            self.logger.info(f"上传第 {i+1}/{len(file_list)} 个文件")
            result = self.upload_for_approval(file_info, file_path, approvers)
            results.append(result)
            
            # 每个文件之间稍作等待
            if i < len(file_list) - 1:
                time.sleep(5)
        
        return results
