"""
简单的日志清理工具
不依赖外部库，可以直接使用
"""

import os
import shutil
from datetime import datetime


def simple_log_cleanup():
    """简单的日志清理函数"""
    log_file = "upload_approval.log"
    max_size_mb = 5
    
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    # 检查文件大小
    file_size = os.path.getsize(log_file)
    max_size = max_size_mb * 1024 * 1024
    
    print(f"当前日志文件大小: {file_size / 1024 / 1024:.2f} MB")
    print(f"最大允许大小: {max_size_mb} MB")
    
    if file_size > max_size:
        # 创建备份
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{log_file}.backup_{timestamp}"
        
        try:
            # 备份当前日志
            shutil.copy2(log_file, backup_file)
            print(f"已创建备份: {backup_file}")
            
            # 清空当前日志文件
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 日志清理 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            
            print(f"日志文件已清理")
            
            # 清理旧备份（保留最近3个）
            cleanup_old_backups(log_file)
            
        except Exception as e:
            print(f"清理失败: {e}")
    else:
        print("日志文件大小正常，无需清理")


def cleanup_old_backups(log_file, keep_count=3):
    """清理旧的备份文件"""
    dir_path = os.path.dirname(log_file) or "."
    base_name = os.path.basename(log_file)
    
    # 找到所有备份文件
    backup_files = []
    for file in os.listdir(dir_path):
        if file.startswith(f"{base_name}.backup_"):
            file_path = os.path.join(dir_path, file)
            if os.path.isfile(file_path):
                mtime = os.path.getmtime(file_path)
                backup_files.append((file_path, mtime))
    
    # 按时间排序（最新的在前）
    backup_files.sort(key=lambda x: x[1], reverse=True)
    
    # 删除超过保留数量的备份
    if len(backup_files) > keep_count:
        for file_path, _ in backup_files[keep_count:]:
            try:
                os.remove(file_path)
                print(f"删除旧备份: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"删除备份失败: {e}")


def get_log_status():
    """获取日志状态"""
    log_file = "upload_approval.log"
    
    if not os.path.exists(log_file):
        print("日志文件不存在")
        return
    
    file_size = os.path.getsize(log_file)
    print(f"日志文件: {log_file}")
    print(f"文件大小: {file_size / 1024 / 1024:.2f} MB")
    
    # 统计备份文件
    dir_path = os.path.dirname(log_file) or "."
    base_name = os.path.basename(log_file)
    backup_count = 0
    
    for file in os.listdir(dir_path):
        if file.startswith(f"{base_name}.backup_"):
            backup_count += 1
    
    print(f"备份文件数量: {backup_count}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == "status":
            get_log_status()
        elif command == "clean":
            simple_log_cleanup()
        else:
            print("用法: python simple_log_cleanup.py [status|clean]")
    else:
        simple_log_cleanup()
