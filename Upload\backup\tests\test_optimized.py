"""
测试优化后的上传审批自动化程序
"""

import logging
import time
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait

# 导入自定义模块
from browser_manager import BrowserManager
from first_page_handler import FirstPageHandler
from utils import ConfigManager, setup_logging


def main():
    """主测试函数"""
    # 设置日志
    logger = setup_logging('test_optimized.log')
    logger.info("🚀 开始测试优化后的上传审批自动化程序")
    
    # 加载配置
    config = ConfigManager.load_config()
    
    # 初始化浏览器管理器
    browser_manager = BrowserManager(config)
    
    try:
        # 启动浏览器并登录
        if not browser_manager.start_browser():
            logger.error("❌ 浏览器启动失败")
            return False
        
        if not browser_manager.login():
            logger.error("❌ 登录失败")
            return False
        
        # 导航到文档创建页面
        if not browser_manager.navigate_to_document_creation():
            logger.error("❌ 导航到文档创建页面失败")
            return False
        
        # 初始化第一页处理器
        first_page_handler = FirstPageHandler(
            browser_manager.driver,
            browser_manager.wait,
            config
        )
        
        # 模拟文件信息
        test_file = {
            'name': 'HYHB_FN_A19-000011_测试文档.docx',
            'path': Path(r'D:\work\Upload\Final_Approval_Documents\test_document.docx'),
            'doc_id': 'HYHB_FN_A19-000011',
            'type': 'FN'
        }
        
        # 测试第一页填写
        logger.info("📝 开始测试第一页信息填写...")
        success = first_page_handler.fill_document_info(
            doc_id=test_file['doc_id'],
            source_file=test_file,
            file_type=test_file['type']
        )
        
        if success:
            logger.info("✅ 第一页信息填写测试成功")
        else:
            logger.error("❌ 第一页信息填写测试失败")
        
        # 保持浏览器打开一段时间以便观察
        logger.info("🔍 保持浏览器打开30秒以便观察结果...")
        time.sleep(30)
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {str(e)}")
        return False
        
    finally:
        # 清理资源
        if browser_manager:
            browser_manager.close()
        logger.info("🏁 测试结束")


if __name__ == "__main__":
    main()
