@echo off
chcp 65001
echo 车型文件管理系统 - 安装向导
echo ================================

echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo 警告：依赖包安装可能不完整，请手动安装缺失的包
    echo.
)

echo.
echo 安装完成！
echo.
echo 使用说明：
echo 1. 运行 run_gui.bat 启动主程序
echo 2. 首次使用请先设置车型信息
echo 3. 申请编号和上传审批功能需要在能访问内网的电脑上运行
echo.

pause
