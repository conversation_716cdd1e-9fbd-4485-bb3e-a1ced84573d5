"""
GUI组件创建模块
包含所有UI组件的创建和布局代码
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QLineEdit, 
    QPushButton, QComboBox, QCheckBox, QGroupBox, QTabWidget, QFrame,
    QTableWidget, QTextEdit, QProgressBar, QHeaderView
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont


class UIComponents:
    """UI组件创建类"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.default_font = QFont()
        self.default_font.setPointSize(11)
        # 全局字体设置
        main_window.setFont(self.default_font)

    def create_title_bar(self, parent_layout):
        """创建标题栏（包含OA登录设置）"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setFixedHeight(110)
        
        title_layout = QVBoxLayout(title_frame)
        
        # 第一行：标题和OA登录设置
        title_row = QHBoxLayout()
        title_label = QLabel("车型文件管理系统")
        title_label.setFont(self.default_font)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        title_row.addWidget(title_label)
        title_row.addStretch()
        
        # OA登录设置区域（放在最右位置）
        oa_label = QLabel("OA登录设置:")
        oa_label.setFont(self.default_font)
        oa_label.setStyleSheet("font-weight: normal; color: #000000;")
        title_row.addWidget(oa_label)
        
        # 用户名输入
        username_label = QLabel("用户名:")
        username_label.setFont(self.default_font)
        username_label.setStyleSheet("")
        title_row.addWidget(username_label)
        self.main_window.username_edit = QLineEdit()
        self.main_window.username_edit.setFont(self.default_font)
        self.main_window.username_edit.setMaximumWidth(160)
        self.main_window.username_edit.setPlaceholderText("请输入用户名")
        title_row.addWidget(self.main_window.username_edit)
        
        # 密码输入
        password_label = QLabel("密码:")
        password_label.setFont(self.default_font)
        password_label.setStyleSheet("")
        title_row.addWidget(password_label)
        self.main_window.password_edit = QLineEdit()
        self.main_window.password_edit.setFont(self.default_font)
        self.main_window.password_edit.setEchoMode(QLineEdit.Password)
        self.main_window.password_edit.setMaximumWidth(160)
        self.main_window.password_edit.setPlaceholderText("请输入密码")
        title_row.addWidget(self.main_window.password_edit)
        
        # 第二行：车型设置和快捷操作
        controls_row = QHBoxLayout()
        
        # 车型选择区域
        vehicle_layout = QHBoxLayout()
        vehicle_label = QLabel("车型代号:")
        vehicle_label.setFont(self.default_font)
        vehicle_layout.addWidget(vehicle_label)
        self.main_window.vehicle_combo = QComboBox()
        self.main_window.vehicle_combo.setFont(self.default_font)
        self.main_window.vehicle_combo.setEditable(True)
        self.main_window.vehicle_combo.setMinimumWidth(100)
        self.main_window.vehicle_combo.currentTextChanged.connect(self.main_window.on_vehicle_changed)
        vehicle_layout.addWidget(self.main_window.vehicle_combo)
        
        self.main_window.setup_vehicle_btn = QPushButton("设置车型")
        self.main_window.setup_vehicle_btn.setFont(self.default_font)
        self.main_window.setup_vehicle_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 10pt;
                font-weight: normal;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 28px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.main_window.setup_vehicle_btn.clicked.connect(self.main_window.setup_vehicle_info)
        vehicle_layout.addWidget(self.main_window.setup_vehicle_btn)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        # 快捷按钮区域
        self.main_window.open_vehicles_btn = QPushButton("车型文件夹")
        self.main_window.open_vehicles_btn.setFont(self.default_font)
        self.main_window.open_vehicles_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 10pt;
                font-weight: normal;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 28px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.main_window.open_vehicles_btn.clicked.connect(self.main_window.open_vehicles_folder)
        
        self.main_window.open_templates_btn = QPushButton("模板文件夹")
        self.main_window.open_templates_btn.setFont(self.default_font)
        self.main_window.open_templates_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-size: 10pt;
                font-weight: normal;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 28px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.main_window.open_templates_btn.clicked.connect(self.main_window.open_templates_folder)
        
        controls_row.addLayout(vehicle_layout)
        controls_row.addWidget(separator)
        controls_row.addWidget(QLabel("快捷操作:").setFont(self.default_font) or QLabel("快捷操作:"))
        controls_row.addWidget(self.main_window.open_vehicles_btn)
        controls_row.addWidget(self.main_window.open_templates_btn)
        controls_row.addStretch()
        
        title_layout.addLayout(title_row)
        title_layout.addLayout(controls_row)
        
        parent_layout.addWidget(title_frame)
    
    def create_main_workspace(self, parent_layout):
        """创建主工作区域"""
        # 创建选项卡widget
        self.main_window.tab_widget = QTabWidget()
        self.main_window.tab_widget.setFont(self.default_font)
        
        # 文件操作选项卡
        self.create_file_operations_tab()
        
        # 上传审批选项卡
        self.create_upload_approval_tab()
        
        # 日志监控选项卡
        self.create_log_monitor_tab()
        
        parent_layout.addWidget(self.main_window.tab_widget)
    
    def create_file_operations_tab(self):
        """创建文件操作选项卡"""
        file_ops_widget = QWidget()
        file_ops_widget.setFont(self.default_font)
        layout = QVBoxLayout(file_ops_widget)
        
        # 说明信息
        info_label = QLabel("选择需要处理的文件类型，系统将自动执行：申请编号 → 填写内容 → 生成完整文件")
        info_label.setFont(self.default_font)
        info_label.setStyleSheet("QLabel { color: #7f8c8d; font-style: italic; padding: 5px 10px; margin: 0px; }")
        info_label.setMaximumHeight(40)
        layout.addWidget(info_label)
        
        # 运行模式选择
        mode_group = QGroupBox("运行模式")
        mode_group.setFont(self.default_font)
        mode_group.setMaximumHeight(70)
        mode_layout = QHBoxLayout(mode_group)
        mode_layout.setContentsMargins(10, 8, 10, 8)
        
        self.main_window.silent_mode_cb = QCheckBox("静默模式（后台运行，不显示浏览器窗口）")
        mode_layout.addWidget(self.main_window.silent_mode_cb)
        mode_layout.addStretch()
        
        layout.addWidget(mode_group)
        
        # 文件选择区域
        file_selection_group = QGroupBox("文件选择")
        file_selection_group.setFont(self.default_font)
        file_selection_layout = QVBoxLayout(file_selection_group)
        file_selection_layout.setSpacing(1)
        
        # 第一行：DVP和PPL文件
        main_files_layout = QHBoxLayout()
        main_files_layout.setSpacing(20)
        main_files_layout.setContentsMargins(10, 12, 10, 12)
        
        self.main_window.dvp_cb = QCheckBox("DVP-系统设计验证计划")
        self.main_window.dvp_cb.setFont(self.default_font)
        self.main_window.dvp_cb.setStyleSheet("QCheckBox { margin: 4px; padding: 4px; }")
        self.main_window.ppl_cb = QCheckBox("PPL-开发匹配测试计划")
        self.main_window.ppl_cb.setFont(self.default_font)
        self.main_window.ppl_cb.setStyleSheet("QCheckBox { margin: 4px; padding: 4px; }")
        main_files_layout.addWidget(self.main_window.dvp_cb)
        main_files_layout.addWidget(self.main_window.ppl_cb)
        main_files_layout.addStretch()
        
        file_selection_layout.addLayout(main_files_layout)
        
        # FN文件区域标题
        fn_title_label = QLabel("FN文件 (接口定义通知单):")
        fn_title_label.setFont(self.default_font)
        fn_title_label.setStyleSheet("font-weight: bold; margin-top: 8px; margin-bottom: 0px; padding: 0px; line-height: 1;")
        file_selection_layout.addWidget(fn_title_label)
        
        # FN文件网格布局
        fn_grid = QGridLayout()
        fn_grid.setSpacing(8)
        fn_grid.setContentsMargins(15, 0, 15, 8)
        fn_grid.setVerticalSpacing(8)
        
        self.main_window.fn_checkboxes = {}
        fn_files = [
            ("FN_IMU", "IMU"), ("FN_VCU", "VCU"), ("FN_IPB", "IPB"), 
            ("FN_ESP_BWA", "ESP+BWA"), ("FN_EPS", "EPS"), ("FN_EPSA", "EPSA"),
            ("FN_EPB", "EPB"), ("FN_DISUS_A", "DiSus-A"), ("FN_DISUS_C", "DiSus-C"), 
            ("FN_DISUS_P", "DiSus-P"), ("FN_DISUS_X", "DiSus-X"), ("FN_DISUS_M", "DiSus-M"),
            ("FN_域控", "域控")
        ]
        
        for i, (fn_key, fn_name) in enumerate(fn_files):
            cb = QCheckBox(fn_name)
            cb.setFont(self.default_font)
            cb.setStyleSheet("QCheckBox { margin: 4px; padding: 4px; }")
            self.main_window.fn_checkboxes[fn_key] = cb
            row = i // 5
            col = i % 5
            fn_grid.addWidget(cb, row, col)
        
        file_selection_layout.addLayout(fn_grid)
        
        # 全选/取消全选按钮
        select_buttons_layout = QHBoxLayout()
        select_buttons_layout.setContentsMargins(15, 8, 15, 8)
        
        self.main_window.select_all_btn = QPushButton("全选")
        self.main_window.select_all_btn.setFont(self.default_font)
        self.main_window.select_all_btn.setStyleSheet("""
            QPushButton { 
                background-color: #16a085;
                color: white;
                font-size: 10pt;
                font-weight: normal;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 26px;
                margin: 4px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        self.main_window.select_all_btn.clicked.connect(self.main_window.select_all_files)
        
        self.main_window.deselect_all_btn = QPushButton("取消全选")
        self.main_window.deselect_all_btn.setFont(self.default_font)
        self.main_window.deselect_all_btn.setStyleSheet("""
            QPushButton { 
                background-color: #e67e22;
                color: white;
                font-size: 10pt;
                font-weight: normal;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 26px;
                margin: 4px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        self.main_window.deselect_all_btn.clicked.connect(self.main_window.deselect_all_files)
        
        select_buttons_layout.addWidget(self.main_window.select_all_btn)
        select_buttons_layout.addWidget(self.main_window.deselect_all_btn)
        select_buttons_layout.addStretch()
        
        file_selection_layout.addLayout(select_buttons_layout)
        layout.addWidget(file_selection_group)
        
        # 执行按钮和查看结果按钮
        execute_layout = QHBoxLayout()
        
        self.main_window.execute_btn = QPushButton("执行文件处理")
        self.main_window.execute_btn.clicked.connect(self.main_window.execute_file_operations)
        self.main_window.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 10pt;
                font-weight: bold;
                padding: 8px 20px;
                border-radius: 6px;
                border: none;
                min-height: 28px;
                max-height: 36px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        self.main_window.open_result_folder_btn = QPushButton("查看处理结果")
        self.main_window.open_result_folder_btn.clicked.connect(self.main_window.open_numbered_filled_folder)
        self.main_window.open_result_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 9pt;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 6px;
                border: none;
                min-height: 28px;
                max-height: 36px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        execute_layout.addStretch()
        execute_layout.addWidget(self.main_window.execute_btn)
        execute_layout.addWidget(self.main_window.open_result_folder_btn)
        execute_layout.addStretch()
        
        layout.addLayout(execute_layout)
        
        # 进度显示
        self.main_window.progress_bar = QProgressBar()
        self.main_window.progress_bar.setFont(self.default_font)
        self.main_window.progress_bar.setVisible(False)
        layout.addWidget(self.main_window.progress_bar)
        
        self.main_window.tab_widget.addTab(file_ops_widget, "文件操作")
    
    def create_upload_approval_tab(self):
        """创建上传审批选项卡"""
        upload_approval_widget = QWidget()
        upload_approval_widget.setFont(self.default_font)
        layout = QVBoxLayout(upload_approval_widget)
        
        # 说明文字和审批文件夹按钮
        info_header_layout = QHBoxLayout()
        info_label = QLabel(" 请将需要审批的文件（源文件和PDF文件）放入审批文件夹中")
        info_font = QFont()
        info_font.setPointSize(11)
        info_label.setFont(info_font)
        info_label.setStyleSheet("QLabel { color: #7f8c8d; font-style: italic; }")
        
        self.main_window.open_final_approval_btn = QPushButton("打开审批文件夹")
        self.main_window.open_final_approval_btn.clicked.connect(self.main_window.open_final_approval_folder)
        self.main_window.open_final_approval_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-size: 10pt;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 28px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        info_header_layout.addWidget(info_label)
        info_header_layout.addStretch()
        info_header_layout.addWidget(self.main_window.open_final_approval_btn)
        
        layout.addLayout(info_header_layout)
        
        # 上传选项
        upload_options_group = QGroupBox("上传选项")
        upload_options_group.setFont(self.default_font)
        upload_options_layout = QHBoxLayout(upload_options_group)
        
        self.main_window.upload_silent_mode_cb = QCheckBox("静默模式（后台运行，不显示浏览器窗口）")
        
        upload_options_layout.addWidget(self.main_window.upload_silent_mode_cb)
        upload_options_layout.addStretch()
        
        layout.addWidget(upload_options_group)
        
        # 上传状态表格
        status_group = QGroupBox("文件上传状态")
        status_group.setFont(self.default_font)
        status_layout = QVBoxLayout(status_group)
        
        self.main_window.upload_status_table = QTableWidget()
        self.main_window.upload_status_table.setFont(self.default_font)
        self.main_window.upload_status_table.setColumnCount(5)
        self.main_window.upload_status_table.setHorizontalHeaderLabels([
            "文件名", "编号", "完整文件名", "已填写", "已上传"
        ])
        self.main_window.upload_status_table.horizontalHeader().setStretchLastSection(True)
        self.main_window.upload_status_table.setAlternatingRowColors(True)
        
        status_layout.addWidget(self.main_window.upload_status_table)
        layout.addWidget(status_group)
        
        # 执行上传按钮
        upload_layout = QHBoxLayout()
        self.main_window.upload_approval_btn = QPushButton("上传审批文件")
        self.main_window.upload_approval_btn.clicked.connect(self.main_window.upload_approval_files)
        self.main_window.upload_approval_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 10pt;
                font-weight: bold;
                padding: 8px 20px;
                border-radius: 6px;
                border: none;
                min-height: 28px;
                max-height: 36px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        upload_layout.addStretch()
        upload_layout.addWidget(self.main_window.upload_approval_btn)
        upload_layout.addStretch()
        
        layout.addLayout(upload_layout)
        
        # 进度显示
        self.main_window.upload_progress = QProgressBar()
        self.main_window.upload_progress.setFont(self.default_font)
        self.main_window.upload_progress.setVisible(False)
        layout.addWidget(self.main_window.upload_progress)
        
        self.main_window.tab_widget.addTab(upload_approval_widget, "上传审批")
    
    def create_log_monitor_tab(self):
        """创建日志监控选项卡"""
        log_monitor_widget = QWidget()
        log_monitor_widget.setFont(self.default_font)
        layout = QVBoxLayout(log_monitor_widget)
        
        # 日志控制按钮
        log_controls_layout = QHBoxLayout()
        
        button_style = """
            QPushButton {
                background-color: #8e44ad;
                color: white;
                font-size: 10pt;
                font-weight: normal;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                min-height: 28px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #7d3c98;
            }
        """
        
        self.main_window.open_main_log_btn = QPushButton("主程序日志")
        self.main_window.open_main_log_btn.setStyleSheet(button_style)
        self.main_window.open_main_log_btn.clicked.connect(self.main_window.open_main_log)
        
        self.main_window.open_subprocess_log_btn = QPushButton("子进程日志")
        self.main_window.open_subprocess_log_btn.setStyleSheet(button_style)
        self.main_window.open_subprocess_log_btn.clicked.connect(self.main_window.open_subprocess_log)
        
        self.main_window.open_update_templates_btn = QPushButton("更新模板文件夹")
        self.main_window.open_update_templates_btn.setStyleSheet(button_style)
        self.main_window.open_update_templates_btn.clicked.connect(self.main_window.open_update_templates_folder)
        
        self.main_window.replace_templates_btn = QPushButton("替换模板")
        self.main_window.replace_templates_btn.setStyleSheet(button_style)
        self.main_window.replace_templates_btn.clicked.connect(self.main_window.replace_templates)
        
        self.main_window.operation_log_btn = QPushButton("操作日志")
        self.main_window.operation_log_btn.setStyleSheet(button_style)
        self.main_window.operation_log_btn.clicked.connect(self.main_window.open_operation_log)
        
        log_controls_layout.addWidget(self.main_window.open_main_log_btn)
        log_controls_layout.addWidget(self.main_window.open_subprocess_log_btn)
        log_controls_layout.addWidget(self.main_window.open_update_templates_btn)
        log_controls_layout.addWidget(self.main_window.replace_templates_btn)
        log_controls_layout.addWidget(self.main_window.operation_log_btn)
        log_controls_layout.addStretch()
        
        layout.addLayout(log_controls_layout)
        
        # 实时日志显示
        self.main_window.log_display = QTextEdit()
        self.main_window.log_display.setFont(self.default_font)
        self.main_window.log_display.setReadOnly(True)
        self.main_window.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        
        layout.addWidget(QLabel("实时日志:"))
        layout.addWidget(self.main_window.log_display)
        
        self.main_window.tab_widget.addTab(log_monitor_widget, "日志监控")
    
    def create_status_bar(self):
        """创建状态栏"""
        self.main_window.statusBar().showMessage("准备就绪")
        self.main_window.time_label = QLabel()
        self.main_window.time_label.setFont(self.default_font)
        self.main_window.statusBar().addPermanentWidget(self.main_window.time_label)
        
        # 定时器更新时间
        self.main_window.timer = QTimer()
        self.main_window.timer.timeout.connect(self.main_window.update_time)
        self.main_window.timer.start(1000)
