import os
from pathlib import Path
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.shared import Pt
from openpyxl import load_workbook
import logging

logger = logging.getLogger(__name__)

def validate_filename(filename, project_code):
    """
    验证文件名是否以 '{project_code}项目' 开头。

    Args:
        filename (str): 文件名（含后缀）。
        project_code (str): 项目代号。

    Returns:
        bool: 如果文件名有效，返回 True；否则返回 False。

    Raises:
        ValueError: 如果文件名不是 .docx 或 .xlsx。
    """
    try:
        if not (filename.endswith('.docx') or filename.endswith('.xlsx')):
            logger.error(f"文件 {filename} 不是 .docx 或 .xlsx 文件")
            raise ValueError("仅支持 .docx 和 .xlsx 文件")
        prefix = f"{project_code}项目"
        if not filename.startswith(prefix):
            logger.warning(f"文件 {filename} 未以 '{prefix}' 开头")
            return False
        logger.info(f"文件 {filename} 命名验证通过")
        return True
    except Exception as e:
        logger.error(f"验证文件名 {filename} 失败，错误信息: {str(e)}")
        raise

def get_document_type(filename):
    """
    根据文件名确定文档类型。

    Args:
        filename (str): 文件名（含后缀）。

    Returns:
        str: 文档类型。

    Raises:
        ValueError: 如果文件名不匹配任何文档类型。
    """
    try:
        if "软件设计验证计划" in filename:
            return "DVP 系统设计验证计划"
        elif "接口定义通知单" in filename:
            return "FN 接口定义/功能输入通知单"
        elif "软件开发匹配测试计划" in filename:
            return "PPL 车辆匹配计划"
        else:
            logger.error(f"文件 {filename} 不匹配任何已知文档类型")
            raise ValueError("未知文档类型")
    except Exception as e:
        logger.error(f"获取文件 {filename} 文档类型失败，错误信息: {str(e)}")
        raise

def update_filename(original_path, file_code, output_folder):
    """
    使用文件编码更新文件名。

    Args:
        original_path (Path): 原始文件路径。
        file_code (str): 文件编码。
        output_folder (str): 输出文件夹路径。

    Returns:
        Path: 新文件路径。

    Raises:
        OSError: 如果文件重命名失败。
    """
    try:
        filename = original_path.name
        new_filename = f"{file_code}-{filename}"
        new_path = Path(output_folder) / new_filename
        os.makedirs(output_folder, exist_ok=True)
        # 复制文件到输出文件夹并重命名
        with open(original_path, 'rb') as src, open(new_path, 'wb') as dst:
            dst.write(src.read())
        logger.info(f"文件已重命名为: {new_path}")
        return new_path
    except OSError as e:
        logger.error(f"重命名文件 {original_path} 失败，错误信息: {str(e)}")
        raise

def update_docx_document(file_path, file_code):
    """
    更新 .docx 文件（接口定义通知单）的编号。

    Args:
        file_path (Path): Word 文件路径。
        file_code (str): 文件编码。

    Raises:
        Exception: 如果更新失败。
    """
    try:
        logger.info(f"开始更新 Word 文件 {file_path} 的编号为 {file_code}")
        doc = Document(file_path)

        # 更新首页编号
        for para in doc.paragraphs:
            text = para.text
            if '编号' in text and (':' in text or '：' in text):
                # 找到冒号位置
                colon = ':' if ':' in text else '：'
                prefix = text.split(colon)[0] + colon
                # 清空段落并重新添加内容
                para.clear()
                run = para.add_run(f"{prefix}{file_code}")
                run.font.name = 'Arial'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), 'Arial')  # 确保中西文一致
                run.font.size = Pt(12)
                run.bold = True  # 加粗
                logger.info(f"已更新首页编号为: {file_code}")
                break

        # 更新页眉表格中的编号（第二页及之后）
        for section in doc.sections:
            header = section.header
            for table in header.tables:
                for row in table.rows:
                    for i, cell in enumerate(row.cells):
                        if '文件编号' in cell.text:
                            # 右侧单元格（i+1）
                            if i + 1 < len(row.cells):
                                right_cell = row.cells[i + 1]
                                right_cell.text = file_code
                                for para in right_cell.paragraphs:
                                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                logger.info(f"已更新页眉表格编号为: {file_code}")
                                break

        # 保存更新后的文件
        doc.save(file_path)
        logger.info(f"Word 文件 {file_path} 已更新")
    except Exception as e:
        logger.error(f"更新 Word 文件 {file_path} 失败，错误信息: {str(e)}")
        raise

def update_dvp_xlsx_document(file_path, file_code):
    """
    更新 .xlsx 文件（DVP）的编号。

    Args:
        file_path (Path): Excel 文件路径。
        file_code (str): 文件编码。

    Raises:
        Exception: 如果更新失败。
    """
    try:
        logger.info(f"开始更新 Excel 文件 {file_path} 的编号为 {file_code}")
        wb = load_workbook(file_path)

        # 查找包含“软件设计验证计划”的 sheet
        target_sheet = None
        for sheet in wb:
            if "软件设计验证计划" in sheet.title:
                target_sheet = sheet
                break

        if not target_sheet:
            logger.error(f"文件 {file_path} 中未找到包含‘软件设计验证计划’的 sheet")
            raise ValueError("未找到目标 sheet")

        # 遍历单元格，查找包含“DVP编号：”或“DVP编号:”的单元格
        updated = False
        for row in target_sheet.iter_rows():
            for cell in row:
                cell_value = str(cell.value).strip() if cell.value else ""
                if "DVP编号：" in cell_value or "DVP编号:" in cell_value:
                    # 检查是否为合并单元格
                    for merged_range in target_sheet.merged_cells.ranges:
                        if cell.coordinate in merged_range:
                            # 使用合并单元格的起始单元格
                            start_cell = target_sheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                            col_idx = start_cell.column
                            row_idx = start_cell.row
                            break
                    else:
                        col_idx = cell.column
                        row_idx = cell.row

                    # 右侧单元格
                    right_cell = target_sheet.cell(row=row_idx, column=col_idx + 1)
                    right_cell.value = file_code
                    logger.info(f"已更新 DVP sheet 的编号为: {file_code}")
                    updated = True
                    break
            if updated:
                break

        if not updated:
            logger.warning(f"文件 {file_path} 中未找到包含‘DVP编号’的单元格")
            raise ValueError("未找到 DVP 编号单元格")

        # 保存更新后的文件
        wb.save(file_path)
        logger.info(f"Excel 文件 {file_path} 已更新")
    except Exception as e:
        logger.error(f"更新 Excel 文件 {file_path} 失败，错误信息: {str(e)}")
        raise

def update_ppl_xlsx_document(file_path, file_code):
    """
    更新 .xlsx 文件（PPL）的编号。

    Args:
        file_path (Path): Excel 文件路径。
        file_code (str): 文件编码。

    Raises:
        Exception: 如果更新失败。
    """
    try:
        logger.info(f"开始更新 Excel 文件 {file_path} 的编号为 {file_code}")
        wb = load_workbook(file_path)

        # 遍历所有 sheet
        updated_sheets = []
        for sheet in wb:
            updated = False
            # 查找“编号”单元格
            for row in sheet.iter_rows():
                for cell in row:
                    cell_value = str(cell.value).strip() if cell.value else ""
                    if cell_value == "编号":
                        # 检查“编号”单元格是否为合并单元格
                        right_col_idx = None
                        for merged_range in sheet.merged_cells.ranges:
                            if cell.coordinate in merged_range:
                                # 使用合并单元格的右边界列
                                right_col_idx = merged_range.max_col + 1
                                row_idx = merged_range.min_row
                                break
                        else:
                            right_col_idx = cell.column + 1
                            row_idx = cell.row

                        # 找到右侧单元格（可能是合并单元格）
                        right_cell = sheet.cell(row=row_idx, column=right_col_idx)
                        # 检查右侧单元格是否为合并单元格
                        for merged_range in sheet.merged_cells.ranges:
                            if right_cell.coordinate in merged_range:
                                # 使用合并单元格的起始单元格
                                right_cell = sheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                                break

                        # 写入编号
                        right_cell.value = file_code
                        logger.info(f"已更新 PPL sheet {sheet.title} 的编号为: {file_code}")
                        updated_sheets.append(sheet.title)
                        updated = True
                        break
                if updated:
                    break

            if not updated:
                logger.warning(f"Sheet {sheet.title} 中未找到‘编号’单元格")

        if not updated_sheets:
            logger.warning(f"文件 {file_path} 中未找到任何‘编号’单元格")
            raise ValueError("未找到 PPL 编号单元格")

        # 保存更新后的文件
        wb.save(file_path)
        logger.info(f"Excel 文件 {file_path} 已更新，更新了 sheet: {', '.join(updated_sheets)}")
    except Exception as e:
        logger.error(f"更新 Excel 文件 {file_path} 失败，错误信息: {str(e)}")
        raise

def update_document(file_path, file_code):
    """
    根据文件类型更新文档内容。

    Args:
        file_path (Path): 文件路径（.docx 或 .xlsx）。
        file_code (str): 文件编码。

    Raises:
        ValueError: 如果文件类型不受支持。
        Exception: 如果更新失败。
    """
    try:
        if file_path.suffix == '.docx':
            update_docx_document(file_path, file_code)
        elif file_path.suffix == '.xlsx':
            if "软件设计验证计划" in file_path.name:
                update_dvp_xlsx_document(file_path, file_code)
            elif "软件开发匹配测试计划" in file_path.name:
                update_ppl_xlsx_document(file_path, file_code)
            else:
                logger.error(f"文件 {file_path} 不匹配 DVP 或 PPL 类型")
                raise ValueError("未知 Excel 文件类型")
        else:
            logger.error(f"不支持的文件类型: {file_path.suffix}")
            raise ValueError("仅支持 .docx 和 .xlsx 文件")
    except Exception as e:
        logger.error(f"更新文件 {file_path} 失败，错误信息: {str(e)}")
        raise