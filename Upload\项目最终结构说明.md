# 上传审批自动化项目 - 最终结构说明

## 项目概述
本项目是一个完整的上传审批自动化系统，能够自动处理文档上传、审批流程填写等任务。

## 核心程序文件

### 主要程序模块
- **`main_controller.py`** - 主控制器，协调整个自动化流程
- **`browser_manager.py`** - 浏览器管理器，处理浏览器启动、窗口切换等
- **`first_page_handler.py`** - 第一页处理器，负责文件上传和基本信息填写
- **`second_page_handler.py`** - 第二页处理器，负责评审人选择、截止时间填写、提交等
- **`utils.py`** - 工具函数集合，提供通用功能
- **`config.py`** - 配置文件，包含系统参数和设置

### 启动和运行文件
- **`run.py`** - 程序入口点，启动自动化流程
- **`start.bat`** - 一键启动脚本（Windows批处理）
- **`requirements.txt`** - Python依赖包列表

### 数据和日志
- **`Data/`** - Excel数据目录
  - `Fill_Template_Data.xlsx` - 角色映射和填写模板数据
- **`Final_Approval_Documents/`** - 最终审批文档存储目录
- **`upload_approval.log`** - 运行日志文件

### 文档说明
- **`README.md`** - 项目主要说明文档
- **`项目说明文档.md`** - 详细的项目功能和使用说明
- **`项目文件整理说明.md`** - 文件整理和架构说明
- **`项目最终结构说明.md`** - 最终项目结构说明

## 归档文件夹

### `backup/` - 备份文件夹
包含以下子目录：
- **`analysis/`** - 分析和辅助脚本
- **`docs/`** - 旧版本文档
- **`old_versions/`** - 程序的旧版本
- **`reference/`** - 参考文件
- **`tests/`** - 测试脚本和测试日志

### `archive/` - 归档文件夹
包含以下子目录：
- **`config_files/`** - JSON配置文件
  - 上传审批dvp.json, fn.json, ppl.json
- **`docs/`** - 旧版本说明文档
  - 各种README和使用指南
- **`html_files/`** - HTML参考文件
  - 各种页面的HTML结构参考文件

### `log_tools/` - 日志管理工具集
包含可选的日志管理工具：
- **`log_manager.py`** - 高级日志管理器
- **`simple_log_cleanup.py`** - 简单日志清理工具
- **`cleanup_logs.bat`** - 批处理清理脚本
- **`start_with_log_cleanup.bat`** - 集成清理的启动脚本
- **`日志管理说明.md`** - 详细使用说明
- **`README.md`** - 工具集说明

*注意: 项目已在utils.py中集成自动日志轮转，正常使用无需这些额外工具*

## 使用方法

### 快速启动
1. 双击 `start.bat` 启动程序
2. 或者在命令行中运行 `python run.py`

### 环境要求
- Python 3.7+
- 安装依赖：`pip install -r requirements.txt`
- Chrome浏览器

### 配置说明
- 修改 `config.py` 中的URL和参数设置
- 更新 `Data/Fill_Template_Data.xlsx` 中的角色映射数据
- 设置静默模式：`HEADLESS_MODE = True` 可实现后台运行

## 主要功能

1. **自动文件上传** - 支持批量文件上传
2. **智能表单填写** - 根据文件名自动识别并填写相应信息
3. **评审人自动选择** - 根据配置自动选择合适的评审人
4. **截止时间设置** - 自动计算并设置审批截止时间
5. **批量处理** - 支持多文件连续自动化处理
6. **错误处理** - 完善的异常处理和日志记录
7. **自动日志管理** - 集成在utils.py中，自动轮转大日志文件
8. **静默模式** - 支持后台运行，不显示浏览器窗口

## 日志管理

项目已在 `utils.py` 中集成自动日志轮转功能：
- **自动触发**: 日志文件超过5MB时自动轮转
- **安全备份**: 轮转前创建带时间戳的备份文件
- **智能清理**: 自动删除过期备份，保留最近3个备份
- **无需干预**: 程序启动时自动检查，无需手动操作

如需特殊的日志管理需求，可使用 `log_tools/` 文件夹中的工具。

## 维护说明

- 查看 `upload_approval.log` 了解运行状态
- 根据需要更新 `Data/Fill_Template_Data.xlsx` 中的数据映射
- 如需功能扩展，主要修改对应的handler文件
- 备份重要配置文件后再进行修改

## 版本信息
- 当前版本：最终整理版
- 最后更新：2025年7月1日
- 状态：生产就绪

---
*此文档记录了项目的最终整理结果，所有核心功能已完成并测试通过。*
