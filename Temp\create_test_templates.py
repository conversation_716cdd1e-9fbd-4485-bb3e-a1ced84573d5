#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
根据用户提供的文件列表创建测试模板文件
"""

import os
from pathlib import Path

def create_test_templates():
    """创建测试用的模板文件"""
    base_path = Path(__file__).parent
    templates_path = base_path / "Templates"
    
    # 用户提供的文件列表
    template_files = [
        "XX项目VSE系统 to DiSus-A系统接口定义通知单.docx",
        "XX项目VSE系统 to DiSus-C系统接口定义通知单 .docx",  # 注意有空格
        "XX项目VSE系统 to DiSus-M系统接口定义通知单.docx",
        "XX项目VSE系统 to DiSus-P系统接口定义通知单.docx",
        "XX项目VSE系统 to DiSus-X系统接口定义通知单.docx",
        "XX项目VSE系统 to EPB系统接口定义通知单.docx",
        "XX项目VSE系统 to EPSA系统接口定义通知单.docx",
        "XX项目VSE系统 to EPS系统接口定义通知单.docx",
        "XX项目VSE系统 to ESP+BWA系统接口定义通知单.docx",
        "XX项目VSE系统 to IPB系统接口定义通知单.docx",
        "XX项目VSE系统 to VCU系统接口定义通知单.docx",
        "XX项目VSE系统 to 安全气囊节点接口定义通知单.docx",
        "XX项目VSE系统 to 跨域计算平台接口定义通知单.docx",
        "XX项目VSE软件开发匹配测试计划.xlsx",
        "XX项目VSE软件设计验证计划（DVP）.xlsx"
    ]
    
    print(f"在 {templates_path} 中创建测试模板文件...")
    
    for file_name in template_files:
        file_path = templates_path / file_name
        if not file_path.exists():
            # 创建空文件作为测试
            file_path.touch()
            print(f"创建: {file_name}")
        else:
            print(f"已存在: {file_name}")
    
    print("测试模板文件创建完成!")

if __name__ == "__main__":
    create_test_templates()
