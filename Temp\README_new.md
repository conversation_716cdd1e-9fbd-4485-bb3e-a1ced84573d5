# 车型文件管理系统

## 快速开始

### 1. 安装依赖
```bash
cd Scripts
install.bat
```

### 2. 启动程序
```bash
cd Scripts  
run_gui.bat
```

### 3. 或直接运行
```bash
python main_gui_final.py
```

## 项目结构

- `main_gui_final.py` - 主程序入口
- `Core/` - 核心功能模块
- `Scripts/` - 启动和安装脚本
- `Config/` - 配置文件
- `Templates/` - 模板文件
- `Vehicles/` - 车型数据
- `Docs/` - 详细文档
- `Temp/` - 临时和测试文件

## 详细说明

请查看 `车型文件管理系统功能说明.md` 获取完整的功能描述和使用指南。

## 主要功能

1. **车型管理** - 车型文件夹创建和配置
2. **模板文件复制** - 智能模板文件查找和复制
3. **申请编号** - 自动化申请文件编号
4. **文件填写** - 自动填写文件内容
5. **上传审批** - 自动化上传和审批流程
6. **状态管理** - 完整的文件状态跟踪

## 依赖要求

- Python 3.7+
- PyQt5
- Selenium
- Pandas
- openpyxl

## 技术栈

- **界面框架**: PyQt5
- **网页自动化**: Selenium WebDriver
- **数据处理**: Pandas
- **文件操作**: openpyxl
- **配置管理**: JSON
