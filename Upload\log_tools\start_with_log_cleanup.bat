@echo off
echo ====================================
echo    Upload Approval Automation
echo ====================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM 清理日志文件（如果需要）
echo Checking log file size...
python simple_log_cleanup.py status

REM 询问是否清理日志
set /p cleanup="Do you want to clean log file if it's too large? (y/n): "
if /i "%cleanup%"=="y" (
    python simple_log_cleanup.py clean
)

echo.
echo Starting upload approval automation...
echo Press Ctrl+C to stop the program
echo.

REM 启动主程序
python run.py

echo.
echo Program finished.
pause
