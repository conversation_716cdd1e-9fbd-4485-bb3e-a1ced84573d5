import sys
import os
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QMessageBox
)
from PyQt5.QtCore import Qt

# 导入GUI_Core模块
from GUI_Core import (
    FileManager, UIComponents, FileOperations, 
    ProcessManager, UtilityFunctions
)


class VehicleManagementGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("车型文件管理系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置路径
        self.base_path = Path(__file__).parent
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.fillin_path = self.base_path / "Fillin"
        self.update_templates_path = self.base_path / "Update_Templates"
        self.logs_path = self.base_path / "logs"
        
        # 创建必要的目录
        self.vehicles_path.mkdir(exist_ok=True)
        self.logs_path.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 初始化UI
        self.init_ui()
        
        # 设置样式
        self.setup_style()
        
        # 当前选择的车型代号
        self.current_vehicle_code = ""
        
        # 初始化车型列表
        self.load_vehicle_codes()

    def setup_logging(self):
        """设置日志"""
        log_file = self.logs_path / f"vehicle_management_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题栏（包含OA登录设置）
        self.create_title_bar(main_layout)
        
        # 创建主要工作区域
        self.create_main_workspace(main_layout)
        
        # 创建状态栏
        self.create_status_bar()

    def create_title_bar(self, parent_layout):
        """创建标题栏（包含OA登录设置）"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setFixedHeight(100)
        
        title_layout = QVBoxLayout(title_frame)
        
        # 第一行：标题和OA登录设置
        title_row = QHBoxLayout()
        title_label = QLabel("车型文件管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        title_row.addWidget(title_label)
        title_row.addStretch()
        
        # OA登录设置区域（放在最右位置）
        oa_label = QLabel("OA登录设置:")
        oa_label.setStyleSheet("font-weight: normal; color: #000000;")  # 与车型代号、快捷操作字体大小一致（使用默认字体大小）
        title_row.addWidget(oa_label)
        
        # 用户名输入
        username_label = QLabel("用户名:")
        username_label.setStyleSheet("")  # 与车型代号、快捷操作字体大小一致（使用默认字体大小）
        title_row.addWidget(username_label)
        self.username_edit = QLineEdit()
        self.username_edit.setMaximumWidth(160)  # 增加1/3长度（120 * 1.33 ≈ 160）
        self.username_edit.setPlaceholderText("请输入用户名")
        title_row.addWidget(self.username_edit)
        
        # 密码输入
        password_label = QLabel("密码:")
        password_label.setStyleSheet("")  # 与车型代号、快捷操作字体大小一致（使用默认字体大小）
        title_row.addWidget(password_label)
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMaximumWidth(160)  # 增加1/3长度（120 * 1.33 ≈ 160）
        self.password_edit.setPlaceholderText("请输入密码")
        title_row.addWidget(self.password_edit)
        
        # 第二行：车型设置和快捷操作
        controls_row = QHBoxLayout()
        
        # 车型选择区域
        vehicle_layout = QHBoxLayout()
        vehicle_layout.addWidget(QLabel("车型代号:"))
        self.vehicle_combo = QComboBox()
        self.vehicle_combo.setEditable(True)
        self.vehicle_combo.setMinimumWidth(100)
        self.vehicle_combo.currentTextChanged.connect(self.on_vehicle_changed)
        vehicle_layout.addWidget(self.vehicle_combo)
        
        self.setup_vehicle_btn = QPushButton("设置车型")
        self.setup_vehicle_btn.clicked.connect(self.setup_vehicle_info)
        vehicle_layout.addWidget(self.setup_vehicle_btn)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        # 快捷按钮区域（只保留车型文件夹和模板文件夹）
        self.open_vehicles_btn = QPushButton("车型文件夹")
        self.open_vehicles_btn.clicked.connect(self.open_vehicles_folder)
        
        self.open_templates_btn = QPushButton("模板文件夹")
        self.open_templates_btn.clicked.connect(self.open_templates_folder)
        
        controls_row.addLayout(vehicle_layout)
        controls_row.addWidget(separator)
        controls_row.addWidget(QLabel("快捷操作:"))
        controls_row.addWidget(self.open_vehicles_btn)
        controls_row.addWidget(self.open_templates_btn)
        controls_row.addStretch()
        
        title_layout.addLayout(title_row)
        title_layout.addLayout(controls_row)
        
        parent_layout.addWidget(title_frame)

    def create_main_workspace(self, parent_layout):
        """创建主工作区域"""
        # 创建选项卡widget
        self.tab_widget = QTabWidget()
        
        # 文件操作选项卡 (合并了申请编号功能)
        self.create_file_operations_tab()
        
        # 上传审批选项卡
        self.create_upload_approval_tab()
        
        # 日志监控选项卡
        self.create_log_monitor_tab()
        
        parent_layout.addWidget(self.tab_widget)

    def create_file_operations_tab(self):
        """创建文件操作选项卡"""
        file_ops_widget = QWidget()
        layout = QVBoxLayout(file_ops_widget)
        
        # 说明信息
        info_label = QLabel("选择需要处理的文件类型，系统将自动执行：申请编号 → 填写内容 → 生成完整文件")
        info_label.setStyleSheet("QLabel { color: #7f8c8d; font-style: italic; padding: 5px 10px; margin: 0px; }")
        info_label.setMaximumHeight(25)  # 限制最大高度
        layout.addWidget(info_label)
        
        # 运行模式选择（适当增加高度使静默模式行更舒适）
        mode_group = QGroupBox("运行模式")
        mode_group.setMaximumHeight(70)  # 适当增加高度，使静默模式行更舒适
        mode_layout = QHBoxLayout(mode_group)
        mode_layout.setContentsMargins(10, 8, 10, 8)  # 适当增加内边距
        
        self.silent_mode_cb = QCheckBox("静默模式（后台运行，不显示浏览器窗口）")
        mode_layout.addWidget(self.silent_mode_cb)
        mode_layout.addStretch()
        
        layout.addWidget(mode_group)
        
        # 文件选择区域 - 优化布局，增加空间利用
        file_selection_group = QGroupBox("文件选择")
        file_selection_layout = QVBoxLayout(file_selection_group)
        file_selection_layout.setSpacing(1)  # 标题和FN勾选框之间间距极小
        
        # 第一行：DVP和PPL文件，增加高度使布局更均衡，往下移一点
        main_files_layout = QHBoxLayout()
        main_files_layout.setSpacing(20)  # 组件间适当间距
        main_files_layout.setContentsMargins(10, 12, 10, 12)  # 增加上下内边距，使整个区域往下移
        
        self.dvp_cb = QCheckBox("DVP-系统设计验证计划")
        self.dvp_cb.setStyleSheet("QCheckBox { margin: 4px; padding: 4px; }")  # 增加边距和内边距
        self.ppl_cb = QCheckBox("PPL-开发匹配测试计划")
        self.ppl_cb.setStyleSheet("QCheckBox { margin: 4px; padding: 4px; }")  # 增加边距和内边距
        main_files_layout.addWidget(self.dvp_cb)
        main_files_layout.addWidget(self.ppl_cb)
        main_files_layout.addStretch()
        
        file_selection_layout.addLayout(main_files_layout)
        
        # FN文件区域标题，极致贴合下方勾选框
        fn_title_label = QLabel("FN文件 (接口定义通知单):")
        fn_title_label.setStyleSheet("font-weight: bold; margin-top: 8px; margin-bottom: 0px; padding: 0px; line-height: 1;")  # 下边距0，padding 0，行高最小
        file_selection_layout.addWidget(fn_title_label)
        
        # FN文件使用更优化的网格布局，每行5个，增加行间距
        fn_grid = QGridLayout()
        fn_grid.setSpacing(8)  # 增加水平间距
        fn_grid.setContentsMargins(15, 0, 15, 8)  # 顶部边距设为0，使勾选框更紧贴标题行
        fn_grid.setVerticalSpacing(8)  # 增加垂直间距，使行间距更大
        
        self.fn_checkboxes = {}
        fn_files = [
            ("FN_IMU", "IMU"), ("FN_VCU", "VCU"), ("FN_IPB", "IPB"), 
            ("FN_ESP_BWA", "ESP+BWA"), ("FN_EPS", "EPS"), ("FN_EPSA", "EPSA"),
            ("FN_EPB", "EPB"), ("FN_DISUS_A", "DiSus-A"), ("FN_DISUS_C", "DiSus-C"), 
            ("FN_DISUS_P", "DiSus-P"), ("FN_DISUS_X", "DiSus-X"), ("FN_DISUS_M", "DiSus-M"),
            ("FN_域控", "域控")
        ]
        
        for i, (fn_key, fn_name) in enumerate(fn_files):
            cb = QCheckBox(fn_name)
            cb.setStyleSheet("QCheckBox { margin: 4px; padding: 4px; }")  # 增加复选框的外边距和内边距
            self.fn_checkboxes[fn_key] = cb
            row = i // 5  # 每行5个，减少行数
            col = i % 5
            fn_grid.addWidget(cb, row, col)
        
        file_selection_layout.addLayout(fn_grid)
        
        # 全选/取消全选按钮，增加高度
        select_buttons_layout = QHBoxLayout()
        select_buttons_layout.setContentsMargins(15, 8, 15, 8)  # 增加边距，增加这部分高度
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.setStyleSheet("QPushButton { margin: 4px; padding: 6px 12px; }")  # 增加按钮的内边距和外边距
        self.select_all_btn.clicked.connect(self.select_all_files)
        self.deselect_all_btn = QPushButton("取消全选")
        self.deselect_all_btn.setStyleSheet("QPushButton { margin: 4px; padding: 6px 12px; }")  # 增加按钮的内边距和外边距
        self.deselect_all_btn.clicked.connect(self.deselect_all_files)
        
        select_buttons_layout.addWidget(self.select_all_btn)
        select_buttons_layout.addWidget(self.deselect_all_btn)
        select_buttons_layout.addStretch()
        
        file_selection_layout.addLayout(select_buttons_layout)
        layout.addWidget(file_selection_group)
        
        # 执行按钮和查看结果按钮
        execute_layout = QHBoxLayout()
        
        self.execute_btn = QPushButton("执行文件处理")
        self.execute_btn.clicked.connect(self.execute_file_operations)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        self.open_result_folder_btn = QPushButton("查看处理结果")
        self.open_result_folder_btn.clicked.connect(self.open_numbered_filled_folder)
        self.open_result_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        execute_layout.addStretch()
        execute_layout.addWidget(self.execute_btn)
        execute_layout.addWidget(self.open_result_folder_btn)
        execute_layout.addStretch()
        
        layout.addLayout(execute_layout)
        
        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.tab_widget.addTab(file_ops_widget, "文件操作")

    def create_upload_approval_tab(self):
        """创建上传审批选项卡"""
        upload_approval_widget = QWidget()
        layout = QVBoxLayout(upload_approval_widget)
        
        # 说明文字和审批文件夹按钮
        info_header_layout = QHBoxLayout()
        info_label = QLabel("请将需要审批的文件（源文件和PDF文件）放入审批文件夹中")
        info_label.setStyleSheet("QLabel { color: #7f8c8d; font-style: italic; }")
        
        self.open_final_approval_btn = QPushButton("打开审批文件夹")
        self.open_final_approval_btn.clicked.connect(self.open_final_approval_folder)
        self.open_final_approval_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        info_header_layout.addWidget(info_label)
        info_header_layout.addStretch()
        info_header_layout.addWidget(self.open_final_approval_btn)
        
        layout.addLayout(info_header_layout)
        
        # 上传选项（只保留静默模式）
        upload_options_group = QGroupBox("上传选项")
        upload_options_layout = QHBoxLayout(upload_options_group)
        
        self.upload_silent_mode_cb = QCheckBox("静默模式（后台运行，不显示浏览器窗口）")
        
        upload_options_layout.addWidget(self.upload_silent_mode_cb)
        upload_options_layout.addStretch()
        
        layout.addWidget(upload_options_group)
        
        # 上传状态表格
        status_group = QGroupBox("文件上传状态")
        status_layout = QVBoxLayout(status_group)
        
        self.upload_status_table = QTableWidget()
        self.upload_status_table.setColumnCount(5)
        self.upload_status_table.setHorizontalHeaderLabels([
            "文件名", "编号", "完整文件名", "已填写", "已上传"
        ])
        self.upload_status_table.horizontalHeader().setStretchLastSection(True)
        self.upload_status_table.setAlternatingRowColors(True)
        
        status_layout.addWidget(self.upload_status_table)
        layout.addWidget(status_group)
        
        # 执行上传按钮
        upload_layout = QHBoxLayout()
        self.upload_approval_btn = QPushButton("上传审批文件")
        self.upload_approval_btn.clicked.connect(self.upload_approval_files)
        self.upload_approval_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        upload_layout.addStretch()
        upload_layout.addWidget(self.upload_approval_btn)
        upload_layout.addStretch()
        
        layout.addLayout(upload_layout)
        
        # 进度显示
        self.upload_progress = QProgressBar()
        self.upload_progress.setVisible(False)
        layout.addWidget(self.upload_progress)
        
        self.tab_widget.addTab(upload_approval_widget, "上传审批")

    def create_log_monitor_tab(self):
        """创建日志监控选项卡"""
        log_monitor_widget = QWidget()
        layout = QVBoxLayout(log_monitor_widget)
        
        # 日志控制按钮
        log_controls_layout = QHBoxLayout()
        
        self.open_main_log_btn = QPushButton("主程序日志")
        self.open_main_log_btn.clicked.connect(self.open_main_log)
        
        self.open_subprocess_log_btn = QPushButton("子进程日志")
        self.open_subprocess_log_btn.clicked.connect(self.open_subprocess_log)
        
        self.open_update_templates_btn = QPushButton("更新模板文件夹")
        self.open_update_templates_btn.clicked.connect(self.open_update_templates_folder)
        
        self.replace_templates_btn = QPushButton("替换模板")
        self.replace_templates_btn.clicked.connect(self.replace_templates)
        
        self.operation_log_btn = QPushButton("操作日志")
        self.operation_log_btn.clicked.connect(self.open_operation_log)
        
        log_controls_layout.addWidget(self.open_main_log_btn)
        log_controls_layout.addWidget(self.open_subprocess_log_btn)
        log_controls_layout.addWidget(self.open_update_templates_btn)
        log_controls_layout.addWidget(self.replace_templates_btn)
        log_controls_layout.addWidget(self.operation_log_btn)
        log_controls_layout.addStretch()
        
        layout.addLayout(log_controls_layout)
        
        # 实时日志显示
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        
        layout.addWidget(QLabel("实时日志:"))
        layout.addWidget(self.log_display)
        
        self.tab_widget.addTab(log_monitor_widget, "日志监控")

    def create_status_bar(self):
        """创建状态栏"""
        self.statusBar().showMessage("准备就绪")
        
        # 添加当前时间显示
        self.time_label = QLabel()
        self.statusBar().addPermanentWidget(self.time_label)
        
        # 定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def setup_style(self):
        """设置应用程序样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
            QPushButton:pressed {
                background-color: #1a252f;
            }
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            }
            QLineEdit {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
            }
            QCheckBox {
                spacing: 5px;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #95a5a6;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #34495e;
            }
            QTabBar::tab:hover {
                background-color: #7f8c8d;
            }
        """)

    def load_vehicle_codes(self):
        """加载车型代号列表"""
        # 从Vehicles目录读取已有的车型代号
        if self.vehicles_path.exists():
            vehicle_codes = [d.name for d in self.vehicles_path.iterdir() if d.is_dir()]
            self.vehicle_combo.addItems(vehicle_codes)
        
        # 添加一些常用的车型代号
        common_codes = ["HAHB", "QYHA", "GXHD", "SYHC"]
        for code in common_codes:
            if self.vehicle_combo.findText(code) == -1:
                self.vehicle_combo.addItem(code)

    def on_vehicle_changed(self, vehicle_code):
        """车型代号改变时的处理"""
        self.current_vehicle_code = vehicle_code.strip().upper()
        if self.current_vehicle_code:
            self.statusBar().showMessage(f"当前车型: {self.current_vehicle_code}")
            self.load_vehicle_file_status()

    def setup_vehicle_info(self):
        """设置车型信息"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择或输入车型代号!")
            return
        
        try:
            from Core.file_manager import FileManager
            file_manager = FileManager(self.base_path)
            
            created, target_file = file_manager.setup_vehicle_folder(self.current_vehicle_code)
            
            if created:
                QMessageBox.information(self, "成功", f"车型 {self.current_vehicle_code} 初始化完成!\n\n请在打开的Excel文件中填写相关人员信息。")
            else:
                QMessageBox.information(self, "提示", f"车型 {self.current_vehicle_code} 已存在。\n\n将打开现有配置文件供编辑。")
            
            # 打开Excel文件供用户编辑
            os.startfile(str(target_file))
            
            # 同时打开数据管理员名单图片
            self.open_admin_list_image()
            
            # 复制information文件夹中的xlsx文件到项目根目录
            self.copy_template_data_to_root()
            
        except Exception as e:
            self.logger.error(f"设置车型信息失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"设置车型信息失败: {str(e)}")

    def load_vehicle_file_status(self):
        """加载车型文件状态"""
        try:
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            info_dir = vehicle_dir / "information"
            excel_file = info_dir / f"{self.current_vehicle_code}_Fill_Template_Data.xlsx"
            
            if excel_file.exists():
                df = pd.read_excel(excel_file, sheet_name="File_Status")
                
                # 更新上传状态表格
                self.upload_status_table.setRowCount(len(df))
                for i, row in df.iterrows():
                    self.upload_status_table.setItem(i, 0, QTableWidgetItem(str(row.get('file_name', ''))))
                    self.upload_status_table.setItem(i, 1, QTableWidgetItem(str(row.get('code', ''))))
                    self.upload_status_table.setItem(i, 2, QTableWidgetItem(str(row.get('numbered_file', ''))))
                    self.upload_status_table.setItem(i, 3, QTableWidgetItem(str(row.get('is_fillin', ''))))
                    self.upload_status_table.setItem(i, 4, QTableWidgetItem(str(row.get('is_upload', ''))))
                
        except Exception as e:
            self.logger.error(f"加载车型文件状态失败: {str(e)}")

    def select_all_files(self):
        """全选所有文件"""
        self.dvp_cb.setChecked(True)
        self.ppl_cb.setChecked(True)
        for cb in self.fn_checkboxes.values():
            cb.setChecked(True)

    def deselect_all_files(self):
        """取消全选"""
        self.dvp_cb.setChecked(False)
        self.ppl_cb.setChecked(False)
        for cb in self.fn_checkboxes.values():
            cb.setChecked(False)

    def execute_file_operations(self):
        """执行文件操作：申请编号 → 填写内容 → 生成完整文件"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        # 检查登录信息
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        # 收集选中的文件
        selected_files = []
        if self.dvp_cb.isChecked():
            selected_files.append("DVP")
        if self.ppl_cb.isChecked():
            selected_files.append("PPL")
        for fn_type, cb in self.fn_checkboxes.items():
            if cb.isChecked():
                selected_files.append(fn_type)
        
        if not selected_files:
            QMessageBox.warning(self, "警告", "请选择至少一个文件!")
            return
        
        try:
            # 禁用执行按钮
            self.execute_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 启动完整的文件处理流程
            self.start_complete_file_processing(selected_files, username, password)
            
        except Exception as e:
            self.logger.error(f"执行文件操作失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行文件操作失败: {str(e)}")
            self.execute_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            self.load_vehicle_file_status()

    def start_complete_file_processing(self, selected_files, username, password):
        """启动完整的文件处理流程"""
        try:
            # 第一步：准备工作环境和文件
            self.progress_bar.setValue(10)
            self.statusBar().showMessage("正在准备工作环境...")
            
            # 清空Apply文件夹
            apply_input_path = self.base_path / "Apply" / "input_files"
            apply_output_path = self.base_path / "Apply" / "output_files"
            self.clear_directory(apply_input_path)
            self.clear_directory(apply_output_path)
            
            # 清空Fillin文件夹
            fillin_paths = [
                self.base_path / "Fillin" / "dvp" / "input",
                self.base_path / "Fillin" / "dvp" / "output",
                self.base_path / "Fillin" / "fn" / "inputs",
                self.base_path / "Fillin" / "fn" / "outputs",
                self.base_path / "Fillin" / "ppl" / "inputs",
                self.base_path / "Fillin" / "ppl" / "outputs"
            ]
            for path in fillin_paths:
                self.clear_directory(path)
            
            self.progress_bar.setValue(20)
            self.statusBar().showMessage("正在复制模板文件...")
            
            # 复制模板文件到Apply输入文件夹，并根据车型代号重命名
            self.copy_templates_to_apply_input(selected_files)
            
            self.progress_bar.setValue(30)
            self.statusBar().showMessage("正在配置Apply模块...")
            
            # 配置Apply/config.py
            self.configure_apply_config(username, password)
            
            self.progress_bar.setValue(40)
            self.statusBar().showMessage("正在申请文件编号...")
            
            # 运行Apply\run_apply_id.py
            self.run_apply_id_process()
            
        except Exception as e:
            self.logger.error(f"文件处理流程失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"文件处理流程失败: {str(e)}")
            self.execute_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

    def clear_directory(self, directory_path):
        """清空指定目录"""
        try:
            directory_path.mkdir(parents=True, exist_ok=True)
            for item in directory_path.iterdir():
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)
        except Exception as e:
            self.logger.error(f"清空目录失败 {directory_path}: {str(e)}")

    def copy_templates_to_apply_input(self, selected_files):
        """复制模板文件到Apply输入文件夹并重命名"""
        try:
            templates_path = self.base_path / "Templates"
            apply_input_path = self.base_path / "Apply" / "input_files"
            apply_input_path.mkdir(parents=True, exist_ok=True)
            
            # 定义文件类型与模板匹配规则的映射
            template_matching_rules = {
                "DVP": {
                    "keywords": ["设计验证计划"],
                    "extension": ".xlsx"
                },
                "PPL": {
                    "keywords": ["开发匹配测试计划"],
                    "extension": ".xlsx"
                },
                "FN_IMU": {
                    "keywords": ["气囊", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_VCU": {
                    "keywords": ["VCU", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_IPB": {
                    "keywords": ["IPB", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_ESP_BWA": {
                    "keywords": ["ESP", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_EPS": {
                    "keywords": ["EPS", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_EPSA": {
                    "keywords": ["EPSA", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_EPB": {
                    "keywords": ["EPB", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_A": {
                    "keywords": ["DiSus-A", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_C": {
                    "keywords": ["DiSus-C", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_P": {
                    "keywords": ["DiSus-P", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_X": {
                    "keywords": ["DiSus-X", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_DISUS_M": {
                    "keywords": ["DiSus-M", "接口定义通知单"],
                    "extension": ".docx"
                },
                "FN_域控": {
                    "keywords": ["域", "接口定义通知单"],
                    "extension": ".docx"
                }
            }
            
            for file_type in selected_files:
                source_file = None
                
                if file_type in template_matching_rules:
                    # 使用新的匹配规则
                    rule = template_matching_rules[file_type]
                    keywords = rule["keywords"]
                    extension = rule["extension"]
                    
                    # 查找符合所有关键词的模板文件
                    all_files = list(templates_path.glob(f"*{extension}"))
                    for template_file in all_files:
                        filename = template_file.name
                        # 检查文件名是否包含所有必需的关键词
                        if all(keyword in filename for keyword in keywords):
                            source_file = template_file
                            break
                else:
                    # 备用匹配方式：使用原有的关键字匹配
                    template_files = list(templates_path.glob(f"*{file_type}*.xlsx"))
                    if not template_files:
                        template_files = list(templates_path.glob(f"*{file_type}*.docx"))
                    if template_files:
                        source_file = template_files[0]
                
                if source_file:
                    # 智能重命名：将模板文件名中的XX替换为车型代号
                    original_filename = source_file.name
                    if "XX" in original_filename:
                        # 替换XX为车型代号
                        target_filename = original_filename.replace("XX", self.current_vehicle_code)
                    else:
                        # 如果没有XX，使用映射方式生成文件名
                        file_mapping = {
                            "DVP": f"{self.current_vehicle_code}_DVP_系统设计验证计划.xlsx",
                            "PPL": f"{self.current_vehicle_code}_PPL_开发匹配测试计划.xlsx",
                            "FN_IMU": f"{self.current_vehicle_code}_FN_IMU_接口定义通知单.docx",
                            "FN_VCU": f"{self.current_vehicle_code}_FN_VCU_接口定义通知单.docx",
                            "FN_IPB": f"{self.current_vehicle_code}_FN_IPB_接口定义通知单.docx",
                            "FN_ESP_BWA": f"{self.current_vehicle_code}_FN_ESP_BWA_接口定义通知单.docx",
                            "FN_EPS": f"{self.current_vehicle_code}_FN_EPS_接口定义通知单.docx",
                            "FN_EPSA": f"{self.current_vehicle_code}_FN_EPSA_接口定义通知单.docx",
                            "FN_EPB": f"{self.current_vehicle_code}_FN_EPB_接口定义通知单.docx",
                            "FN_DISUS_A": f"{self.current_vehicle_code}_FN_DiSus-A_接口定义通知单.docx",
                            "FN_DISUS_C": f"{self.current_vehicle_code}_FN_DiSus-C_接口定义通知单.docx",
                            "FN_DISUS_P": f"{self.current_vehicle_code}_FN_DiSus-P_接口定义通知单.docx",
                            "FN_DISUS_X": f"{self.current_vehicle_code}_FN_DiSus-X_接口定义通知单.docx",
                            "FN_DISUS_M": f"{self.current_vehicle_code}_FN_DiSus-M_接口定义通知单.docx",
                            "FN_域控": f"{self.current_vehicle_code}_FN_域控_接口定义通知单.docx"
                        }
                        target_filename = file_mapping.get(file_type, f"{self.current_vehicle_code}_{file_type}.docx")
                    
                    target_file = apply_input_path / target_filename
                    
                    shutil.copy2(source_file, target_file)
                    self.logger.info(f"已复制模板文件: {source_file} -> {target_file}")
                else:
                    self.logger.warning(f"未找到 {file_type} 的模板文件")
                    
        except Exception as e:
            self.logger.error(f"复制模板文件失败: {str(e)}")
            raise

    def configure_apply_config(self, username, password):
        """配置Apply/config.py文件"""
        try:
            config_file = self.base_path / "Apply" / "config.py"
            
            # 读取当前配置
            config_content = ""
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
            
            # 更新配置项
            lines = config_content.split('\n')
            updated_lines = []
            
            username_set = False
            password_set = False
            project_code_set = False
            headless_set = False
            
            for line in lines:
                if line.strip().startswith('USERNAME ='):
                    updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
                    username_set = True
                elif line.strip().startswith('PASSWORD ='):
                    updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
                    password_set = True
                elif line.strip().startswith('PROJECT_CODE ='):
                    updated_lines.append(f'PROJECT_CODE = "{self.current_vehicle_code}"  # 请填写车型代号')
                    project_code_set = True
                elif line.strip().startswith('HEADLESS_MODE ='):
                    headless_mode = "True" if self.silent_mode_cb.isChecked() else "False"
                    updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
                    headless_set = True
                else:
                    updated_lines.append(line)
            
            # 如果某些配置项不存在，则添加
            if not username_set:
                updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
            if not password_set:
                updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
            if not project_code_set:
                updated_lines.append(f'PROJECT_CODE = "{self.current_vehicle_code}"  # 请填写车型代号')
            if not headless_set:
                headless_mode = "True" if self.silent_mode_cb.isChecked() else "False"
                updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
            
            # 写入更新后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(updated_lines))
            
            self.logger.info("Apply配置文件已更新")
            
        except Exception as e:
            self.logger.error(f"配置Apply模块失败: {str(e)}")
            raise

    def run_apply_id_process(self):
        """运行Apply\run_apply_id.py"""
        try:
            apply_script = self.base_path / "Apply" / "run_apply_id.py"
            
            if not apply_script.exists():
                raise FileNotFoundError(f"Apply脚本不存在: {apply_script}")
            
            # 使用当前Python解释器的完整路径
            python_executable = sys.executable
            
            # 使用subprocess运行脚本
            result = subprocess.run([
                python_executable, str(apply_script)
            ], cwd=str(self.base_path / "Apply"), 
               capture_output=True, text=True, encoding='utf-8')
            
            self.logger.info(f"Apply进程输出: {result.stdout}")
            if result.stderr:
                self.logger.warning(f"Apply进程错误: {result.stderr}")
            
            if result.returncode == 0:
                self.progress_bar.setValue(60)
                self.statusBar().showMessage("申请编号完成，正在分发文件...")
                self.distribute_numbered_files()
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                raise Exception(f"Apply进程执行失败: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"运行Apply进程失败: {str(e)}")
            raise

    def distribute_numbered_files(self):
        """将已编号的文件分发到Fillin相应的输入文件夹"""
        try:
            apply_output_path = self.base_path / "Apply" / "output_files"
            
            # 文件类型到Fillin文件夹的映射
            fillin_mapping = {
                "DVP": self.base_path / "Fillin" / "dvp" / "input",
                "PPL": self.base_path / "Fillin" / "ppl" / "inputs",
                "FN": self.base_path / "Fillin" / "fn" / "inputs"
            }
            
            # 记录实际分发的文件类型
            distributed_types = set()
            
            # 扫描Apply输出文件夹
            for file_path in apply_output_path.iterdir():
                if file_path.is_file():
                    filename = file_path.name
                    target_dir = None
                    file_type = None
                    
                    # 根据文件名内容确定文件类型
                    if "设计验证计划" in filename:
                        target_dir = fillin_mapping["DVP"]
                        file_type = "DVP"
                    elif "接口定义通知单" in filename or "接口定义" in filename:
                        target_dir = fillin_mapping["FN"]
                        file_type = "FN"
                    elif "开发匹配测试计划" in filename or "匹配计划" in filename:
                        target_dir = fillin_mapping["PPL"]
                        file_type = "PPL"
                    else:
                        # 备用识别方式：使用原有的关键字识别
                        if "DVP" in filename:
                            target_dir = fillin_mapping["DVP"]
                            file_type = "DVP"
                        elif "FN" in filename:
                            target_dir = fillin_mapping["FN"]
                            file_type = "FN"
                        elif "PPL" in filename:
                            target_dir = fillin_mapping["PPL"]
                            file_type = "PPL"
                        else:
                            self.logger.warning(f"未识别的文件类型，跳过文件: {filename}")
                            continue  # 跳过未识别的文件
                    
                    if target_dir and file_type:
                        target_dir.mkdir(parents=True, exist_ok=True)
                        target_file = target_dir / filename
                        shutil.move(str(file_path), str(target_file))
                        distributed_types.add(file_type)
                        self.logger.info(f"分发文件: {filename} -> {target_dir}")
            
            self.progress_bar.setValue(70)
            self.statusBar().showMessage("正在填写文件内容...")
            # 只运行有文件分发的Fillin程序
            self.run_fillin_processes(distributed_types)
            
        except Exception as e:
            self.logger.error(f"分发文件失败: {str(e)}")
            raise

    def run_fillin_processes(self, distributed_types):
        """运行Fillin各个模块的处理程序"""
        try:
            # 使用当前Python解释器的完整路径
            python_executable = sys.executable
            
            # 定义脚本路径和对应的文件类型
            fillin_scripts = [
                (self.base_path / "Fillin" / "dvp" / "main.py", "DVP"),
                (self.base_path / "Fillin" / "fn" / "src" / "main.py", "FN"),
                (self.base_path / "Fillin" / "ppl" / "src" / "main.py", "PPL")
            ]
            
            executed_count = 0
            total_to_execute = len([script for script, file_type in fillin_scripts if file_type in distributed_types])
            
            for script, file_type in fillin_scripts:
                # 只运行有对应文件分发的程序
                if file_type not in distributed_types:
                    self.logger.info(f"跳过填写程序 {script}：没有对应的 {file_type} 文件")
                    continue
                
                if script.exists():
                    self.logger.info(f"运行填写程序: {script}")
                    
                    # 使用当前Python解释器运行脚本
                    result = subprocess.run([
                        python_executable, str(script)
                    ], cwd=str(script.parent), 
                       capture_output=True, text=True, encoding='utf-8')
                    
                    if result.stdout:
                        self.logger.info(f"填写程序输出 {script}: {result.stdout}")
                    
                    if result.returncode != 0:
                        error_msg = result.stderr or result.stdout or "未知错误"
                        self.logger.warning(f"填写程序执行警告 {script}: {error_msg}")
                    else:
                        self.logger.info(f"填写程序执行成功: {script}")
                
                executed_count += 1
                # 更新进度
                if total_to_execute > 0:
                    progress = 70 + (executed_count * 20 // total_to_execute)
                    self.progress_bar.setValue(min(progress, 90))
            
            self.progress_bar.setValue(90)
            self.statusBar().showMessage("正在收集处理结果...")
            self.collect_final_results()
            
        except Exception as e:
            self.logger.error(f"运行填写进程失败: {str(e)}")
            raise

    def collect_final_results(self):
        """收集最终处理结果到车型文件夹"""
        try:
            # 创建结果文件夹
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            result_folder = vehicle_dir / "Numbered_and_Filled"
            result_folder.mkdir(parents=True, exist_ok=True)
            
            # 收集所有输出文件
            output_dirs = [
                self.base_path / "Fillin" / "dvp" / "output",
                self.base_path / "Fillin" / "fn" / "outputs",
                self.base_path / "Fillin" / "ppl" / "outputs"
            ]
            
            collected_files = 0
            for output_dir in output_dirs:
                if output_dir.exists():
                    for file_path in output_dir.iterdir():
                        if file_path.is_file():
                            target_file = result_folder / file_path.name
                            shutil.move(str(file_path), str(target_file))
                            collected_files += 1
                            self.logger.info(f"收集结果文件: {file_path.name}")
            
            # 清理配置文件中的敏感信息
            self.clear_config_credentials()
            
            self.progress_bar.setValue(100)
            self.statusBar().showMessage("文件处理完成!")
            
            # 完成后的处理
            QMessageBox.information(self, "成功", 
                f"文件处理完成!\n\n"
                f"• 已收集 {collected_files} 个处理完成的文件\n"
                f"• 文件保存在: {result_folder}\n"
                f"• 账号密码已清理\n\n"
                f"点击'查看处理结果'按钮可打开结果文件夹。")
            
            self.execute_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            
            # 刷新文件状态
            self.load_vehicle_file_status()
            
        except Exception as e:
            self.logger.error(f"收集最终结果失败: {str(e)}")
            raise

    def clear_config_credentials(self):
        """清理配置文件中的账号密码信息"""
        try:
            config_files = [
                self.base_path / "Apply" / "config.py",
                self.base_path / "Upload" / "config.py"
            ]
            
            for config_file in config_files:
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 清空用户名和密码
                    lines = content.split('\n')
                    updated_lines = []
                    
                    for line in lines:
                        if line.strip().startswith('USERNAME ='):
                            updated_lines.append('USERNAME = ""  # 请填写您的用户名')
                        elif line.strip().startswith('PASSWORD ='):
                            updated_lines.append('PASSWORD = ""  # 请填写您的密码')
                        else:
                            updated_lines.append(line)
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(updated_lines))
            
            self.logger.info("配置文件中的敏感信息已清理")
            
        except Exception as e:
            self.logger.error(f"清理配置文件失败: {str(e)}")

    def open_numbered_filled_folder(self):
        """打开处理结果文件夹（Numbered_and_Filled）"""
        try:
            if not self.current_vehicle_code:
                QMessageBox.warning(self, "警告", "请先选择车型代号!")
                return
                
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            result_folder = vehicle_dir / "Numbered_and_Filled"
            
            if not result_folder.exists():
                result_folder.mkdir(parents=True, exist_ok=True)
                
            os.startfile(str(result_folder))
            
        except Exception as e:
            self.logger.error(f"打开结果文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开结果文件夹失败: {str(e)}")

    def open_final_approval_folder(self):
        """打开当前车型的Final_Files文件夹"""
        try:
            if not self.current_vehicle_code:
                QMessageBox.warning(self, "警告", "请先选择车型代号!")
                return
                
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            final_folder = vehicle_dir / "Final_Files"
            
            if not final_folder.exists():
                final_folder.mkdir(parents=True, exist_ok=True)
                
            os.startfile(str(final_folder))
            
        except Exception as e:
            self.logger.error(f"打开审批文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开审批文件夹失败: {str(e)}")

    def open_vehicles_folder(self):
        """打开车型文件夹"""
        try:
            if self.current_vehicle_code:
                vehicle_dir = self.vehicles_path / self.current_vehicle_code
                if vehicle_dir.exists():
                    os.startfile(str(vehicle_dir))
                else:
                    os.startfile(str(self.vehicles_path))
            else:
                os.startfile(str(self.vehicles_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_templates_folder(self):
        """打开模板文件夹"""
        try:
            os.startfile(str(self.templates_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_logs_folder(self):
        """打开日志文件夹"""
        try:
            os.startfile(str(self.logs_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_main_log(self):
        """打开主程序日志"""
        try:
            log_files = list(self.logs_path.glob("vehicle_management_*.log"))
            if log_files:
                # 打开最新的日志文件
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                os.startfile(str(latest_log))
            else:
                QMessageBox.information(self, "提示", "没有找到主程序日志文件")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开日志文件: {str(e)}")

    def open_subprocess_log(self):
        """打开子进程日志"""
        try:
            # 这里可以打开Fillin程序的日志
            dvp_logs = self.fillin_path / "dvp" / "logs"
            fn_logs = self.fillin_path / "fn" / "logs"
            ppl_logs = self.fillin_path / "ppl" / "logs"
            
            for log_dir in [dvp_logs, fn_logs, ppl_logs]:
                if log_dir.exists():
                    os.startfile(str(log_dir))
                    break
            else:
                QMessageBox.information(self, "提示", "没有找到子进程日志目录")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开子进程日志: {str(e)}")

    def open_update_templates_folder(self):
        """打开更新模板文件夹"""
        try:
            self.update_templates_path.mkdir(exist_ok=True)
            os.startfile(str(self.update_templates_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")

    def replace_templates(self):
        """替换模板"""
        try:
            update_templates_path = self.update_templates_path
            templates_path = self.templates_path
            
            if not update_templates_path.exists():
                QMessageBox.warning(self, "警告", "Update_Templates文件夹不存在")
                return
            
            replaced_count = 0
            for update_file in update_templates_path.iterdir():
                if update_file.is_file():
                    target_file = templates_path / update_file.name
                    shutil.copy2(update_file, target_file)
                    replaced_count += 1
                    self.logger.info(f"替换模板文件: {update_file.name}")
            
            if replaced_count > 0:
                QMessageBox.information(self, "成功", f"模板替换完成! 共替换 {replaced_count} 个文件")
            else:
                QMessageBox.information(self, "提示", "没有找到需要替换的文件")
                
        except Exception as e:
            self.logger.error(f"替换模板失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"替换模板失败: {str(e)}")

    def open_operation_log(self):
        """打开操作日志"""
        try:
            operation_log = self.logs_path / "operation_history.log"
            if operation_log.exists():
                os.startfile(str(operation_log))
            else:
                QMessageBox.information(self, "提示", "没有找到操作日志文件")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开操作日志: {str(e)}")

    def update_time(self):
        """更新状态栏时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def closeEvent(self, event):
        """程序关闭时的处理"""
        # 清空密码
        self.password_edit.clear()
        
        event.accept()

    def open_admin_list_image(self):
        """打开数据管理员名单图片"""
        try:
            admin_list_image = self.templates_path / "数据管理员名单.png"
            if admin_list_image.exists():
                os.startfile(str(admin_list_image))
            else:
                QMessageBox.warning(self, "警告", "数据管理员名单图片不存在")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开图片: {str(e)}")

    def upload_approval_files(self):
        """上传审批文件"""
        if not self.current_vehicle_code:
            QMessageBox.warning(self, "警告", "请先选择车型代号!")
            return
        
        # 检查登录信息
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "警告", "请输入用户名和密码!")
            return
        
        try:
            # 禁用上传按钮
            self.upload_approval_btn.setEnabled(False)
            self.upload_progress.setVisible(True)
            self.upload_progress.setValue(0)
            
            self.statusBar().showMessage("正在准备上传文件...")
            
            # 第一步：清空Upload工作目录
            upload_final_path = self.base_path / "Upload" / "Final_Approval_Documents"
            upload_data_path = self.base_path / "Upload" / "Data"
            self.clear_directory(upload_final_path)
            self.clear_directory(upload_data_path)
            
            self.upload_progress.setValue(20)
            
            # 第二步：复制Final_Files到Upload文件夹
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            final_files_dir = vehicle_dir / "Final_Files"
            
            if not final_files_dir.exists() or not any(final_files_dir.iterdir()):
                QMessageBox.warning(self, "警告", 
                    f"车型 {self.current_vehicle_code} 的审批文件夹为空!\n\n"
                    f"请先将要上传的文件放入: {final_files_dir}")
                self.upload_approval_btn.setEnabled(True)
                self.upload_progress.setVisible(False)
                return
            
            # 复制所有文件到Upload文件夹
            for file_path in final_files_dir.iterdir():
                if file_path.is_file():
                    target_file = upload_final_path / file_path.name
                    shutil.copy2(file_path, target_file)
                    self.logger.info(f"复制审批文件: {file_path.name}")
            
            self.upload_progress.setValue(40)
            self.statusBar().showMessage("正在复制数据文件...")
            
            # 第三步：复制information文件夹中的xlsx文件到Upload/Data
            info_dir = vehicle_dir / "information"
            if info_dir.exists():
                xlsx_files = list(info_dir.glob("*.xlsx"))
                if xlsx_files:
                    source_file = xlsx_files[0]
                    target_file = upload_data_path / source_file.name
                    shutil.copy2(source_file, target_file)
                    self.logger.info(f"复制数据文件: {source_file.name}")
            
            self.upload_progress.setValue(60)
            self.statusBar().showMessage("正在配置Upload模块...")
            
            # 第四步：配置Upload/config.py
            self.configure_upload_config(username, password)
            
            self.upload_progress.setValue(70)
            self.statusBar().showMessage("正在执行上传审批...")
            
            # 第五步：运行Upload/main_controller.py
            self.run_upload_process()
            
        except Exception as e:
            self.logger.error(f"上传审批失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"上传审批失败: {str(e)}")
            self.upload_approval_btn.setEnabled(True)
            self.upload_progress.setVisible(False)

    def configure_upload_config(self, username, password):
        """配置Upload/config.py文件"""
        try:
            config_file = self.base_path / "Upload" / "config.py"
            
            # 读取当前配置
            config_content = ""
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
            
            # 更新配置项
            lines = config_content.split('\n')
            updated_lines = []
            
            username_set = False
            password_set = False
            headless_set = False
            
            for line in lines:
                if line.strip().startswith('USERNAME ='):
                    updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
                    username_set = True
                elif line.strip().startswith('PASSWORD ='):
                    updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
                    password_set = True
                elif line.strip().startswith('HEADLESS_MODE ='):
                    headless_mode = "True" if self.upload_silent_mode_cb.isChecked() else "False"
                    updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
                    headless_set = True
                else:
                    updated_lines.append(line)
            
            # 如果某些配置项不存在，则添加
            if not username_set:
                updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
            if not password_set:
                updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
            if not headless_set:
                headless_mode = "True" if self.upload_silent_mode_cb.isChecked() else "False"
                updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
            
            # 写入更新后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(updated_lines))
            
            self.logger.info("Upload配置文件已更新")
            
        except Exception as e:
            self.logger.error(f"配置Upload模块失败: {str(e)}")
            raise

    def run_upload_process(self):
        """运行Upload/main_controller.py"""
        try:
            upload_script = self.base_path / "Upload" / "main_controller.py"
            
            if not upload_script.exists():
                raise FileNotFoundError(f"Upload脚本不存在: {upload_script}")
            
            # 使用当前Python解释器的完整路径
            python_executable = sys.executable
            
            # 使用subprocess运行脚本
            result = subprocess.run([
                python_executable, str(upload_script)
            ], cwd=str(self.base_path / "Upload"), 
               capture_output=True, text=True, encoding='utf-8')
            
            self.logger.info(f"Upload进程输出: {result.stdout}")
            if result.stderr:
                self.logger.warning(f"Upload进程错误: {result.stderr}")
            
            # 清理配置文件中的敏感信息
            self.clear_config_credentials()
            
            self.upload_progress.setValue(100)
            self.statusBar().showMessage("上传审批完成!")
            
            if result.returncode == 0:
                QMessageBox.information(self, "成功", 
                    "上传审批完成!\n\n"
                    "• 所有文件已上传到系统\n"
                    "• 审批流程已启动\n"
                    "• 账号密码已清理")
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                QMessageBox.warning(self, "警告", 
                    f"上传过程中出现问题:\n\n{error_msg}\n\n"
                    f"请检查日志了解详细信息。")
            
            self.upload_approval_btn.setEnabled(True)
            self.upload_progress.setVisible(False)
            
        except Exception as e:
            self.logger.error(f"运行Upload进程失败: {str(e)}")
            raise

    def copy_template_data_to_root(self):
        """复制车型信息文件夹中的xlsx文件到项目根目录"""
        try:
            if not self.current_vehicle_code:
                return
            
            # 车型信息文件夹路径
            vehicle_dir = self.vehicles_path / self.current_vehicle_code
            info_dir = vehicle_dir / "information"
            
            if not info_dir.exists():
                self.logger.warning(f"车型信息文件夹不存在: {info_dir}")
                return
            
            # 查找information文件夹中的xlsx文件
            xlsx_files = list(info_dir.glob("*.xlsx"))
            if not xlsx_files:
                self.logger.warning(f"车型信息文件夹中没有找到xlsx文件: {info_dir}")
                return
            
            # 使用第一个找到的xlsx文件
            source_file = xlsx_files[0]
            
            # 目标文件路径（项目根目录）
            target_file = self.base_path / "Fill_Template_Data.xlsx"
            
            # 复制文件并重命名
            shutil.copy2(str(source_file), str(target_file))
            
            self.logger.info(f"已复制模板数据文件: {source_file} -> {target_file}")
            
        except Exception as e:
            self.logger.error(f"复制模板数据文件失败: {str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("车型文件管理系统")
    app.setApplicationVersion("1.0.0")
    
    window = VehicleManagementGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
