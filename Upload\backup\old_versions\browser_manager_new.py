"""
浏览器管理模块
处理浏览器启动、登录、弹窗关闭等基础功能
"""

import os
import time
import logging
import shutil
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)

class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, config):
        self.config = config
        self.driver = None
        self.wait = None
        
    def setup_browser(self):
        """设置并启动浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 禁用保存密码提示
            prefs = {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            if self.config.get('HEADLESS', False):
                chrome_options.add_argument('--headless')
            
            # 查找ChromeDriver
            chromedriver_path = self._find_chromedriver()
            
            if chromedriver_path:
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info(f"使用ChromeDriver: {chromedriver_path}")
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
                logger.info("使用默认ChromeDriver")
            
            # 防止被检测为自动化
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置等待器
            self.wait = WebDriverWait(self.driver, self.config.get('WAIT_TIMEOUT', 20))
            logger.info("浏览器启动成功")
            
        except Exception as e:
            logger.error(f"浏览器启动失败: {str(e)}")
            raise
    
    def _find_chromedriver(self):
        """查找ChromeDriver路径"""
        current_dir = Path(__file__).parent
        possible_paths = [
            current_dir / "chromedriver.exe",
            current_dir.parent / "chromedriver.exe",
            "chromedriver.exe",
            "chromedriver"
        ]
        
        for path in possible_paths:
            if isinstance(path, Path) and path.exists():
                return str(path)
            elif isinstance(path, str) and shutil.which(path):
                return path
        
        return None
    
    def login(self, username, password, base_url):
        """登录系统"""
        try:
            logger.info("开始登录...")
            self.driver.get(base_url)
            time.sleep(self.config.get('OPERATION_DELAY', 3) + 1)
            
            # 点击登录按钮
            login_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span")
            ))
            login_btn.click()
            logger.info("已点击登录按钮")
            time.sleep(self.config.get('OPERATION_DELAY', 3))
            
            # 输入用户名
            username_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div/div/div/input")
            ))
            username_field.clear()
            username_field.send_keys(username)
            logger.info("已输入用户名")
            time.sleep(1)
            
            # 输入密码
            password_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[2]/div/div/input")
            ))
            password_field.clear()
            password_field.send_keys(password)
            logger.info("已输入密码")
            time.sleep(1)
            
            # 点击登录
            submit_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[4]/div/button")
            ))
            submit_btn.click()
            logger.info("已点击登录提交按钮")
            time.sleep(5)  # 等待登录完成
            
            logger.info("登录成功")
            
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            raise
    
    def close_popups(self):
        """关闭登录后的弹窗"""
        try:
            logger.info("🔄 开始弹窗快速检测与关闭...")
            time.sleep(1)
            
            max_attempts = 8
            check_interval = 0.6
            popup_found = False
            
            for attempt in range(max_attempts):
                logger.info(f"  📍 第 {attempt + 1}/{max_attempts} 次弹窗检测...")
                current_popup_closed = False
                
                # 尝试关闭智能小助手弹窗
                try:
                    close_btn1 = self.driver.find_element(By.CSS_SELECTOR, ".maxkb-close")
                    if close_btn1.is_displayed():
                        close_btn1.click()
                        logger.info("    ✅ 关闭智能小助手弹窗")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试点击"我知道了"按钮
                try:
                    know_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), '我知道了')]")
                    if know_btn.is_displayed():
                        know_btn.click()
                        logger.info("    ✅ 点击'我知道了'按钮")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试关闭文档管控公告弹窗
                try:
                    notification_selectors = [
                        ".el-notification .el-icon",
                        ".el-notification__closeBtn",
                        "#notification_1 .el-icon",
                        ".layout-notification .el-icon"
                    ]
                    
                    for selector in notification_selectors:
                        try:
                            close_btn2 = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if close_btn2.is_displayed():
                                close_btn2.click()
                                logger.info(f"    ✅ 关闭文档管控公告弹窗 (选择器: {selector})")
                                current_popup_closed = True
                                popup_found = True
                                time.sleep(0.5)
                                break
                        except (NoSuchElementException, Exception):
                            continue
                except Exception:
                    pass
                
                # 检测逻辑
                if not current_popup_closed:
                    if popup_found and attempt < max_attempts - 1:
                        time.sleep(check_interval)
                        continue
                    elif not popup_found and attempt >= 3:
                        logger.info("    📝 连续多次未发现弹窗，提前结束检测")
                        break
                    else:
                        time.sleep(check_interval)
                else:
                    time.sleep(0.8)
                    
            if popup_found:
                logger.info("🎉 弹窗关闭流程完成")
                time.sleep(2.5)
            else:
                logger.info("✨ 弹窗检测完成 (未发现弹窗)")
                time.sleep(1.5)
                
        except Exception as e:
            logger.warning(f"⚠️ 关闭弹窗过程中出现问题，继续执行: {str(e)}")
    
    def navigate_to_document_creation(self):
        """导航到文档创建页面"""
        try:
            logger.info("🧭 导航到文档创建页面...")
            time.sleep(1.5)
            
            # 尝试点击文档创建按钮
            create_button_selectors = [
                "//*[@id='main']/div/div/div/div/div/div[2]/span",
                "//span[contains(text(), '创建文档')]",
                ".CreateDocument span",
                "//div[contains(@class, 'CreateDocument')]//span"
            ]
            
            button_clicked = False
            for i, selector in enumerate(create_button_selectors):
                try:
                    if selector.startswith("//") or selector.startswith("/"):
                        create_btn = WebDriverWait(self.driver, 8).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        create_btn = WebDriverWait(self.driver, 8).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    create_btn.click()
                    logger.info(f"  ✅ 已点击文档创建按钮 (选择器 {i+1})")
                    button_clicked = True
                    break
                except TimeoutException:
                    logger.info(f"  ⚠️ 选择器 {i+1} 未找到，尝试下一个...")
                    continue
                    
            if not button_clicked:
                raise Exception("❌ 无法找到文档创建按钮")
                
            time.sleep(2.5)
            
            # 验证模态框是否打开
            try:
                modal = WebDriverWait(self.driver, 8).until(EC.presence_of_element_located(
                    (By.CSS_SELECTOR, ".el-drawer")
                ))
                logger.info("  🎯 文档创建模态框已成功打开")
            except TimeoutException:
                raise Exception("❌ 文档创建模态框未能打开")
            
        except Exception as e:
            logger.error(f"❌ 导航到文档创建页面失败: {str(e)}")
            raise
    
    def quit(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {str(e)}")
