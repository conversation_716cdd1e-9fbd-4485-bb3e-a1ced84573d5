"""
处理流程管理模块
包含文件处理的完整流程控制和配置管理
"""

import subprocess
import sys
import logging
from PyQt5.QtWidgets import QMessageBox


class ProcessManager:
    """处理流程管理类"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
    
    def start_complete_file_processing(self, selected_files, username, password):
        """启动完整的文件处理流程"""
        try:
            # 第一步：准备工作环境和文件
            self.main_window.progress_bar.setValue(10)
            self.main_window.statusBar().showMessage("正在准备工作环境...")
            
            # 清空Apply文件夹
            apply_input_path = self.main_window.base_path / "Apply" / "input_files"
            apply_output_path = self.main_window.base_path / "Apply" / "output_files"
            self.main_window.file_operations.clear_directory(apply_input_path)
            self.main_window.file_operations.clear_directory(apply_output_path)
            
            # 清空Fillin文件夹
            fillin_paths = [
                self.main_window.base_path / "Fillin" / "dvp" / "input",
                self.main_window.base_path / "Fillin" / "dvp" / "output",
                self.main_window.base_path / "Fillin" / "fn" / "inputs",
                self.main_window.base_path / "Fillin" / "fn" / "outputs",
                self.main_window.base_path / "Fillin" / "ppl" / "inputs",
                self.main_window.base_path / "Fillin" / "ppl" / "outputs"
            ]
            for path in fillin_paths:
                self.main_window.file_operations.clear_directory(path)
            
            self.main_window.progress_bar.setValue(20)
            self.main_window.statusBar().showMessage("正在复制模板文件...")
            
            # 复制模板文件到Apply输入文件夹，并根据车型代号重命名
            self.main_window.file_operations.copy_templates_to_apply_input(selected_files)
            
            self.main_window.progress_bar.setValue(30)
            self.main_window.statusBar().showMessage("正在配置Apply模块...")
            
            # 配置Apply/config.py
            self.configure_apply_config(username, password)
            
            self.main_window.progress_bar.setValue(40)
            self.main_window.statusBar().showMessage("正在申请文件编号...")
            
            # 运行Apply\run_apply_id.py
            self.run_apply_id_process()
            
        except Exception as e:
            self.logger.error(f"文件处理流程失败: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"文件处理流程失败: {str(e)}")
            self.main_window.execute_btn.setEnabled(True)
            self.main_window.progress_bar.setVisible(False)

    def configure_apply_config(self, username, password):
        """配置Apply/config.py文件"""
        try:
            config_file = self.main_window.base_path / "Apply" / "config.py"
            
            # 读取当前配置
            config_content = ""
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
            
            # 更新配置项
            lines = config_content.split('\n')
            updated_lines = []
            
            username_set = False
            password_set = False
            project_code_set = False
            headless_set = False
            
            for line in lines:
                if line.strip().startswith('USERNAME ='):
                    updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
                    username_set = True
                elif line.strip().startswith('PASSWORD ='):
                    updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
                    password_set = True
                elif line.strip().startswith('PROJECT_CODE ='):
                    updated_lines.append(f'PROJECT_CODE = "{self.main_window.current_vehicle_code}"  # 请填写车型代号')
                    project_code_set = True
                elif line.strip().startswith('HEADLESS_MODE ='):
                    headless_mode = "True" if self.main_window.silent_mode_cb.isChecked() else "False"
                    updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
                    headless_set = True
                else:
                    updated_lines.append(line)
            
            # 如果某些配置项不存在，则添加
            if not username_set:
                updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
            if not password_set:
                updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
            if not project_code_set:
                updated_lines.append(f'PROJECT_CODE = "{self.main_window.current_vehicle_code}"  # 请填写车型代号')
            if not headless_set:
                headless_mode = "True" if self.main_window.silent_mode_cb.isChecked() else "False"
                updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
            
            # 写入更新后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(updated_lines))
            
            self.logger.info("Apply配置文件已更新")
            
        except Exception as e:
            self.logger.error(f"配置Apply模块失败: {str(e)}")
            raise

    def run_apply_id_process(self):
        """运行Apply\run_apply_id.py"""
        try:
            apply_script = self.main_window.base_path / "Apply" / "run_apply_id.py"
            
            if not apply_script.exists():
                raise FileNotFoundError(f"Apply脚本不存在: {apply_script}")
            
            # 使用当前Python解释器的完整路径
            python_executable = sys.executable
            
            # 使用subprocess运行脚本
            result = subprocess.run([
                python_executable, str(apply_script)
            ], cwd=str(self.main_window.base_path / "Apply"), 
               capture_output=True, text=True, encoding='utf-8')
            
            self.logger.info(f"Apply进程输出: {result.stdout}")
            if result.stderr:
                self.logger.warning(f"Apply进程错误: {result.stderr}")
            
            if result.returncode == 0:
                self.main_window.progress_bar.setValue(60)
                self.main_window.statusBar().showMessage("申请编号完成，正在分发文件...")
                self.distribute_numbered_files()
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                raise Exception(f"Apply进程执行失败: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"运行Apply进程失败: {str(e)}")
            raise

    def distribute_numbered_files(self):
        """将已编号的文件分发到Fillin相应的输入文件夹"""
        try:
            distributed_types = self.main_window.file_operations.distribute_numbered_files()
            
            self.main_window.progress_bar.setValue(70)
            self.main_window.statusBar().showMessage("正在填写文件内容...")
            # 只运行有文件分发的Fillin程序
            self.run_fillin_processes(distributed_types)
            
        except Exception as e:
            self.logger.error(f"分发文件失败: {str(e)}")
            raise

    def run_fillin_processes(self, distributed_types):
        """运行Fillin各个模块的处理程序"""
        try:
            success = self.main_window.file_operations.run_fillin_processes(distributed_types)
            
            if success:
                self.main_window.progress_bar.setValue(90)
                self.main_window.statusBar().showMessage("正在收集处理结果...")
                self.collect_final_results()
            else:
                self.logger.warning("没有执行任何Fillin进程")
                self.collect_final_results()
            
        except Exception as e:
            self.logger.error(f"运行填写进程失败: {str(e)}")
            raise

    def collect_final_results(self):
        """收集最终处理结果"""
        try:
            collected_files = self.main_window.file_operations.collect_final_results()
            
            self.main_window.progress_bar.setValue(100)
            self.main_window.statusBar().showMessage(f"处理完成！共生成 {collected_files} 个文件")
            
            # 显示完成消息
            QMessageBox.information(
                self.main_window, 
                "处理完成", 
                f"文件处理已完成！\n\n共生成 {collected_files} 个文件\n结果已保存到车型文件夹和审批文件夹中"
            )
            
            # 重置UI状态
            self.main_window.execute_btn.setEnabled(True)
            self.main_window.progress_bar.setVisible(False)
            
            # 清空配置文件中的敏感信息
            self.clear_config_credentials()
            
        except Exception as e:
            self.logger.error(f"收集结果失败: {str(e)}")
            raise

    def clear_config_credentials(self):
        """清空配置文件中的敏感信息"""
        try:
            config_file = self.main_window.base_path / "Apply" / "config.py"
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                
                # 清空用户名和密码
                lines = config_content.split('\n')
                updated_lines = []
                
                for line in lines:
                    if line.strip().startswith('USERNAME ='):
                        updated_lines.append('USERNAME = ""  # 请填写您的用户名')
                    elif line.strip().startswith('PASSWORD ='):
                        updated_lines.append('PASSWORD = ""  # 请填写您的密码')
                    else:
                        updated_lines.append(line)
                
                # 写入更新后的配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(updated_lines))
                
                self.logger.info("已清空配置文件中的敏感信息")
                
        except Exception as e:
            self.logger.error(f"清空配置凭证失败: {str(e)}")

    def configure_upload_config(self, username, password):
        """配置Upload/config.py文件"""
        try:
            config_file = self.main_window.base_path / "Upload" / "config.py"
            
            # 读取当前配置
            config_content = ""
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
            
            # 更新配置项
            lines = config_content.split('\n')
            updated_lines = []
            
            username_set = False
            password_set = False
            headless_set = False
            
            for line in lines:
                if line.strip().startswith('USERNAME ='):
                    updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
                    username_set = True
                elif line.strip().startswith('PASSWORD ='):
                    updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
                    password_set = True
                elif line.strip().startswith('HEADLESS_MODE ='):
                    headless_mode = "True" if self.main_window.upload_silent_mode_cb.isChecked() else "False"
                    updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
                    headless_set = True
                else:
                    updated_lines.append(line)
            
            # 如果某些配置项不存在，则添加
            if not username_set:
                updated_lines.append(f'USERNAME = "{username}"  # 请填写您的用户名')
            if not password_set:
                updated_lines.append(f'PASSWORD = "{password}"  # 请填写您的密码')
            if not headless_set:
                headless_mode = "True" if self.main_window.upload_silent_mode_cb.isChecked() else "False"
                updated_lines.append(f'HEADLESS_MODE = {headless_mode}')
            
            # 写入更新后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(updated_lines))
            
            self.logger.info("Upload配置文件已更新")
            
        except Exception as e:
            self.logger.error(f"配置Upload模块失败: {str(e)}")
            raise

    def run_upload_process(self):
        """运行Upload上传进程"""
        try:
            upload_script = self.main_window.base_path / "Upload" / "run.py"
            
            if not upload_script.exists():
                raise FileNotFoundError(f"Upload脚本不存在: {upload_script}")
            
            # 使用当前Python解释器的完整路径
            python_executable = sys.executable
            
            # 使用subprocess运行脚本
            result = subprocess.run([
                python_executable, str(upload_script)
            ], cwd=str(self.main_window.base_path / "Upload"), 
               capture_output=True, text=True, encoding='utf-8')
            
            self.logger.info(f"Upload进程输出: {result.stdout}")
            if result.stderr:
                self.logger.warning(f"Upload进程错误: {result.stderr}")
            
            if result.returncode == 0:
                self.main_window.upload_progress.setValue(100)
                self.main_window.statusBar().showMessage("上传完成")
                QMessageBox.information(self.main_window, "上传完成", "文件上传审批已完成！")
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                raise Exception(f"Upload进程执行失败: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"运行Upload进程失败: {str(e)}")
            QMessageBox.critical(self.main_window, "上传失败", f"上传过程中发生错误: {str(e)}")
        finally:
            # 重置UI状态
            self.main_window.upload_approval_btn.setEnabled(True)
            self.main_window.upload_progress.setVisible(False)
            # 清空Upload配置文件中的敏感信息
            self.clear_upload_config_credentials()

    def clear_upload_config_credentials(self):
        """清空Upload配置文件中的敏感信息"""
        try:
            config_file = self.main_window.base_path / "Upload" / "config.py"
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                
                # 清空用户名和密码
                lines = config_content.split('\n')
                updated_lines = []
                
                for line in lines:
                    if line.strip().startswith('USERNAME ='):
                        updated_lines.append('USERNAME = ""  # 请填写您的用户名')
                    elif line.strip().startswith('PASSWORD ='):
                        updated_lines.append('PASSWORD = ""  # 请填写您的密码')
                    else:
                        updated_lines.append(line)
                
                # 写入更新后的配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(updated_lines))
                
                self.logger.info("已清空Upload配置文件中的敏感信息")
                
        except Exception as e:
            self.logger.error(f"清空Upload配置凭证失败: {str(e)}")
