# 车型文件管理系统 - 完整依赖包列表
# Vehicle File Management System - Complete Dependencies

# GUI界面框架 (Main GUI Framework)
PyQt5==5.15.10

# 网页自动化 (Web Automation)
selenium==4.15.2

# 数据处理 (Data Processing)
pandas==2.1.4

# Excel文件处理 (Excel File Processing)
openpyxl==3.1.2

# Word文档处理 (Word Document Processing)
python-docx==1.1.0

# YAML配置文件处理 (YAML Configuration Processing)
pyyaml==6.0.1

# 可选依赖 (Optional Dependencies)
# ===================================

# 如果需要更多Excel功能 (For additional Excel features)
# xlsxwriter>=3.0.0

# 如果需要PDF处理 (For PDF processing)
# PyPDF2>=3.0.0

# 如果需要定时任务 (For scheduled tasks)
# schedule>=1.2.0

# 如果需要更好的日期时间处理 (For better datetime handling)
# python-dateutil>=2.8.0

# 系统兼容性说明 (System Compatibility Notes)
# ==========================================
# 本项目在以下环境测试通过：
# - Python 3.8+
# - Windows 10/11
# - Chrome浏览器（需要安装对应版本的ChromeDriver）
# 
# 安装命令 (Installation Command)：
# pip install -r requirements.txt
# 
# 或者逐个安装 (Or install individually)：
# pip install PyQt5==5.15.10 selenium==4.15.2 pandas==2.1.4 openpyxl==3.1.2 python-docx==1.1.0 pyyaml==6.0.1
