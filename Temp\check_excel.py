import pandas as pd

# 检查Excel模板的sheet名称和列名
df = pd.ExcelFile('Templates/Fill_Template_Data.xlsx')
print('Sheet names:', df.sheet_names)
print()

for sheet in df.sheet_names:
    print(f'Sheet: {sheet}')
    sheet_df = df.parse(sheet)
    print('Columns:', sheet_df.columns.tolist())
    if len(sheet_df) > 0:
        print('First few rows:')
        print(sheet_df.head(3))
    print('-' * 50)
    print()
