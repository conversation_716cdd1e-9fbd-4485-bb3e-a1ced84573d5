#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修正后的File_Status表结构
"""

import sys
import os
import shutil
from pathlib import Path
import pandas as pd

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from file_manager import FileManager

def test_file_status_structure():
    """测试File_Status表结构"""
    print("开始测试File_Status表结构...")
    
    base_path = Path(__file__).parent
    file_manager = FileManager(base_path)
    
    # 使用新的测试车型代号
    test_vehicle = "TEST88"
    
    # 清理可能存在的旧测试数据
    vehicle_dir = file_manager.vehicles_path / test_vehicle
    if vehicle_dir.exists():
        shutil.rmtree(vehicle_dir)
        print(f"清理旧测试数据: {vehicle_dir}")
    
    try:
        # 首先设置车型文件夹
        print(f"\n设置车型文件夹: {test_vehicle}")
        created, target_file = file_manager.setup_vehicle_folder(test_vehicle)
        
        # 测试复制文件
        selected_files = ["DVP", "PPL"]
        print(f"\n测试复制文件: {selected_files}")
        
        copied_files = file_manager.copy_template_files(test_vehicle, selected_files)
        print(f"复制的文件: {copied_files}")
        
        # 检查File_Status表内容
        print(f"\n检查File_Status表结构:")
        info_file = vehicle_dir / "information" / f"{test_vehicle}_Fill_Template_Data.xlsx"
        if info_file.exists():
            df = pd.read_excel(info_file, sheet_name="File_Status")
            print(f"File_Status表内容:")
            print(df)
            print(f"\n列名: {df.columns.tolist()}")
            
            # 检查file_name字段内容
            print(f"\nfile_name字段内容:")
            for i, file_name in enumerate(df['file_name']):
                print(f"  {i+1}. {file_name}")
                if test_vehicle in str(file_name):
                    print(f"      ✅ 包含车型代号")
                else:
                    print(f"      ❌ 缺少车型代号")
        
        # 测试申请编号文件列表
        print(f"\n测试申请编号文件列表:")
        apply_files = file_manager.get_files_for_apply_id(test_vehicle)
        print(f"需要申请编号的文件数量: {len(apply_files)}")
        for file_info in apply_files:
            print(f"  - {file_info['file_name']} (车型: {file_info['vehicle_code']})")
        
        # 测试更新文件编号
        if apply_files:
            test_file = apply_files[0]['file_name']
            test_id = "20241224001"
            print(f"\n测试更新文件编号: {test_file} -> {test_id}")
            
            file_manager.update_file_with_id(test_vehicle, test_file, test_id)
            
            # 重新检查File_Status表
            df_updated = pd.read_excel(info_file, sheet_name="File_Status")
            print(f"\n更新后的File_Status表:")
            print(df_updated)
            
            # 检查numbered_file格式
            for _, row in df_updated.iterrows():
                if row['code'] == test_id:
                    numbered_file = row['numbered_file']
                    print(f"\nnumbered_file格式: {numbered_file}")
                    if f"{test_id}-{test_file}" == numbered_file:
                        print(f"✅ numbered_file格式正确")
                    else:
                        print(f"❌ numbered_file格式不正确，期望: {test_id}-{test_file}")
        
        print("✅ File_Status表结构测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_file_status_structure()
