import shutil
import logging
from pathlib import Path
import pandas as pd


class FileManager:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.templates_path = self.base_path / "Templates"
        self.vehicles_path = self.base_path / "Vehicles"
        self.logger = logging.getLogger(__name__)

    def setup_vehicle_folder(self, vehicle_code):
        """设置车型文件夹结构"""
        try:
            vehicle_dir = self.vehicles_path / vehicle_code
            info_dir = vehicle_dir / "information"
            info_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制并重命名Fill_Template_Data.xlsx
            template_file = self.templates_path / "Fill_Template_Data.xlsx"
            target_file = info_dir / f"{vehicle_code}_Fill_Template_Data.xlsx"
            
            if not target_file.exists():
                shutil.copy2(template_file, target_file)
                self.update_vehicle_code_in_excel(target_file, vehicle_code)
                self.logger.info(f"创建车型模板文件: {target_file}")
                return True, target_file
            else:
                self.logger.info(f"车型模板文件已存在: {target_file}")
                return False, target_file
                
        except Exception as e:
            self.logger.error(f"设置车型文件夹失败: {str(e)}")
            raise

    def update_vehicle_code_in_excel(self, excel_file, vehicle_code):
        """更新Excel文件中的车型代号"""
        try:
            # 读取Info sheet
            df = pd.read_excel(excel_file, sheet_name="Info")
            df.loc[df['角色'] == '车型代号', 'people'] = vehicle_code
            
            # 写回Excel文件
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name="Info", index=False)
            
            self.logger.info(f"更新Excel中车型代号为: {vehicle_code}")
            
        except Exception as e:
            self.logger.error(f"更新Excel中车型代号失败: {str(e)}")
            raise
