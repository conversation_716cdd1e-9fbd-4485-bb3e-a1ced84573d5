"""
工具函数和辅助方法模块
包含文件夹操作、日志处理、时间更新等功能
"""

import os
import shutil
import logging
from datetime import datetime
from pathlib import Path
from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem
import pandas as pd


class UtilityFunctions:
    """工具函数类"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
    
    def setup_style(self):
        """设置应用程序样式"""
        style = """
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 6px 12px;
                text-align: center;
                text-decoration: none;
                font-size: 10pt;
                margin: 2px;
                border-radius: 4px;
                min-height: 24px;
                max-height: 32px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 13px;
                height: 13px;
            }
            QComboBox {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 4px;
            }
            QLineEdit {
                border: 1px solid #cccccc;
                border-radius: 3px;
                padding: 4px;
            }
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """
        self.main_window.setStyleSheet(style)
    
    def load_vehicle_codes(self):
        """加载车型代号列表"""
        try:
            vehicle_codes = []
            if self.main_window.vehicles_path.exists():
                for item in self.main_window.vehicles_path.iterdir():
                    if item.is_dir():
                        vehicle_codes.append(item.name)
            
            # 排序并添加到下拉框
            vehicle_codes.sort()
            self.main_window.vehicle_combo.clear()
            self.main_window.vehicle_combo.addItems(vehicle_codes)
            
        except Exception as e:
            self.logger.error(f"加载车型代号失败: {str(e)}")
    
    def on_vehicle_changed(self, vehicle_code):
        """车型代号改变时的处理"""
        self.main_window.current_vehicle_code = vehicle_code.strip()
        if self.main_window.current_vehicle_code:
            self.load_vehicle_file_status()
    
    def load_vehicle_file_status(self):
        """加载车型文件状态"""
        try:
            if not self.main_window.current_vehicle_code:
                return
            
            info_dir = self.main_window.vehicles_path / self.main_window.current_vehicle_code / "information"
            excel_file = info_dir / f"{self.main_window.current_vehicle_code}_Fill_Template_Data.xlsx"
            
            # 清空表格
            self.main_window.upload_status_table.setRowCount(0)
            
            if excel_file.exists():
                try:
                    df = pd.read_excel(excel_file, sheet_name="File_Status")
                    
                    # 填充表格
                    self.main_window.upload_status_table.setRowCount(len(df))
                    for i, row in df.iterrows():
                        self.main_window.upload_status_table.setItem(i, 0, QTableWidgetItem(str(row.get('file_name', ''))))
                        self.main_window.upload_status_table.setItem(i, 1, QTableWidgetItem(str(row.get('code', ''))))
                        self.main_window.upload_status_table.setItem(i, 2, QTableWidgetItem(str(row.get('numbered_file', ''))))
                        self.main_window.upload_status_table.setItem(i, 3, QTableWidgetItem(str(row.get('is_fillin', ''))))
                        self.main_window.upload_status_table.setItem(i, 4, QTableWidgetItem(str(row.get('is_upload', ''))))
                        
                except Exception as e:
                    self.logger.warning(f"读取文件状态失败: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"加载文件状态失败: {str(e)}")
    
    def select_all_files(self):
        """全选文件"""
        self.main_window.dvp_cb.setChecked(True)
        self.main_window.ppl_cb.setChecked(True)
        for cb in self.main_window.fn_checkboxes.values():
            cb.setChecked(True)
    
    def deselect_all_files(self):
        """取消全选文件"""
        self.main_window.dvp_cb.setChecked(False)
        self.main_window.ppl_cb.setChecked(False)
        for cb in self.main_window.fn_checkboxes.values():
            cb.setChecked(False)
    
    def open_numbered_filled_folder(self):
        """打开编号填写后的文件夹"""
        try:
            if not self.main_window.current_vehicle_code:
                QMessageBox.warning(self.main_window, "警告", "请先选择车型代号!")
                return
            
            vehicle_dir = self.main_window.vehicles_path / self.main_window.current_vehicle_code
            result_folder = vehicle_dir / "Numbered_and_Filled"
            
            if result_folder.exists():
                os.startfile(str(result_folder))
            else:
                # 如果文件夹不存在，创建并打开
                result_folder.mkdir(parents=True, exist_ok=True)
                os.startfile(str(result_folder))
                QMessageBox.information(
                    self.main_window, 
                    "提示", 
                    f"已创建结果文件夹: {result_folder}\n\n处理完成后，文件将保存在此文件夹中。"
                )
                
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"打开文件夹失败: {str(e)}")
    
    def open_final_approval_folder(self):
        """打开最终审批文件夹"""
        try:
            final_approval_dir = self.main_window.base_path / "Final_Approval_Documents"
            final_approval_dir.mkdir(parents=True, exist_ok=True)
            os.startfile(str(final_approval_dir))
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"打开审批文件夹失败: {str(e)}")
    
    def open_vehicles_folder(self):
        """打开车型文件夹"""
        try:
            if self.main_window.current_vehicle_code:
                vehicle_dir = self.main_window.vehicles_path / self.main_window.current_vehicle_code
                if vehicle_dir.exists():
                    os.startfile(str(vehicle_dir))
                else:
                    os.startfile(str(self.main_window.vehicles_path))
            else:
                os.startfile(str(self.main_window.vehicles_path))
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_templates_folder(self):
        """打开模板文件夹"""
        try:
            os.startfile(str(self.main_window.templates_path))
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_logs_folder(self):
        """打开日志文件夹"""
        try:
            os.startfile(str(self.main_window.logs_path))
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_main_log(self):
        """打开主程序日志"""
        try:
            log_files = list(self.main_window.logs_path.glob("vehicle_management_*.log"))
            if log_files:
                # 打开最新的日志文件
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                os.startfile(str(latest_log))
            else:
                QMessageBox.information(self.main_window, "提示", "没有找到主程序日志文件")
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开日志文件: {str(e)}")
    
    def open_subprocess_log(self):
        """打开子进程日志"""
        try:
            # 这里可以打开Fillin程序的日志
            dvp_logs = self.main_window.fillin_path / "dvp" / "logs"
            fn_logs = self.main_window.fillin_path / "fn" / "logs"
            ppl_logs = self.main_window.fillin_path / "ppl" / "logs"
            
            for log_dir in [dvp_logs, fn_logs, ppl_logs]:
                if log_dir.exists():
                    os.startfile(str(log_dir))
                    break
            else:
                QMessageBox.information(self.main_window, "提示", "没有找到子进程日志目录")
                
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开子进程日志: {str(e)}")
    
    def open_update_templates_folder(self):
        """打开更新模板文件夹"""
        try:
            self.main_window.update_templates_path.mkdir(exist_ok=True)
            os.startfile(str(self.main_window.update_templates_path))
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开文件夹: {str(e)}")
    
    def replace_templates(self):
        """替换模板"""
        try:
            update_templates_path = self.main_window.update_templates_path
            templates_path = self.main_window.templates_path
            
            if not update_templates_path.exists():
                QMessageBox.warning(self.main_window, "警告", "Update_Templates文件夹不存在")
                return
            
            replaced_count = 0
            for update_file in update_templates_path.iterdir():
                if update_file.is_file():
                    target_file = templates_path / update_file.name
                    shutil.copy2(update_file, target_file)
                    replaced_count += 1
                    self.logger.info(f"替换模板文件: {update_file.name}")
            
            if replaced_count > 0:
                QMessageBox.information(self.main_window, "成功", f"模板替换完成! 共替换 {replaced_count} 个文件")
            else:
                QMessageBox.information(self.main_window, "提示", "没有找到需要替换的文件")
                
        except Exception as e:
            self.logger.error(f"替换模板失败: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"替换模板失败: {str(e)}")
    
    def open_operation_log(self):
        """打开操作日志"""
        try:
            operation_log = self.main_window.logs_path / "operation_history.log"
            if operation_log.exists():
                os.startfile(str(operation_log))
            else:
                QMessageBox.information(self.main_window, "提示", "没有找到操作日志文件")
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开操作日志: {str(e)}")
    
    def update_time(self):
        """更新状态栏时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.main_window.time_label.setText(current_time)
    
    def open_admin_list_image(self):
        """打开数据管理员名单图片"""
        try:
            admin_list_image = self.main_window.templates_path / "数据管理员名单.png"
            if admin_list_image.exists():
                os.startfile(str(admin_list_image))
            else:
                QMessageBox.warning(self.main_window, "警告", "数据管理员名单图片不存在")
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"无法打开图片: {str(e)}")
    
    def close_event_handler(self, event):
        """程序关闭时的处理"""
        # 清空密码
        self.main_window.password_edit.clear()
        event.accept()
