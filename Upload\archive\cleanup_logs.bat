@echo off
chcp 65001 >nul
echo Starting log cleanup...

REM 设置日志文件路径
set LOG_FILE=upload_approval.log
set MAX_SIZE_MB=5
set BACKUP_DIR=logs_backup

REM 检查日志文件是否存在
if not exist "%LOG_FILE%" (
    echo Log file does not exist: %LOG_FILE%
    goto :end
)

REM 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM 获取文件大小（字节）
for %%A in ("%LOG_FILE%") do set FILE_SIZE=%%~zA

REM 计算最大字节数 (5MB = 5242880 bytes)
set /a MAX_BYTES=%MAX_SIZE_MB% * 1024 * 1024

echo Current log file size: %FILE_SIZE% bytes
echo Maximum allowed size: %MAX_BYTES% bytes (%MAX_SIZE_MB% MB)

REM 如果文件超过限制，进行轮转
if %FILE_SIZE% gtr %MAX_BYTES% (
    echo Log file is too large, rotating...
    
    REM 创建时间戳
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "TIMESTAMP=%dt:~0,8%_%dt:~8,6%"
    
    REM 移动当前日志到备份
    move "%LOG_FILE%" "%BACKUP_DIR%\%LOG_FILE%.%TIMESTAMP%"
    
    REM 创建新日志文件
    echo === Log rotated at %date% %time% === > "%LOG_FILE%"
    
    echo Log file rotated successfully
) else (
    echo Log file size is within limits, no action needed
)

REM 清理超过7天的备份文件
echo Cleaning old backup files...
forfiles /p "%BACKUP_DIR%" /m "*.log.*" /d -7 /c "cmd /c echo Deleting @file && del @path" 2>nul

REM 限制备份文件数量（最多保留5个）
set COUNT=0
for /f %%i in ('dir /b /o-d "%BACKUP_DIR%\*.log.*" 2^>nul') do (
    set /a COUNT+=1
    if !COUNT! gtr 5 (
        echo Deleting old backup: %%i
        del "%BACKUP_DIR%\%%i"
    )
)

echo Log cleanup completed!

:end
pause
