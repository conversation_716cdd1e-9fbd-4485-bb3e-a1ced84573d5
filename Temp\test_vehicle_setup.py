#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修正后的车型设置功能
"""

import sys
import os
from pathlib import Path
import pandas as pd

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from file_manager import FileManager

def test_vehicle_setup():
    """测试车型设置功能"""
    print("开始测试车型设置功能...")
    
    base_path = Path(__file__).parent
    file_manager = FileManager(base_path)
    
    # 测试车型代号
    test_vehicle = "TEST01"
    
    try:
        # 测试设置车型文件夹
        print(f"设置车型文件夹: {test_vehicle}")
        created, target_file = file_manager.setup_vehicle_folder(test_vehicle)
        
        if created:
            print(f"✅ 成功创建车型文件夹和模板文件: {target_file}")
        else:
            print(f"ℹ️  车型文件夹已存在: {target_file}")
        
        # 验证Excel文件的sheet
        print(f"检查Excel文件sheet结构...")
        df_file = pd.ExcelFile(target_file)
        print(f"Sheet名称: {df_file.sheet_names}")
        
        # 验证Info sheet
        info_df = pd.read_excel(target_file, sheet_name="Info")
        print(f"Info sheet列名: {info_df.columns.tolist()}")
        
        # 检查车型代号是否正确设置
        vehicle_row = info_df[info_df['角色'] == '车型代号']
        if not vehicle_row.empty:
            current_vehicle = vehicle_row.iloc[0]['people']
            print(f"✅ 车型代号已设置为: {current_vehicle}")
        else:
            print("❌ 未找到车型代号行")
        
        # 验证其他sheet
        for sheet_name in ['File_Code', 'File_Status']:
            try:
                sheet_df = pd.read_excel(target_file, sheet_name=sheet_name)
                print(f"✅ {sheet_name} sheet存在，列名: {sheet_df.columns.tolist()}")
            except Exception as e:
                print(f"❌ {sheet_name} sheet有问题: {e}")
        
        print("✅ 车型设置功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_vehicle_setup()
