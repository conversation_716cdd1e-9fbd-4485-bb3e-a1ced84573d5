"""
第一个页面处理模块
处理文档信息填写、文件上传等功能
"""

import time
import logging
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)

class FirstPageHandler:
    """第一个页面处理器"""
    
    def __init__(self, driver, wait, config):
        self.driver = driver
        self.wait = wait
        self.config = config
        
    def fill_document_info(self, doc_id, source_file, file_type, excel_file=None):
        """填写文档信息（第一个页面）"""
        try:
            logger.info(f"📝 开始填写文档信息: {doc_id}")
            time.sleep(self.config.get('OPERATION_DELAY', 3))
            
            # 1. 填写文档编号
            if not self._fill_document_number(doc_id):
                return False
            
            # 2. 等待几秒让页面加载更多项
            logger.info("⏳ 等待页面加载更多项...")
            time.sleep(6)  # 增加等待时间
            
            # 3. 填写所属项目阶段
            if not self._fill_project_stage():
                return False
            
            # 4. 填写交付物级别
            if not self._fill_delivery_level():
                return False
            
            # 5. 填写文件内容简要
            if not self._fill_content_summary(source_file['name']):
                return False
            
            # 6. 上传文件
            if not self._upload_files(source_file, excel_file):
                return False
            
            # 7. 保存并发起评审
            if not self._save_and_start_review():
                return False
            
            logger.info(f"✅ 第一页信息填写完成: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 第一页处理失败: {str(e)}")
            return False
    
    def _scroll_to_bottom(self):
        """滚动到页面底部 - 优化版本，尝试多种滚动方式"""
        try:
            logger.info("📜 滚动到页面底部（多种方式尝试）...")
            
            # 方法1: 滚动整个页面窗口（最常用）+ 验证实际滚动
            try:
                # 记录滚动前的位置
                scroll_before = self.driver.execute_script("return window.pageYOffset;")
                
                # 执行滚动
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # 检查滚动后的位置
                scroll_after = self.driver.execute_script("return window.pageYOffset;")
                
                if scroll_after > scroll_before + 100:  # 实际滚动了超过100px
                    logger.info(f"✅ 方法1: 页面窗口滚动成功 (前:{scroll_before}px -> 后:{scroll_after}px)")
                    return True
                else:
                    logger.warning(f"⚠️ 方法1: 滚动命令执行但未实际滚动 (前:{scroll_before}px -> 后:{scroll_after}px)")
                    
            except Exception as e1:
                logger.warning(f"⚠️ 方法1失败: {str(e1)}")
            
            # 方法2: 强制滚动到超大数值（强制版本）+ 验证实际滚动
            try:
                # 记录滚动前的位置
                scroll_before = self.driver.execute_script("return window.pageYOffset;")
                
                # 执行滚动
                self.driver.execute_script("window.scrollTo(0, 999999);")
                time.sleep(2)
                
                # 检查滚动后的位置
                scroll_after = self.driver.execute_script("return window.pageYOffset;")
                
                if scroll_after > scroll_before + 100:  # 实际滚动了超过100px
                    logger.info(f"✅ 方法2: 强制滚动成功 (前:{scroll_before}px -> 后:{scroll_after}px)")
                    return True
                else:
                    logger.warning(f"⚠️ 方法2: 滚动命令执行但未实际滚动 (前:{scroll_before}px -> 后:{scroll_after}px)")
                    
            except Exception as e2:
                logger.warning(f"⚠️ 方法2失败: {str(e2)}")                    
            # 方法2.5: 专门针对Element UI页面的滚动策略
            try:
                logger.info("尝试Element UI专用滚动...")
                
                # 尝试找到实际的可滚动容器
                possible_containers = [
                    "//main[@id='main']",
                    "//div[contains(@class, 'el-main')]",
                    "//div[contains(@class, 'cus-content')]", 
                    "//section[contains(@class, 'el-container')]",
                    "//div[contains(@class, 'contanier')]",
                    "//div[contains(@class, 'el-scrollbar__wrap')]"
                ]
                
                for container_xpath in possible_containers:
                    try:
                        container = self.driver.find_element(By.XPATH, container_xpath)
                        
                        # 记录容器滚动前位置
                        scroll_before = self.driver.execute_script("return arguments[0].scrollTop;", container)
                        
                        # 执行容器滚动
                        self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", container)
                        time.sleep(2)
                        
                        # 检查容器滚动后位置
                        scroll_after = self.driver.execute_script("return arguments[0].scrollTop;", container)
                        
                        if scroll_after > scroll_before + 50:  # 容器实际滚动了
                            logger.info(f"✅ 方法2.5: Element UI容器滚动成功 ({container_xpath}) (前:{scroll_before}px -> 后:{scroll_after}px)")
                            return True
                        else:
                            logger.debug(f"容器 {container_xpath} 未滚动 (前:{scroll_before}px -> 后:{scroll_after}px)")
                    except:
                        continue
                        
                logger.warning("⚠️ 方法2.5: 所有Element UI容器都无法滚动")
            except Exception as e25:
                logger.warning(f"⚠️ 方法2.5失败: {str(e25)}")
            
            # 方法3: 滚动到保存按钮位置
            try:
                footer_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '保存')]")
                if footer_buttons:
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'end'});", footer_buttons[0])
                    time.sleep(3)
                    logger.info("✅ 方法3: 滚动到保存按钮位置成功")
                    return True
            except Exception as e3:
                logger.warning(f"⚠️ 方法3失败: {str(e3)}")
            
            # 方法4: 滚动主容器el-main（id=main）
            try:
                main_element = self.driver.find_element(By.ID, "main")
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", main_element)
                time.sleep(2)
                logger.info("✅ 方法4: el-main(id=main)容器滚动成功")
                return True
            except Exception as e4:
                logger.warning(f"⚠️ 方法4失败: {str(e4)}")
            
            # 方法5: 滚动el-main class容器
            try:
                main_elements = self.driver.find_elements(By.CLASS_NAME, "el-main")
                if main_elements:
                    self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", main_elements[0])
                    time.sleep(2)
                    logger.info("✅ 方法5: el-main class容器滚动成功")
                    return True
            except Exception as e5:
                logger.warning(f"⚠️ 方法5失败: {str(e5)}")
            
            # 方法6: 使用Page Down键连续滚动
            try:
                body = self.driver.find_element(By.TAG_NAME, "body")
                for i in range(10):  # 按10次Page Down
                    body.send_keys(Keys.PAGE_DOWN)
                    time.sleep(0.3)
                logger.info("✅ 方法6: Page Down键连续滚动成功")
                return True
            except Exception as e6:
                logger.warning(f"⚠️ 方法6失败: {str(e6)}")
            
            # 方法6.5: JavaScript强制显示所有隐藏内容并滚动
            try:
                logger.info("尝试强制显示隐藏内容并滚动...")
                
                # 强制显示所有可能隐藏的元素
                self.driver.execute_script("""
                    // 移除所有可能阻止滚动的样式
                    var allElements = document.querySelectorAll('*');
                    for(var i = 0; i < allElements.length; i++) {
                        var elem = allElements[i];
                        if(elem.style) {
                            elem.style.overflow = 'visible';
                            elem.style.height = 'auto';
                            elem.style.maxHeight = 'none';
                        }
                    }
                    
                    // 强制滚动到页面最底部
                    window.scrollTo(0, document.documentElement.scrollHeight);
                    
                    // 如果有main容器，也滚动它
                    var mainEl = document.getElementById('main');
                    if(mainEl) {
                        mainEl.scrollTop = mainEl.scrollHeight;
                    }
                """)
                
                time.sleep(3)
                logger.info("✅ 方法6.5: JavaScript强制显示并滚动成功")
                return True
            except Exception as e65:
                logger.warning(f"⚠️ 方法6.5失败: {str(e65)}")
                
            # 方法7: 滚动到文件上传区域
            try:
                upload_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'upload-box')]")
                if upload_elements:
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", upload_elements[0])
                    time.sleep(3)
                    logger.info("✅ 方法7: 滚动到文件上传区域成功")
                    return True
            except Exception as e7:
                logger.warning(f"⚠️ 方法7失败: {str(e7)}")
            
            # 方法8: 滚动表单容器form-style
            try:
                form_elements = self.driver.find_elements(By.CLASS_NAME, "form-style")
                if form_elements:
                    self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", form_elements[0])
                    time.sleep(2)
                    logger.info("✅ 方法8: form-style容器滚动成功")
                    return True
            except Exception as e8:
                logger.warning(f"⚠️ 方法8失败: {str(e8)}")
            
            # 方法9: 滚动contanier容器
            try:
                container_elements = self.driver.find_elements(By.CLASS_NAME, "contanier")
                if container_elements:
                    self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", container_elements[0])
                    time.sleep(2)
                    logger.info("✅ 方法9: contanier容器滚动成功")
                    return True
            except Exception as e9:
                logger.warning(f"⚠️ 方法9失败: {str(e9)}")
            
            # 方法10: 滚动section容器（根据HTML结构）
            try:
                section_elements = self.driver.find_elements(By.TAG_NAME, "section")
                if section_elements:
                    self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", section_elements[0])
                    time.sleep(2)
                    logger.info("✅ 方法10: section容器滚动成功")
                    return True
            except Exception as e10:
                logger.warning(f"⚠️ 方法10失败: {str(e10)}")
            
            logger.warning("⚠️ 所有滚动方法都失败了，但继续执行...")
            return False
            
        except Exception as e:
            logger.warning(f"⚠️ 滚动功能整体失败: {str(e)}，但继续执行...")
            return False
    
    def _scroll_to_upload_area(self):
        """滚动到文件上传区域"""
        try:
            logger.info("📜 滚动到文件上传区域...")
            
            # 方法1: 滚动到文件上传区域
            try:
                upload_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'upload-box')]")
                if upload_elements:
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", upload_elements[0])
                    time.sleep(3)
                    logger.info("✅ 滚动到文件上传区域成功")
                    return True
            except Exception as e1:
                logger.warning(f"⚠️ 方法1失败: {str(e1)}")
            
            # 方法2: 滚动到源文件标签
            try:
                source_file_elements = self.driver.find_elements(By.XPATH, "//div[contains(text(), '源文件')]")
                if source_file_elements:
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", source_file_elements[0])
                    time.sleep(3)
                    logger.info("✅ 滚动到源文件标签成功")
                    return True
            except Exception as e2:
                logger.warning(f"⚠️ 方法2失败: {str(e2)}")
            
            # 方法3: 滚动到PDF文件标签
            try:
                pdf_file_elements = self.driver.find_elements(By.XPATH, "//div[contains(text(), 'PDF文件')]")
                if pdf_file_elements:
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", pdf_file_elements[0])
                    time.sleep(3)
                    logger.info("✅ 滚动到PDF文件标签成功")
                    return True
            except Exception as e3:
                logger.warning(f"⚠️ 方法3失败: {str(e3)}")
            
            # 方法4: 使用之前的强制滚动方法
            logger.info("使用强制滚动方法...")
            return self._scroll_to_bottom()
            
        except Exception as e:
            logger.warning(f"⚠️ 滚动到上传区域失败: {str(e)}")
            return self._scroll_to_bottom()  # 降级到通用滚动
    
    def _fill_document_number(self, doc_id):
        """填写文档编号"""
        try:
            logger.info("📝 填写文档编号...")
            
            # 基于实际页面结构优化的选择器 - 保留前3个，第3个成功
            doc_number_selectors = [
                "//div[1]/div[2]/div[1]/div/div/div[1]/input",
                "//div[contains(@class, 'el-select')]//input[contains(@class, 'el-select__input')]",
                "//label[contains(text(), '文档编号')]/following-sibling::div//input"  # 第3个成功的选择器
            ]
            
            for i, selector in enumerate(doc_number_selectors):
                try:
                    logger.info(f"尝试文档编号选择器 {i+1}/{len(doc_number_selectors)}")
                    doc_number_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    doc_number_field.click()
                    time.sleep(2)
                    doc_number_field.clear()
                    doc_number_field.send_keys(doc_id)
                    logger.info(f"✅ 已输入文档编号: {doc_id}")
                    time.sleep(3)  # 等待下拉选项出现
                    
                    # 尝试点击下拉选项
                    dropdown_selectors = [
                        f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{doc_id}')]",
                        f"//span[contains(text(), '{doc_id}')]/parent::li",
                        f"//ul[contains(@class, 'el-select-dropdown__list')]//li[contains(text(), '{doc_id}')]"
                    ]
                    
                    dropdown_clicked = False
                    for dropdown_selector in dropdown_selectors:
                        try:
                            dropdown_item = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, dropdown_selector)
                            ))
                            dropdown_item.click()
                            logger.info("✅ 成功点击文档编号下拉选项")
                            dropdown_clicked = True
                            time.sleep(2)
                            break
                        except:
                            continue
                    
                    if not dropdown_clicked:
                        doc_number_field.send_keys(Keys.ENTER)
                        time.sleep(2)
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"文档编号选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文档编号填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写文档编号异常: {str(e)}")
            return False
    
    def _fill_project_stage(self):
        """填写所属项目阶段"""
        try:
            logger.info("📝 填写所属项目阶段...")
            
            # 使用选择器2
            stage_selectors = [
                "//div[2]/div[2]/div[1]/div/div/div[1]/input",
                "//div[contains(@class, 'el-form-item') and contains(.//label, '阶段')]//input"  # 选择器2成功
            ]
            
            stage_value = "B版"
            
            for i, selector in enumerate(stage_selectors):
                try:
                    logger.info(f"尝试项目阶段选择器 {i+1}/{len(stage_selectors)}")
                    stage_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    stage_field.click()
                    time.sleep(2)
                    stage_field.clear()
                    stage_field.send_keys(stage_value)
                    time.sleep(3)  # 等待下拉选项出现
                    
                    # 尝试选择下拉选项 - B版
                    stage_option_selectors = [
                        f"//span[contains(text(), '整车项目阶段-产品详细设计阶段-B版')]",
                        f"//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '{stage_value}')]",
                        f"//li[contains(text(), 'B版')]",
                        f"//ul[contains(@class, 'el-select-dropdown__list')]//li[contains(text(), 'B版')]"
                    ]
                    
                    option_clicked = False
                    for option_selector in stage_option_selectors:
                        try:
                            stage_option = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, option_selector)
                            ))
                            stage_option.click()
                            logger.info(f"✅ 已选择项目阶段: {stage_value}")
                            option_clicked = True
                            time.sleep(2)
                            break
                        except:
                            continue
                    
                    if not option_clicked:
                        stage_field.send_keys(Keys.ENTER)
                        logger.info(f"✅ 通过回车确认项目阶段: {stage_value}")
                        time.sleep(2)
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"项目阶段选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 项目阶段填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写项目阶段异常: {str(e)}")
            return False
    
    def _fill_delivery_level(self):
        """填写交付物级别"""
        try:
            logger.info("📝 填写交付物级别...")
            
            # 使用选择器3
            dropdown_selectors = [
                "//div[3]/div[2]/div[1]/div/div/div[2]/i/svg",
                "//div[contains(@class, 'el-select__suffix')]//i//svg",
                "//div[contains(@class, 'el-form-item') and contains(.//label, '级别')]//i[contains(@class, 'el-select__caret')]"  # 选择器3成功
            ]
            
            # 先点击下拉按钮
            dropdown_clicked = False
            for i, selector in enumerate(dropdown_selectors):
                try:
                    logger.info(f"尝试交付物级别下拉按钮选择器 {i+1}/{len(dropdown_selectors)}")
                    dropdown_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    dropdown_btn.click()
                    logger.info("✅ 成功点击交付物级别下拉按钮")
                    dropdown_clicked = True
                    time.sleep(2)  # 等待下拉选项出现
                    break
                except Exception as e:
                    logger.warning(f"交付物级别下拉按钮选择器 {i+1} 失败: {str(e)}")
                    continue
                    
            if not dropdown_clicked:
                logger.error("❌ 无法点击交付物级别下拉按钮")
                return False
            
            # 选择"项目级"
            level_selectors = [
                # 基于下拉选项的选择器
                "//li[contains(@class, 'el-select-dropdown__item') and contains(text(), '项目级')]",
                "//span[contains(text(), '项目级')]/parent::li",
                "//li[contains(text(), '项目级')]",
                "//ul[contains(@class, 'el-select-dropdown__list')]//li[contains(text(), '项目级')]"
            ]
            
            for i, selector in enumerate(level_selectors):
                try:
                    logger.info(f"尝试项目级选择器 {i+1}/{len(level_selectors)}")
                    level_option = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    level_option.click()
                    logger.info("✅ 已选择交付物级别: 项目级")
                    time.sleep(2)
                    
                    return True
                except Exception as e:
                    logger.warning(f"项目级选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 无法选择项目级选项")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写交付物级别异常: {str(e)}")
            return False
    
    def _fill_content_summary(self, filename):
        """填写文件内容简要"""
        try:
            logger.info("📝 填写文件内容简要...")
            
            # 从文件名中提取文档名称（去掉扩展名）
            doc_name = Path(filename).stem
            
            # 使用选择器2
            summary_selectors = [
                "//div[4]/div[2]/div/div/textarea",
                "//textarea[contains(@id, 'el-id-') and contains(@placeholder, '请输入内容')]"  # 选择器2成功
            ]
            
            for i, selector in enumerate(summary_selectors):
                try:
                    logger.info(f"尝试内容简要选择器 {i+1}/{len(summary_selectors)}")
                    summary_field = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    summary_field.click()
                    time.sleep(2)
                    summary_field.clear()
                    summary_field.send_keys(doc_name)
                    logger.info(f"✅ 已输入文件内容简要: {doc_name}")
                    time.sleep(2)
                    
                    return True
                    
                except Exception as e:
                    logger.warning(f"内容简要选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 文件内容简要填写失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 填写文件内容简要异常: {str(e)}")
            return False
    
    def _upload_files(self, source_file, excel_file=None):
        """上传文件"""
        try:
            logger.info("📁 开始上传文件...")
            
            # 在文件上传前，先滚动到文件上传区域
            logger.info("📜 准备上传文件，先滚动到文件上传区域...")
            self._scroll_to_upload_area()
            
            # 1. 上传源文件
            if not self._upload_source_file(source_file):
                return False
            
            # 2. 删除自动转换的PDF（如果存在）
            self._delete_auto_converted_pdf()
            
            # 3. 上传PDF文件（如果有对应的PDF）
            if excel_file and excel_file['path'].suffix.lower() == '.pdf':
                if not self._upload_pdf_file(excel_file):
                    return False
            
            logger.info("✅ 文件上传完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 上传文件异常: {str(e)}")
            return False
    
    def _upload_source_file(self, source_file):
        """上传源文件"""
        try:
            logger.info("📄 上传源文件...")
            
            # 使用选择器2
            upload_selectors = [
                "//div[5]/div[2]/div[1]/div/div[1]/div[1]/input",
                "//div[contains(@class, 'el-form-item') and contains(.//div, '源文件')]//input[@type='file']"  # 选择器2成功
            ]
            
            # 确保使用绝对路径
            file_path = str(source_file['path'].resolve())
            
            for i, selector in enumerate(upload_selectors):
                try:
                    logger.info(f"尝试源文件上传选择器 {i+1}/{len(upload_selectors)}")
                    upload_input = self.driver.find_element(By.XPATH, selector)
                    
                    # 上传文件
                    upload_input.send_keys(file_path)
                    logger.info(f"✅ 已上传源文件: {source_file['name']} (路径: {file_path})")
                    time.sleep(5)  # 等待文件上传完成
                    return True
                    
                except Exception as e:
                    logger.warning(f"源文件上传选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 源文件上传失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 上传源文件异常: {str(e)}")
            return False
    
    def _delete_auto_converted_pdf(self):
        """删除自动转换的PDF文件"""
        try:
            logger.info("🗑️ 尝试删除自动转换的PDF...")
            
            # 等待系统自动转换PDF
            time.sleep(3)
            
            # 基于上传审批dvp.json和deletepdf.html分析的精确删除选择器
            pdf_delete_selectors = [
                # 方法1: 基于JSON中成功的删除路径 - SVG path元素点击
                "//div[contains(@class, 'el-form-item') and .//div[contains(text(), 'PDF文件')]]//div[contains(@class, 'file-item')]//button//span//i//svg//path",
                
                # 方法2: 基于deletepdf.html结构 - 精确定位PDF文件的删除按钮
                "//div[contains(@class, 'file-item') and .//div[contains(@class, 'file-title') and contains(text(), '.pdf')]]//button[contains(@class, 'el-button--danger') and contains(@class, 'is-link')]",
                
                # 方法3: 基于JSON中的CSS选择器路径改写为XPath
                "//div[contains(@class, 'file-item')]//button[contains(@class, 'el-button--danger')]//span//i//svg//path",
                
                # 方法4: 更精准的SVG删除图标定位
                "//div[contains(@class, 'file-title') and contains(text(), '.pdf')]/following-sibling::div//svg[contains(@viewBox, '0 0 1024 1024')]//path[contains(@d, 'M352 192V95.936')]",
                
                # 方法5: 直接定位删除按钮元素
                "//div[contains(@class, 'file-item') and .//div[contains(text(), '.pdf')]]//button[contains(@class, 'el-button--danger')]",
                
                # 方法6: 基于JSON中el-collapse-content-1452的结构模式
                "//div[contains(@id, 'el-collapse-content')]//div[contains(@class, 'file-item')]//button//span//i//svg//path",
                
                # 方法7: 更灵活的PDF文件删除按钮定位
                "//div[contains(@class, 'file-item')]//div[contains(@class, 'file-title') and contains(text(), '.pdf')]/parent::div//button",
                
                # 方法8: 基于完整的删除图标SVG结构
                "//button[contains(@class, 'el-button--danger')]//svg[contains(@xmlns, 'http://www.w3.org/2000/svg')]//path[contains(@fill, 'currentColor')]"
            ]
            
            # 首先检查是否存在PDF文件
            pdf_files = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'file-title') and contains(text(), '.pdf')]")
            
            if not pdf_files:
                logger.info("ℹ️ 未发现PDF文件，无需删除")
                return
                
            logger.info(f"📋 发现 {len(pdf_files)} 个PDF文件，尝试删除...")
            
            # 尝试多种滚动方式确保删除按钮可见
            logger.info("📜 先滚动确保PDF删除按钮可见...")
            try:
                # 滚动到PDF文件区域
                pdf_section = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'el-form-item') and .//div[contains(text(), 'PDF文件')]]")
                if pdf_section:
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", pdf_section[0])
                    time.sleep(2)
            except:
                pass
            
            for i, selector in enumerate(pdf_delete_selectors):
                try:
                    logger.info(f"尝试PDF删除按钮选择器 {i+1}/{len(pdf_delete_selectors)}")
                    delete_elements = self.driver.find_elements(By.XPATH, selector)
                    
                    if delete_elements:
                        logger.info(f"找到 {len(delete_elements)} 个PDF删除元素")
                        for j, element in enumerate(delete_elements):
                            try:
                                # 多种方式确保元素可见和可点击
                                try:
                                    # 滚动到元素
                                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                                    time.sleep(1)
                                except:
                                    pass
                                
                                # 尝试多种点击方式
                                clicked = False
                                
                                # 方式1: JavaScript点击（对不可见元素最有效）
                                try:
                                    self.driver.execute_script("arguments[0].click();", element)
                                    logger.info(f"✅ 已删除第 {j+1} 个PDF文件（JS点击）")
                                    clicked = True
                                    time.sleep(2)
                                except Exception as js_e:
                                    logger.debug(f"JS点击失败: {str(js_e)}")
                                
                                # 方式2: 普通点击
                                if not clicked:
                                    try:
                                        element.click()
                                        logger.info(f"✅ 已删除第 {j+1} 个PDF文件（普通点击）")
                                        clicked = True
                                        time.sleep(2)
                                    except Exception as normal_e:
                                        logger.debug(f"普通点击失败: {str(normal_e)}")
                                
                                # 方式3: ActionChains点击
                                if not clicked:
                                    try:
                                        from selenium.webdriver.common.action_chains import ActionChains
                                        ActionChains(self.driver).move_to_element(element).click().perform()
                                        logger.info(f"✅ 已删除第 {j+1} 个PDF文件（ActionChains点击）")
                                        clicked = True
                                        time.sleep(2)
                                    except Exception as action_e:
                                        logger.debug(f"ActionChains点击失败: {str(action_e)}")
                                
                                if clicked:
                                    # 验证删除是否成功
                                    time.sleep(2)
                                    remaining_pdfs = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'file-title') and contains(text(), '.pdf')]")
                                    if len(remaining_pdfs) < len(pdf_files):
                                        logger.info("✅ PDF删除成功")
                                        return
                                    else:
                                        logger.warning("⚠️ 点击了删除按钮但PDF仍存在，继续尝试")
                                        continue
                                else:
                                    logger.warning(f"第 {j+1} 个PDF删除元素所有点击方式都失败")
                                    continue
                                            
                            except Exception as element_e:
                                logger.warning(f"处理第 {j+1} 个PDF删除元素失败: {str(element_e)}")
                                continue
                                
                except Exception as find_e:
                    logger.warning(f"PDF删除选择器 {i+1} 查找失败: {str(find_e)}")
                    continue
            
            logger.info("ℹ️ 尝试了所有删除方式，PDF可能未能删除")
                
        except Exception as e:
            logger.warning(f"⚠️ 删除自动转换PDF失败: {str(e)}")
    
    def _upload_pdf_file(self, pdf_file):
        """上传PDF文件"""
        try:
            logger.info("📋 上传PDF文件...")
            
            # 使用选择器2
            pdf_upload_selectors = [
                "//div[6]/div[2]/div[1]/div/div[1]/div[1]/input",
                "//div[contains(@class, 'el-form-item') and contains(.//div, 'PDF文件')]//input[@type='file']"  # 选择器2成功
            ]
            
            # 确保使用绝对路径
            file_path = str(pdf_file['path'].resolve())
            
            for i, selector in enumerate(pdf_upload_selectors):
                try:
                    logger.info(f"尝试PDF上传选择器 {i+1}/{len(pdf_upload_selectors)}")
                    pdf_upload_input = self.driver.find_element(By.XPATH, selector)
                    
                    # 上传文件
                    pdf_upload_input.send_keys(file_path)
                    logger.info(f"✅ 已上传PDF文件: {pdf_file['name']} (路径: {file_path})")
                    time.sleep(5)  # 等待文件上传完成
                    return True
                    
                except Exception as e:
                    logger.warning(f"PDF上传选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ PDF文件上传失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 上传PDF文件异常: {str(e)}")
            return False
    
    def _save_and_start_review(self):
        """保存并发起评审"""
        try:
            logger.info("💾 保存并发起评审...")
            
            # 使用选择器2
            save_selectors = [
                "//div[contains(@style, 'width: 100px')]//span[contains(text(), '保存并发起评审')]/parent::button",
                "//button[contains(@class, 'el-button') and contains(.//span, '保存并发起评审')]"  # 选择器2成功
            ]
            
            for i, selector in enumerate(save_selectors):
                try:
                    logger.info(f"尝试保存按钮选择器 {i+1}/{len(save_selectors)}")
                    save_btn = self.wait.until(EC.element_to_be_clickable(
                        (By.XPATH, selector)
                    ))
                    save_btn.click()
                    logger.info("✅ 已点击保存并发起评审按钮")
                    time.sleep(5)  # 等待页面跳转
                    return True
                    
                except Exception as e:
                    logger.warning(f"保存按钮选择器 {i+1} 失败: {str(e)}")
                    continue
            
            logger.error("❌ 保存并发起评审失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 保存并发起评审异常: {str(e)}")
            return False
