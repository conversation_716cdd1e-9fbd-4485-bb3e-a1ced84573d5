# File_Status表结构修正总结

## 修正内容

根据用户要求，File_Status表的三个关键字段应该存储：

### 1. file_name（文件名）
- **存储内容**: 带车型代号的完整文件名（不含扩展名）
- **示例**: `TEST88项目VSE软件设计验证计划（DVP）`
- **修正前**: 只存储核心部分 `VSE软件设计验证计划（DVP）`
- **修正后**: 存储完整文件名 `TEST88项目VSE软件设计验证计划（DVP）`

### 2. code（文件编号）
- **存储内容**: 申请到的文件编号
- **示例**: `20241224001`
- **初始状态**: 空值/NaN（待申请）
- **申请后**: 具体编号

### 3. numbered_file（带编号的文件名）
- **存储内容**: 编号-完整文件名的格式
- **示例**: `20241224001-TEST88项目VSE软件设计验证计划（DVP）`
- **格式**: `{编号}-{车型代号}项目{其他内容}`
- **用途**: 用于文件重命名和管理

## 代码修正

### 1. update_file_status方法
```python
# 修正前：存储标准化名称
standard_name = name_without_ext.replace(f"{vehicle_code}项目", "")

# 修正后：存储完整文件名
new_row = {
    'file_name': name_without_ext,  # 完整文件名（含车型代号）
    'code': '',                     # 文件编号（待申请）
    'numbered_file': '',            # 带编号的文件名（待生成）
    'is_fillin': 'N',
    'is_upload': 'N'
}
```

### 2. update_file_with_id方法
```python
# 修正前：使用下划线连接
df.loc[mask, 'numbered_file'] = f"{file_id}_{file_name}"

# 修正后：使用短横线连接
df.loc[mask, 'numbered_file'] = f"{file_id}-{file_name}"
```

## 测试结果

### ✅ File_Status表内容
```
                  file_name          code                numbered_file is_fillin is_upload
0  TEST88项目VSE软件设计验证计划（DVP）  20241224001  20241224001-TEST88项目VSE软件设计验证计划（DVP）         N         N
1     TEST88项目VSE软件开发匹配测试计划           NaN                            NaN         N         N
```

### ✅ 申请编号文件列表
```
需要申请编号的文件数量: 2
  - TEST88项目VSE软件设计验证计划（DVP） (车型: TEST88)
  - TEST88项目VSE软件开发匹配测试计划 (车型: TEST88)
```

### ✅ 编号更新测试
- 文件: `TEST88项目VSE软件设计验证计划（DVP）`
- 编号: `20241224001`
- 生成的numbered_file: `20241224001-TEST88项目VSE软件设计验证计划（DVP）`

## 工作流程

1. **复制模板文件** → 生成带车型代号的文件名
2. **更新File_Status表** → 存储完整文件名信息
3. **申请编号** → 获取文件列表，申请编号
4. **更新编号** → 更新code和numbered_file字段
5. **文件管理** → 使用numbered_file进行文件重命名和管理

现在File_Status表的结构完全符合用户要求，支持完整的文件管理流程！
