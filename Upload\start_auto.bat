@echo off
chcp 65001 >nul
title 上传审批自动化

echo ================================================
echo          上传审批自动化程序
echo ================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：Python未安装或不在PATH中
    echo 请先安装Python并确保可以在命令行中运行
    timeout /t 10 /nobreak >nul
    exit /b 1
)

REM 检查配置文件
if not exist "config.py" (
    echo ❌ 错误：config.py配置文件不存在
    echo 请确保配置文件存在于当前目录
    timeout /t 10 /nobreak >nul
    exit /b 1
)

REM 检查静默模式配置
python -c "import config; print('静默模式:', 'ON' if getattr(config, 'HEADLESS_MODE', False) else 'OFF')" 2>nul
if errorlevel 1 (
    echo ⚠️ 警告：无法读取配置文件
)

echo.
echo 使用说明：
echo 1. 请确保已配置config.py中的用户名密码（可选）
echo 2. 请将要处理的文档放入Final_Approval_Documents文件夹
echo 3. 请编辑Data文件夹中的Excel文件，填入正确的人员信息
echo 4. 可在config.py中开启静默模式（HEADLESS_MODE = True）实现后台运行
echo.

set /p choice="是否现在运行上传审批程序？(y/n): "
if /i "%choice%"=="y" (
    echo 🚀 启动程序...
    echo.
    python run.py
    echo.
    echo ✅ 程序执行完成
) else (
    echo 📋 程序已准备就绪，您可以稍后运行：
    echo    python run.py
)

echo.
echo 🔄 脚本执行完成
timeout /t 3 /nobreak >nul
