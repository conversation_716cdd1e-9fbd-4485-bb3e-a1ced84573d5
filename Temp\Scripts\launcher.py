import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    missing_deps = []
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        from PyQt5.QtWidgets import QApplication
    except ImportError:
        missing_deps.append("PyQt5")
    
    try:
        import selenium
    except ImportError:
        missing_deps.append("selenium")
    
    try:
        import openpyxl
    except ImportError:
        missing_deps.append("openpyxl")
    
    try:
        from docx import Document
    except ImportError:
        missing_deps.append("python-docx")
    
    return missing_deps

def install_dependencies():
    """安装缺失的依赖"""
    import subprocess
    
    missing = check_dependencies()
    if not missing:
        print("所有依赖已安装完成")
        return True
    
    print(f"缺失依赖: {', '.join(missing)}")
    print("正在尝试安装...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("自动安装失败，请手动运行: pip install -r requirements.txt")
        return False

def main():
    print("车型文件管理系统启动器")
    print("=" * 30)
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"检测到缺失依赖: {', '.join(missing)}")
        response = input("是否尝试自动安装？(y/n): ")
        if response.lower() == 'y':
            if not install_dependencies():
                return
        else:
            print("请手动安装依赖后再运行程序")
            return    # 启动主程序
    try:
        print("正在启动主程序...")
        # 将父目录添加到Python路径
        parent_dir = Path(__file__).parent.parent
        sys.path.insert(0, str(parent_dir))
        import main_gui_final
        main_gui_final.main()
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查错误信息并确保所有依赖已正确安装")

if __name__ == "__main__":
    main()
