import os
import logging
from datetime import datetime
from config_loader import load_config
from excel_processor import find_target_files, process_file

# Configure logging
LOG_DIR = "logs"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, f"ppl_filler_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def main():
    """Main function to execute the PPL file filling process."""
    try:
        # Compute absolute path to config.yaml in parent directory (ppl/)
        config_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "config.yaml"))
        # Load configuration
        config = load_config(config_path)

        # Compute absolute path to output folder (ppl/outputs/)
        output_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", config['dirs']['output']))
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Find target files
        files = find_target_files(config['dirs']['input'], config['file_pattern'])

        # Process each file
        for file in files:
            process_file(file, output_dir, config)

        logger.info("All files processed successfully")

    except Exception as e:
        logger.error(f"Program execution failed: {e}")
        raise


if __name__ == "__main__":
    main()