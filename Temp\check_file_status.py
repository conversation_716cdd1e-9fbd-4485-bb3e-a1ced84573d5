#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查File_Status表内容
"""

import sys
import os
from pathlib import Path
import pandas as pd

def check_file_status():
    """检查File_Status表内容"""
    base_path = Path(__file__).parent
    vehicle_code = "QYHB"
    
    excel_file = base_path / "Vehicles" / vehicle_code / "information" / f"{vehicle_code}_Fill_Template_Data.xlsx"
    
    if excel_file.exists():
        print(f"检查文件: {excel_file}")
        
        # 读取File_Status表
        df = pd.read_excel(excel_file, sheet_name="File_Status")
        print(f"\nFile_Status表内容:")
        print(df)
        
        print(f"\n各列的数据类型:")
        print(df.dtypes)
        
        # 检查哪些文件没有编号
        no_code_files = df[df['code'].isna() | (df['code'] == '')]
        print(f"\n没有编号的文件:")
        print(no_code_files)
        
    else:
        print(f"文件不存在: {excel_file}")

if __name__ == "__main__":
    check_file_status()
