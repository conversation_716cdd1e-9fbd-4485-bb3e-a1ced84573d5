"""
语法检查脚本 - 验证apply_id_automation.py是否有语法错误
"""

import ast
import sys
from pathlib import Path

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print(f"✅ {file_path.name} 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path.name} 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path.name} 检查失败: {str(e)}")
        return False

def main():
    print("="*50)
    print("           Python 语法检查工具")
    print("="*50)
    
    files_to_check = [
        "apply_id_automation.py",
        "file_number_filler.py", 
        "run_apply_id.py"
    ]
    
    all_passed = True
    for filename in files_to_check:
        file_path = Path(filename)
        if file_path.exists():
            if not check_syntax(file_path):
                all_passed = False
        else:
            print(f"⚠️ 文件不存在: {filename}")
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有文件语法检查通过！")
    else:
        print("❌ 发现语法错误，请修复后重试")
    print("="*50)

if __name__ == "__main__":
    main()
