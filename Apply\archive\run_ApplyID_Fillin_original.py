import tkinter as tk
from tkinter import messagebox, font, scrolledtext
from tkinter import ttk
import os
import shutil
import pandas as pd
import subprocess
from pathlib import Path
import sys
import logging
import yaml
from datetime import datetime
import win32api
import win32con


class App:
    def __init__(self, root):
        self.root = root
        self.root.title("文件处理程序")
        self.root.minsize(900, 620)
        self.base_path = Path(__file__).parent
        self.templates_path = self.base_path / "Templates"
        self.update_templates_path = self.base_path / "Update_Templates"
        self.output_path = self.base_path / "output"
        self.apply_id_path = self.base_path / "ApplyID"
        self.fillin_path = self.base_path / "Fillin"
        self.config_path = self.apply_id_path / "config" / "config.yaml"
        self.log_dir = self.base_path / "logs" / "AppID_Fillin"
        self.operation_log_file = self.log_dir / "operation_history.log"
        self.process_log_file = self.log_dir / "process.log"
        self.subprocess_log_file = self.log_dir / "subprocess.log"

        self.log_dir.mkdir(parents=True, exist_ok=True)
        logging.basicConfig(
            level=logging.DEBUG,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(self.process_log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger()

        self.file_types = [
            "DVP", "PPL", "FN_IMU", "FN_VCU", "FN_IPB", "FN_ESP_BWA",
            "FN_EPS", "FN_EPSA", "FN_EPB", "FN_DISUS_A", "FN_DISUS_C",
            "FN_DISUS_P", "FN_DISUS_X", "FN_DISUS_M", "FN_域控"
        ]

        self.file_patterns = {
            "DVP": ("软件设计验证计划", ".xlsx"),
            "PPL": ("软件开发匹配测试计划", ".xlsx"),
            "FN_IMU": ("气囊 * 接口定义通知单", ".docx"),
            "FN_VCU": ("VCU * 接口定义通知单", ".docx"),
            "FN_IPB": ("IPB * 接口定义通知单", ".docx"),
            "FN_ESP_BWA": ("ESP * 接口定义通知单", ".docx"),
            "FN_EPS": ("EPS * 接口定义通知单", ".docx"),
            "FN_EPSA": ("EPSA * 接口定义通知单", ".docx"),
            "FN_EPB": ("EPB * 接口定义通知单", ".docx"),
            "FN_DISUS_A": ("DiSus-A * 接口定义通知单", ".docx"),
            "FN_DISUS_C": ("DiSus-C * 接口定义通知单", ".docx"),
            "FN_DISUS_P": ("DiSus-P * 接口定义通知单", ".docx"),
            "FN_DISUS_X": ("DiSus-X * 接口定义通知单", ".docx"),
            "FN_DISUS_M": ("DiSus-M * 接口定义通知单", ".docx"),
            "FN_域控": ("域 * 接口定义通知单", ".docx")
        }

        self.keywords = [
            "软件设计验证计划", "软件开发匹配测试计划", "气囊", "VCU", "IPB", "ESP",
            "EPS", "EPSA", "EPB", "DiSus-A", "DiSus-C", "DiSus-P", "DiSus-X", "DiSus-M", "域"
        ]

        self.select_all_state = False
        self.create_widgets()
        self.vehicle_code = self.get_vehicle_code()
        self.load_credentials()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动大写锁定检查
        self.check_caps_lock()

    def check_caps_lock(self):
        """检查大写锁定状态"""
        try:
            caps_state = win32api.GetKeyState(win32con.VK_CAPITAL) & 0x0001
            if hasattr(self, 'caps_label'):
                self.caps_label.config(text="大写锁定开启" if caps_state else "")
        except:
            # 如果win32api不可用，隐藏大写锁定提示
            if hasattr(self, 'caps_label'):
                self.caps_label.config(text="")
        # 每100毫秒检查一次
        self.root.after(100, self.check_caps_lock)

    def get_vehicle_code(self):
        excel_path = self.base_path / "Fill_Template_Data.xlsx"
        config_path = self.apply_id_path / "config" / "config.yaml"
        try:
            df = pd.read_excel(excel_path, sheet_name="Sheet1")
            vehicle_code = df.loc[df['角色'] == '车型代号', 'people'].iloc[0]
            self.logger.info(f"成功读取车型代号: {vehicle_code}")

            # 更新 config.yaml 中的 project_code
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            config['settings']['project_code'] = vehicle_code
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, allow_unicode=True)
            self.logger.info(f"成功更新 config.yaml 中的 project_code 为: {vehicle_code}")

            return vehicle_code
        except Exception as e:
            self.logger.error(f"读取车型代号或更新 config.yaml 失败: {str(e)}")
            messagebox.showerror("错误", f"读取车型代号或更新 config.yaml 失败: {str(e)}")
            return None

    def load_credentials(self):
        """从config.yaml加载用户名和密码"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            credentials = config.get('credentials', {})
            username = credentials.get('username', '')
            password = credentials.get('password', '')
            save_password = credentials.get('save_password', False)

            self.username_entry.delete(0, tk.END)
            self.password_entry.delete(0, tk.END)
            self.username_entry.insert(0, username)
            self.password_entry.insert(0, password)
            self.save_password_var.set(save_password)

            self.logger.info("成功加载用户凭据")
        except Exception as e:
            self.logger.error(f"加载用户凭据失败: {str(e)}")

    def save_credentials(self):
        """保存用户名和密码到config.yaml"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        save_password = self.save_password_var.get()

        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            if 'credentials' not in config:
                config['credentials'] = {}

            config['credentials']['username'] = username
            config['credentials']['password'] = password
            config['credentials']['save_password'] = save_password

            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, allow_unicode=True)

            messagebox.showinfo("成功", "OA账号设置已确认")
            self.logger.info("用户凭据保存成功")
        except Exception as e:
            self.logger.error(f"保存用户凭据失败: {str(e)}")
            messagebox.showerror("错误", f"保存用户凭据失败: {str(e)}")

    def clear_credentials(self):
        """清空用户名和密码"""
        self.username_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.save_password_var.set(False)
        self.save_credentials()

    def on_closing(self):
        """窗口关闭时的处理"""
        if not self.save_password_var.get():
            # 如果没有勾选保存密码，则清空密码
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                if 'credentials' in config:
                    config['credentials']['password'] = ''

                    with open(self.config_path, 'w', encoding='utf-8') as f:
                        yaml.safe_dump(config, f, allow_unicode=True)

                    self.logger.info("程序关闭时已清空密码")
            except Exception as e:
                self.logger.error(f"清空密码失败: {str(e)}")

        self.root.destroy()

    def update_config_yaml(self, vehicle_code):
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            config['settings']['project_code'] = vehicle_code
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, allow_unicode=True)
            self.logger.info(f"成功更新config.yaml中的project_code为: {vehicle_code}")
            return True
        except Exception as e:
            self.logger.error(f"更新config.yaml失败: {str(e)}")
            messagebox.showerror("错误", f"更新config.yaml失败: {str(e)}")
            return False

    def create_widgets(self):
        default_font = font.Font(family="Microsoft YaHei", size=11)
        small_font = font.Font(family="Microsoft YaHei", size=10)
        button_font = font.Font(family="Microsoft YaHei", size=11)

        style = ttk.Style()
        style.configure("TButton", font=small_font, padding=6)
        style.configure("Confirm.TButton", font=button_font, padding=8)

        # 操作类型选择区域
        operation_frame = ttk.LabelFrame(self.root, text="操作类型", padding="10")
        operation_frame.grid(row=0, column=0, columnspan=3, padx=10, pady=5, sticky="ew")

        self.apply_fill_var = tk.BooleanVar()
        self.content_fill_var = tk.BooleanVar()

        tk.Checkbutton(operation_frame, text="申请并填充编号", variable=self.apply_fill_var, font=default_font).grid(
            row=0, column=0, padx=5, pady=3, sticky="w")
        tk.Checkbutton(operation_frame, text="填充文件内容", variable=self.content_fill_var, font=default_font).grid(
            row=1, column=0, padx=5, pady=3, sticky="w")

        self.op_select_button = ttk.Button(operation_frame, text="全选/全取消", command=self.toggle_all_ops, width=12)
        self.op_select_button.grid(row=0, column=1, rowspan=2, padx=20, pady=5)

        # 文件类型选择区域
        file_frame = ttk.LabelFrame(self.root, text="文件类型", padding="10")
        file_frame.grid(row=0, column=3, columnspan=4, padx=10, pady=5, sticky="ew")

        self.file_vars = {file_type: tk.BooleanVar() for file_type in self.file_types}
        for i, file_type in enumerate(self.file_types):
            row = i // 4
            col = i % 4
            tk.Checkbutton(file_frame, text=file_type, variable=self.file_vars[file_type], font=small_font).grid(
                row=row, column=col, padx=5, pady=2, sticky="w")

        self.file_select_button = ttk.Button(file_frame, text="全选/全取消", command=self.toggle_all_files, width=12)
        self.file_select_button.grid(row=4, column=0, columnspan=4, pady=5)

        # 创建包含OA账号设置和操作日志的框架
        main_info_frame = ttk.Frame(self.root)
        main_info_frame.grid(row=1, column=0, columnspan=7, padx=10, pady=5, sticky="ew")

        # OA账号设置区域（左边）
        credential_frame = ttk.LabelFrame(main_info_frame, text="OA账号设置", padding="10")
        credential_frame.grid(row=0, column=0, padx=(0, 5), pady=0, sticky="ew")

        ttk.Label(credential_frame, text="用户名:", font=default_font).grid(row=0, column=0, sticky="e", padx=5)
        self.username_entry = ttk.Entry(credential_frame, font=default_font, width=20)
        self.username_entry.grid(row=0, column=1, padx=5, pady=3, sticky="w")

        ttk.Label(credential_frame, text="密码:", font=default_font).grid(row=1, column=0, sticky="e", padx=5)
        self.password_entry = ttk.Entry(credential_frame, show="*", font=default_font, width=20)
        self.password_entry.grid(row=1, column=1, padx=5, pady=3, sticky="w")

        # 添加大写锁定提示标签
        self.caps_label = ttk.Label(credential_frame, text="", foreground="red", font=("Microsoft YaHei", 9))
        self.caps_label.grid(row=1, column=2, padx=5, sticky="w")

        # 保存密码选项
        self.save_password_var = tk.BooleanVar()
        tk.Checkbutton(credential_frame, text="保存密码", variable=self.save_password_var, font=small_font).grid(row=2,
                                                                                                                 column=0,
                                                                                                                 columnspan=3,
                                                                                                                 padx=5,
                                                                                                                 pady=3,
                                                                                                                 sticky="w")

        # 凭据操作按钮 - 减少pady值来缩小间距
        cred_button_frame = ttk.Frame(credential_frame)
        cred_button_frame.grid(row=3, column=0, columnspan=3, pady=2)  # 从pady=5改为pady=2

        ttk.Button(cred_button_frame, text="确认", command=self.save_credentials, style="TButton", width=10).pack(
            side="left", padx=5)
        ttk.Button(cred_button_frame, text="清空", command=self.clear_credentials, style="TButton", width=10).pack(
            side="left", padx=5)

        # 操作日志区域（右边）
        log_frame = ttk.LabelFrame(main_info_frame, text="操作日志", padding="10")
        log_frame.grid(row=0, column=1, padx=(5, 0), pady=0, sticky="ew")

        log_header = ttk.Frame(log_frame)
        log_header.grid(row=0, column=0, sticky="ew", pady=(0, 5))

        ttk.Button(log_header, text="清空日志", command=self.clear_operation_log, width=12).pack(side="right")

        self.operation_log_text = scrolledtext.ScrolledText(log_frame, height=5, width=56, font=("Microsoft YaHei", 9),
                                                            state='disabled')
        self.operation_log_text.grid(row=1, column=0, sticky="ew")

        # 配置列权重
        main_info_frame.columnconfigure(0, weight=1)
        main_info_frame.columnconfigure(1, weight=2)

        # 主要功能按钮区域 - 减少pady值来缩小与上方的间距
        button_frame = ttk.Frame(self.root, padding="5")  # 从padding="10"改为padding="5"
        button_frame.grid(row=2, column=0, columnspan=7, padx=10, pady=2, sticky="ew")  # 从pady=5改为pady=2

        # 创建一个内部框架来居中按钮
        inner_button_frame = ttk.Frame(button_frame)
        inner_button_frame.pack(anchor="center")

        ttk.Button(inner_button_frame, text="填写车型信息", command=self.open_template_excel, style="TButton",
                   width=14).pack(side="left", padx=5)
        ttk.Button(inner_button_frame, text="确定处理", command=self.process_files, style="Confirm.TButton",
                   width=14).pack(side="left", padx=5)
        ttk.Button(inner_button_frame, text="打开输出文件夹", command=self.open_output_folder, style="TButton",
                   width=14).pack(side="left", padx=5)

        # 状态标签 - 减少pady值
        self.status_label = tk.Label(self.root, text="", font=small_font, fg="blue")
        self.status_label.grid(row=3, column=0, columnspan=7, padx=10, pady=2, sticky="ew")  # 从pady=5改为pady=2

        # 分隔线 - 减少pady值
        ttk.Separator(self.root, orient="horizontal").grid(row=4, column=0, columnspan=7, sticky="ew",
                                                           pady=3)  # 从pady=5改为pady=3

        # 日志和模板管理按钮 - 减少padding和pady值
        manage_frame = ttk.Frame(self.root, padding="5")  # 从padding="10"改为padding="5"
        manage_frame.grid(row=5, column=0, columnspan=7, padx=10, pady=2)  # 从pady=5改为pady=2

        ttk.Button(manage_frame, text="打开主程序日志", command=self.open_process_log, style="TButton", width=16).pack(
            side="left", padx=5)
        ttk.Button(manage_frame, text="打开子进程日志", command=self.open_subprocess_log, style="TButton",
                   width=16).pack(side="left", padx=5)
        ttk.Button(manage_frame, text="打开更新模板文件夹", command=self.open_update_templates, style="TButton",
                   width=18).pack(side="left", padx=5)
        ttk.Button(manage_frame, text="替换模板", command=self.replace_templates, style="TButton", width=12).pack(
            side="left", padx=5)

        self.load_operation_log()

    def open_template_excel(self):
        excel_path = self.base_path / "Fill_Template_Data.xlsx"
        try:
            if not excel_path.exists():
                messagebox.showerror("错误", f"未找到 Fill_Template_Data.xlsx 文件: {excel_path}")
                return
            os.startfile(excel_path)
            self.logger.info(f"成功打开 Fill_Template_Data.xlsx")
        except Exception as e:
            messagebox.showerror("错误", f"打开 Fill_Template_Data.xlsx 失败: {str(e)}")

    def open_process_log(self):
        try:
            if not self.process_log_file.exists():
                self.process_log_file.touch()
            os.startfile(self.process_log_file)
        except Exception as e:
            messagebox.showerror("错误", f"打开主程序日志失败: {str(e)}")

    def open_subprocess_log(self):
        try:
            if not self.subprocess_log_file.exists():
                self.subprocess_log_file.touch()
            os.startfile(self.subprocess_log_file)
        except Exception as e:
            messagebox.showerror("错误", f"打开子进程日志失败: {str(e)}")

    def open_update_templates(self):
        try:
            self.update_templates_path.mkdir(exist_ok=True)
            os.startfile(self.update_templates_path)
        except Exception as e:
            messagebox.showerror("错误", f"打开更新模板文件夹失败: {str(e)}")

    def replace_templates(self):
        try:
            self.update_templates_path.mkdir(exist_ok=True)
            self.templates_path.mkdir(exist_ok=True)
            replaced_files = []
            for update_file in self.update_templates_path.glob("*"):
                if not update_file.name.startswith("XX项目VSE系统"):
                    continue
                for keyword in self.keywords:
                    if keyword in update_file.name:
                        for template_file in self.templates_path.glob(f"*{keyword}*"):
                            shutil.copy2(update_file, template_file)
                            replaced_files.append(f"{update_file.name} -> {template_file.name}")
                        break
            if replaced_files:
                messagebox.showinfo("成功", f"替换模板完成: {len(replaced_files)} 个文件")
            else:
                messagebox.showwarning("警告", "未找到符合条件的模板文件进行替换")

            self.clear_folder(self.update_templates_path)
        except Exception as e:
            messagebox.showerror("错误", f"替换模板失败: {str(e)}")

    def clear_operation_log(self):
        try:
            self.operation_log_text.configure(state='normal')
            self.operation_log_text.delete("1.0", tk.END)
            self.operation_log_text.configure(state='disabled')
            with open(self.operation_log_file, 'w', encoding='utf-8') as f:
                f.write("")
        except Exception as e:
            messagebox.showerror("错误", f"清空操作日志失败: {str(e)}")

    def toggle_all_ops(self):
        self.select_all_state = not self.select_all_state
        self.apply_fill_var.set(self.select_all_state)
        self.content_fill_var.set(self.select_all_state)

    def toggle_all_files(self):
        self.select_all_state = not self.select_all_state
        for var in self.file_vars.values():
            var.set(self.select_all_state)

    def copy_and_rename(self, src_file, dst_dir, vehicle_code):
        os.makedirs(dst_dir, exist_ok=True)
        file_name = src_file.name
        if file_name.startswith("XX"):
            new_name = f"{vehicle_code}{file_name[2:]}"
        else:
            new_name = file_name
        dst_path = dst_dir / new_name
        shutil.copy2(src_file, dst_path)
        return dst_path

    def move_to_output(self, src_dir, output_dir, subfolder_name=None):
        os.makedirs(output_dir, exist_ok=True)
        files = list(src_dir.glob("*"))
        if not files:
            return
        for file in files:
            dst_path = output_dir / file.name
            if dst_path.exists():
                subfolder = output_dir / subfolder_name
                os.makedirs(subfolder, exist_ok=True)
                dst_path = subfolder / file.name
            shutil.move(file, dst_path)

    def clear_folder(self, folder):
        if folder.exists():
            for item in folder.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()

    def check_input_files(self, input_dir):
        files = list(input_dir.glob("*"))
        return len(files) > 0

    def log_operation(self, operation, file_type, status):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} {operation}: {file_type} {status}"
        self.operation_log_text.configure(state='normal')
        self.operation_log_text.insert(tk.END, log_entry + "\n")
        self.operation_log_text.configure(state='disabled')
        self.operation_log_text.see(tk.END)
        with open(self.operation_log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")

    def load_operation_log(self):
        if self.operation_log_file.exists():
            with open(self.operation_log_file, 'r', encoding='utf-8') as f:
                self.operation_log_text.configure(state='normal')
                self.operation_log_text.insert(tk.END, f.read())
                self.operation_log_text.configure(state='disabled')
                self.operation_log_text.see(tk.END)

    def open_output_folder(self):
        try:
            self.output_path.mkdir(exist_ok=True)
            os.startfile(self.output_path)
        except Exception as e:
            messagebox.showerror("错误", f"打开输出文件夹失败: {str(e)}")

    def update_status_label(self, text):
        self.status_label.config(text=text)
        self.root.update()

    def clear_status_label(self):
        self.status_label.config(text="")
        self.root.update()

    def process_files(self):
        if not (self.apply_fill_var.get() or self.content_fill_var.get()):
            messagebox.showwarning("警告", "请至少选择一种操作类型")
            self.clear_status_label()
            return
        if not any(var.get() for var in self.file_vars.values()):
            messagebox.showwarning("警告", "请至少选择一种文件类型")
            self.clear_status_label()
            return
        if not self.vehicle_code:
            self.clear_status_label()
            return

        self.update_status_label("程序处理中...")
        selected_files = [ft for ft, var in self.file_vars.items() if var.get()]
        self.logger.info(
            f"选择的处理类型: 申请并填充编号={self.apply_fill_var.get()}, 填充文件内容={self.content_fill_var.get()}")
        self.logger.info(f"选择的文件类型: {selected_files}")

        input_dirs = {
            "DVP": self.fillin_path / "dvp" / "input",
            "PPL": self.fillin_path / "ppl" / "inputs",
            "FN": self.fillin_path / "fn" / "inputs"
        }
        output_dirs = {
            "DVP": self.fillin_path / "dvp" / "output",
            "PPL": self.fillin_path / "ppl" / "outputs",
            "FN": self.fillin_path / "fn" / "outputs"
        }
        scripts = {
            "DVP": self.fillin_path / "dvp" / "main.py",
            "PPL": self.fillin_path / "ppl" / "src" / "main.py",
            "FN": self.fillin_path / "fn" / "src" / "main.py"
        }

        main_output_dir = self.output_path
        applyid_subfolder = main_output_dir / "申请并填充编号的文件"
        content_subfolder = main_output_dir / "填充文件内容的文件"

        # 确保主输出目录存在并可写
        main_output_dir.mkdir(exist_ok=True)
        if not os.access(str(main_output_dir), os.W_OK):
            self.logger.error(f"主输出目录 {main_output_dir} 没有写入权限")
            messagebox.showerror("错误", f"无法写入主输出目录: {main_output_dir}")
            self.update_status_label("处理失败")
            self.root.after(2000, self.clear_status_label)
            return

        applyid_output_dir = self.apply_id_path / "output_files"
        all_success = True
        dirs_to_clear = []

        if self.apply_fill_var.get():
            input_dir = self.apply_id_path / "input_files"
            dirs_to_clear.append(input_dir)
            dirs_to_clear.append(applyid_output_dir)

            self.clear_folder(input_dir)
            self.clear_folder(applyid_output_dir)
            applyid_subfolder.mkdir(parents=True, exist_ok=True)

            for file_type in selected_files:
                pattern, ext = self.file_patterns[file_type]
                if file_type in ["DVP", "PPL"]:
                    glob_pattern = f"*{pattern}*{ext}"
                    glob_pattern1 = f"*{self.vehicle_code}*{pattern}*{ext}"
                else:
                    parts = pattern.split(' * ')
                    glob_pattern = f"*{parts[0]}*{parts[1]}*{ext}"
                    glob_pattern1 = f"*{self.vehicle_code}*{parts[0]}*{parts[1]}*{ext}"

                files_found = False
                if content_subfolder.exists():
                    files = list(content_subfolder.glob(glob_pattern1))
                    if files:
                        files_found = True
                        for file in files:
                            self.copy_and_rename(file, input_dir, self.vehicle_code)
                            self.logger.info(f"从填充文件内容子文件夹复制: {file} -> {input_dir}")

                if not files_found:
                    files = list(self.templates_path.glob(glob_pattern))
                    if not files:
                        self.logger.warning(
                            f"未在 {self.templates_path} 和 {content_subfolder} 找到匹配 {glob_pattern} 的文件")
                        messagebox.showwarning("警告",
                                               f"未找到 {file_type} 的匹配文件，请检查文件名是否包含 '{pattern}'")
                        self.log_operation("申请并填充编号", file_type, "失败", main_output=f"未找到匹配文件")
                        all_success = False
                        self.update_status_label("处理失败")
                        self.root.after(2000, self.clear_status_label)
                        return
                    else:
                        for file in files:
                            self.copy_and_rename(file, input_dir, self.vehicle_code)
                            self.logger.info(f"从Templates复制: {file} -> {input_dir}")

            if not self.check_input_files(input_dir):
                messagebox.showwarning("警告", "ApplyID 输入目录为空，跳过运行 ApplyID main.py")
                self.log_operation("申请并填充编号", ", ".join(selected_files), "失败",
                                   main_output="ApplyID 输入目录为空")
                self.update_status_label("处理失败")
                self.root.after(2000, self.clear_status_label)
                return

            if not self.update_config_yaml(self.vehicle_code):
                self.log_operation("申请并填充编号", ", ".join(selected_files), "失败",
                                   main_output="更新 config.yaml 失败")
                self.update_status_label("处理失败")
                self.root.after(2000, self.clear_status_label)
                return

            try:
                script_path = self.base_path / "ApplyID" / "src" / "main.py"
                self.logger.info(f"运行脚本: {script_path}")
                result = subprocess.run(
                    [sys.executable, str(script_path)],
                    cwd=str(script_path.parent),
                    capture_output=True, text=True, encoding='utf-8', errors='replace'
                )
                self.logger.info(f"ApplyID main.py 输出: {result.stdout}")
                with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now()}] ApplyID main.py:\n{result.stdout}\n{result.stderr}\n")

                output_files = list(applyid_output_dir.iterdir())
                self.logger.info(f"ApplyID 输出目录内容: {output_files}")
                if not output_files:
                    self.logger.warning(f"ApplyID 输出目录 {applyid_output_dir} 为空")
                    messagebox.showwarning("警告", f"ApplyID main.py 未生成输出文件")
                    self.log_operation("申请并填充编号", ", ".join(selected_files), "失败",
                                       main_output="未生成输出文件")
                    all_success = False
                    self.update_status_label("处理失败")
                    self.root.after(2000, self.clear_status_label)
                    return

                self.logger.info(f"开始复制到主输出目录: {applyid_output_dir} -> {main_output_dir}")
                for file in applyid_output_dir.iterdir():
                    if file.is_file():
                        dest_file = main_output_dir / file.name
                        try:
                            shutil.copy2(file, dest_file)
                            self.logger.info(f"复制到主输出目录: {file} -> {dest_file}")
                        except Exception as e:
                            self.logger.error(f"复制到主输出目录失败: {file} -> {dest_file}, 错误: {str(e)}")
                            all_success = False

                self._move_to_applyid_subfolder(applyid_output_dir, applyid_subfolder)

            except subprocess.CalledProcessError as e:
                self.logger.error(f"运行ApplyID main.py失败: {e.stderr}")
                with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now()}] ApplyID main.py 错误:\n{e.stderr}\n")
                messagebox.showerror("错误", f"运行ApplyID main.py失败: {e.stderr}")
                self.log_operation("申请并填充编号", ", ".join(selected_files), "失败",
                                   main_output=str(self.logger.handlers[0].stream.getvalue()),
                                   subprocess_output=e.stderr)
                self.update_status_label("处理失败")
                self.root.after(2000, self.clear_status_label)
                all_success = False
                return
            except UnicodeDecodeError as e:
                self.logger.error(f"ApplyID main.py 输出解码失败: {str(e)}")
                with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now()}] ApplyID main.py 解码错误:\n{str(e)}\n")
                messagebox.showerror("错误", f"ApplyID main.py 输出解码失败: {str(e)}")
                self.log_operation("申请并填充编号", ", ".join(selected_files), "失败",
                                   main_output=str(self.logger.handlers[0].stream.getvalue()), subprocess_output=str(e))
                self.update_status_label("处理失败")
                self.root.after(2000, self.clear_status_label)
                all_success = False
                return

        if self.content_fill_var.get():
            for dir_type in input_dirs.values():
                dirs_to_clear.append(dir_type)
                self.clear_folder(dir_type)
            for dir_type in output_dirs.values():
                dirs_to_clear.append(dir_type)
                self.clear_folder(dir_type)

            content_subfolder.mkdir(parents=True, exist_ok=True)

            fn_files = [ft for ft in selected_files if ft not in ["DVP", "PPL"]]
            dvp_files = [ft for ft in selected_files if ft == "DVP"]
            ppl_files = [ft for ft in selected_files if ft == "PPL"]

            for file_type in selected_files:
                dir_key = "DVP" if file_type == "DVP" else "PPL" if file_type == "PPL" else "FN"
                pattern, ext = self.file_patterns[file_type]
                if file_type in ["DVP", "PPL"]:
                    glob_pattern = f"*{pattern}*{ext}"
                    glob_pattern2 = f"*{self.vehicle_code}*{pattern}*{ext}"
                else:
                    parts = pattern.split(' * ')
                    glob_pattern = f"*{parts[0]}*{parts[1]}*{ext}"
                    glob_pattern2 = f"*{self.vehicle_code}*{parts[0]}*{parts[1]}*{ext}"

                files_found = False
                if applyid_subfolder.exists():
                    files = list(applyid_subfolder.glob(glob_pattern2))
                    if files:
                        files_found = True
                        for file in files:
                            self.copy_and_rename(file, input_dirs[dir_key], self.vehicle_code)
                            self.logger.info(f"从申请并填充编号子文件夹复制: {file} -> {input_dirs[dir_key]}")

                if not files_found:
                    files = list(self.templates_path.glob(glob_pattern))
                    if not files:
                        self.logger.warning(
                            f"未在 {applyid_subfolder} 和 {self.templates_path} 找到匹配 {glob_pattern} 的文件")
                        messagebox.showwarning("警告",
                                               f"未找到 {file_type} 的匹配文件，请检查文件名是否包含 '{pattern}'")
                        self.log_operation("填充文件内容", file_type, "失败", main_output=f"未找到匹配文件")
                        all_success = False
                        self.update_status_label("处理失败")
                        self.root.after(2000, self.clear_status_label)
                        return
                    else:
                        for file in files:
                            self.copy_and_rename(file, input_dirs[dir_key], self.vehicle_code)
                            self.logger.info(f"从Templates复制: {file} -> {input_dirs[dir_key]}")

            for file_type in dvp_files + ppl_files:
                dir_key = "DVP" if file_type == "DVP" else "PPL"
                if not self.check_input_files(input_dirs[dir_key]):
                    self.logger.warning(f"跳过运行 {dir_key} main.py，因为输入目录为空")
                    self.log_operation("填充文件内容", file_type, "失败",
                                       main_output=f"输入目录 {input_dirs[dir_key]} 为空")
                    self.update_status_label("处理失败")
                    self.root.after(2000, self.clear_status_label)
                    all_success = False
                    continue
                try:
                    script_path = scripts[dir_key]
                    self.logger.info(f"运行脚本: {script_path} (文件类型: {file_type})")
                    result = subprocess.run(
                        [sys.executable, str(script_path)],
                        cwd=str(script_path.parent),
                        capture_output=True, text=True, encoding='utf-8', errors='replace'
                    )
                    self.logger.info(f"{dir_key} main.py 输出 (文件类型: {file_type}): {result.stdout}")
                    with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                        f.write(
                            f"[{datetime.now()}] {dir_key} main.py ({file_type}):\n{result.stdout}\n{result.stderr}\n")

                    output_files = list(output_dirs[dir_key].iterdir())
                    self.logger.info(f"{dir_key} 输出目录内容: {output_files}")
                    if not output_files:
                        self.logger.warning(f"{dir_key} 输出目录 {output_dirs[dir_key]} 为空")
                        messagebox.showwarning("警告", f"{dir_key} main.py 未生成输出文件")
                        self.log_operation("填充文件内容", file_type, "失败", main_output="未生成输出文件")
                        all_success = False
                        self.update_status_label("处理失败")
                        self.root.after(2000, self.clear_status_label)
                        continue

                    self.logger.info(f"开始复制到主输出目录: {output_dirs[dir_key]} -> {main_output_dir}")
                    for file in output_dirs[dir_key].iterdir():
                        if file.is_file():
                            dest_file = main_output_dir / file.name
                            try:
                                shutil.copy2(file, dest_file)
                                self.logger.info(f"复制到主输出目录: {file} -> {dest_file}")
                            except Exception as e:
                                self.logger.error(f"复制到主输出目录失败: {file} -> {dest_file}, 错误: {str(e)}")
                                all_success = False

                    self._move_to_content_subfolder(output_dirs[dir_key], content_subfolder)

                    self.log_operation("填充文件内容", file_type, "成功")
                except subprocess.CalledProcessError as e:
                    self.logger.error(f"运行{dir_key} main.py失败 (文件类型: {file_type}): {e.stderr}")
                    with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] {dir_key} main.py ({file_type}) 错误:\n{e.stderr}\n")
                    messagebox.showerror("错误", f"运行{dir_key} main.py失败 ({file_type}): {e.stderr}")
                    self.log_operation("填充文件内容", file_type, "失败",
                                       main_output=str(self.logger.handlers[0].stream.getvalue()),
                                       subprocess_output=e.stderr)
                    self.update_status_label("处理失败")
                    self.root.after(2000, self.clear_status_label)
                    all_success = False
                    return
                except UnicodeDecodeError as e:
                    self.logger.error(f"{dir_key} main.py 输出解码失败 (文件类型: {file_type}): {str(e)}")
                    with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] {dir_key} ({file_type}) 解码错误:\n{str(e)}\n")
                    messagebox.showerror("错误", f"{dir_key} main.py 输出解码失败 ({file_type}): {str(e)}")
                    self.log_operation("填充文件内容", file_type, "失败",
                                       main_output=str(self.logger.handlers[0].stream.getvalue()),
                                       subprocess_output=str(e))
                    self.update_status_label("处理失败")
                    self.root.after(2000, self.clear_status_label)
                    all_success = False
                    return

            if fn_files:
                if not self.check_input_files(input_dirs["FN"]):
                    self.logger.warning(f"跳过运行 FN main.py，因为输入目录为空")
                    self.log_operation("填充文件内容", ", ".join(fn_files), "失败",
                                       main_output=f"输入目录 {input_dirs['FN']} 为空")
                    self.update_status_label("处理失败")
                    self.root.after(2000, self.clear_status_label)
                    all_success = False
                else:
                    try:
                        script_path = scripts["FN"]
                        self.logger.info(f"运行脚本: {script_path} (文件类型: {', '.join(fn_files)})")
                        result = subprocess.run(
                            [sys.executable, str(script_path)],
                            cwd=str(script_path.parent),
                            capture_output=True, text=True, encoding='utf-8', errors='replace'
                        )
                        self.logger.info(f"FN main.py 输出 (文件类型: {', '.join(fn_files)}): {result.stdout}")
                        with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                            f.write(
                                f"[{datetime.now()}] FN main.py ({', '.join(fn_files)}):\n{result.stdout}\n{result.stderr}\n")

                        output_files = list(output_dirs["FN"].iterdir())
                        self.logger.info(f"FN 输出目录内容: {output_files}")
                        if not output_files:
                            self.logger.warning(f"FN 输出目录 {output_dirs['FN']} 为空")
                            messagebox.showwarning("警告", f"FN main.py 未生成输出文件")
                            self.log_operation("填充文件内容", ", ".join(fn_files), "失败",
                                               main_output="未生成输出文件")
                            all_success = False
                            self.update_status_label("处理失败")
                            self.root.after(2000, self.clear_status_label)
                            return

                        self.logger.info(f"开始复制到主输出目录: {output_dirs['FN']} -> {main_output_dir}")
                        for file in output_dirs["FN"].iterdir():
                            if file.is_file():
                                dest_file = main_output_dir / file.name
                                try:
                                    shutil.copy2(file, dest_file)
                                    self.logger.info(f"复制到主输出目录: {file} -> {dest_file}")
                                except Exception as e:
                                    self.logger.error(f"复制到主输出目录失败: {file} -> {dest_file}, 错误: {str(e)}")
                                    all_success = False

                        self._move_to_content_subfolder(output_dirs["FN"], content_subfolder)

                        self.log_operation("填充文件内容", ", ".join(fn_files), "成功")
                    except subprocess.CalledProcessError as e:
                        self.logger.error(f"运行FN main.py失败 (文件类型: {', '.join(fn_files)}): {e.stderr}")
                        with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] FN main.py ({', '.join(fn_files)}) 错误:\n{e.stderr}\n")
                        messagebox.showerror("错误", f"运行FN main.py失败 ({', '.join(fn_files)}): {e.stderr}")
                        self.log_operation("填充文件内容", ", ".join(fn_files), "失败",
                                           main_output=str(self.logger.handlers[0].stream.getvalue()),
                                           subprocess_output=e.stderr)
                        self.update_status_label("处理失败")
                        self.root.after(2000, self.clear_status_label)
                        all_success = False
                        return
                    except UnicodeDecodeError as e:
                        self.logger.error(f"FN main.py 输出解码失败 (文件类型: {', '.join(fn_files)}): {str(e)}")
                        with open(self.subprocess_log_file, 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] FN main.py ({', '.join(fn_files)}) 解码错误:\n{str(e)}\n")
                        messagebox.showerror("错误", f"FN main.py 输出解码失败 ({', '.join(fn_files)}): {str(e)}")
                        self.log_operation("填充文件内容", ", ".join(fn_files), "失败",
                                           main_output=str(self.logger.handlers[0].stream.getvalue()),
                                           subprocess_output=str(e))
                        self.update_status_label("处理失败")
                        self.root.after(2000, self.clear_status_label)
                        all_success = False
                        return

        if all_success:
            for dir_path in dirs_to_clear:
                self.clear_folder(dir_path)
            self.log_operation("处理文件", ", ".join(selected_files), "成功")
            self.update_status_label("处理完成")
            self.root.after(2000, self.clear_status_label)
            messagebox.showinfo("完成", "文件处理完成！")
        else:
            self.update_status_label("处理失败")
            self.root.after(2000, self.clear_status_label)

    def _move_to_applyid_subfolder(self, source_dir, target_subfolder):
        """将文件移动到申请并填充编号的子文件夹，文件名相同时添加后缀"""
        if not source_dir.exists():
            return

        for file in source_dir.iterdir():
            if file.is_file():
                dest_file = target_subfolder / file.name

                # 如果文件已存在，添加后缀
                if dest_file.exists():
                    counter = 1
                    stem = dest_file.stem
                    suffix = dest_file.suffix
                    while dest_file.exists():
                        new_name = f"{stem}_{counter}{suffix}"
                        dest_file = target_subfolder / new_name
                        counter += 1

                shutil.move(file, dest_file)
                self.logger.info(f"移动到申请并填充编号子文件夹: {file} -> {dest_file}")

    def _move_to_content_subfolder(self, source_dir, target_subfolder):
        """将文件移动到填充文件内容的子文件夹，文件名相同时先移动旧文件到"同名的文件"子文件夹"""
        if not source_dir.exists():
            return

        # 创建"同名的文件"子文件夹
        duplicate_folder = target_subfolder / "同名的文件"
        duplicate_folder.mkdir(parents=True, exist_ok=True)

        for file in source_dir.iterdir():
            if file.is_file():
                dest_file = target_subfolder / file.name

                # 如果文件已存在，先将旧文件移动到"同名的文件"子文件夹
                if dest_file.exists():
                    duplicate_dest = duplicate_folder / file.name

                    # 如果"同名的文件"子文件夹中也存在同名文件，添加后缀
                    if duplicate_dest.exists():
                        counter = 1
                        stem = duplicate_dest.stem
                        suffix = duplicate_dest.suffix
                        while duplicate_dest.exists():
                            new_name = f"{stem}_{counter}{suffix}"
                            duplicate_dest = duplicate_folder / new_name
                            counter += 1

                    shutil.move(dest_file, duplicate_dest)
                    self.logger.info(f"移动旧文件到同名的文件子文件夹: {dest_file} -> {duplicate_dest}")

                shutil.move(file, dest_file)
                self.logger.info(f"移动到填充文件内容子文件夹: {file} -> {dest_file}")

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()