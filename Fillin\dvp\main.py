from src.file_handler import find_target_file, get_output_path
from src.excel_processor import ExcelProcessor
from src.reminder import show_reminder

def main():
    file_path = find_target_file()
    if not file_path:
        return
    output_path = get_output_path(file_path)
    processor = ExcelProcessor(file_path, enable_popup=False, enable_console_reminder=True)
    processor.process(output_path)
    show_reminder(processor.enable_popup, processor.enable_console_reminder)

if __name__ == "__main__":
    main()