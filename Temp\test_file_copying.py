#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的文件复制功能
"""

import sys
import os
from pathlib import Path
import pandas as pd

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from file_manager import FileManager

def test_file_copying():
    """测试文件复制功能"""
    print("开始测试文件复制功能...")
    
    base_path = Path(__file__).parent
    file_manager = FileManager(base_path)
    
    # 测试车型代号
    test_vehicle = "TEST02"
    
    try:
        # 首先设置车型文件夹
        print(f"设置车型文件夹: {test_vehicle}")
        created, target_file = file_manager.setup_vehicle_folder(test_vehicle)
        
        if created:
            print(f"✅ 成功创建车型文件夹和模板文件: {target_file}")
        else:
            print(f"ℹ️  车型文件夹已存在: {target_file}")
        
        # 测试复制模板文件
        selected_files = ["DVP", "PPL", "FN_VCU", "FN_IPB", "FN_域控"]
        print(f"\n测试复制文件: {selected_files}")
        
        copied_files = file_manager.copy_template_files(test_vehicle, selected_files)
        
        print(f"✅ 成功复制文件: {copied_files}")
        
        # 检查复制的文件是否存在
        vehicle_dir = file_manager.vehicles_path / test_vehicle
        print(f"\n检查复制的文件:")
        for file_name in copied_files:
            file_path = vehicle_dir / file_name
            if file_path.exists():
                print(f"✅ 文件存在: {file_name}")
                
                # 检查文件内容是否已替换XX
                if file_path.suffix == '.xlsx':
                    try:
                        df = pd.read_excel(file_path, sheet_name=0)  # 读取第一个sheet
                        content = df.to_string()
                        if test_vehicle in content and 'XX' not in content:
                            print(f"  ✅ 文件内容已正确替换XX为{test_vehicle}")
                        elif test_vehicle in content:
                            print(f"  ✅ 文件内容包含{test_vehicle}")
                        else:
                            print(f"  ⚠️  文件内容可能未正确替换")
                    except:
                        print(f"  ⚠️  无法读取Excel内容")
                else:
                    print(f"  ℹ️  Word文件内容替换功能待完善")
            else:
                print(f"❌ 文件不存在: {file_name}")
        
        # 检查File_Status表是否正确更新
        print(f"\n检查File_Status表:")
        files_info = file_manager.get_vehicle_files_info(test_vehicle)
        print(f"File_Status表中的文件数量: {len(files_info)}")
        for info in files_info:
            print(f"  - {info['file_name']}: 编号={info['code']}, 填写={info['is_fillin']}, 上传={info['is_upload']}")
        
        print("✅ 文件复制功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_file_copying()
