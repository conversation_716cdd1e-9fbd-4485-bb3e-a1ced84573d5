# 上传审批自动化程序 - 完善版

## 项目结构

已经成功将上传审批自动化程序拆分为多个模块，并完善了第一个页面的自动化流程：

### 核心模块

1. **browser_manager.py** - 浏览器管理
   - 浏览器启动和配置
   - 用户登录
   - 弹窗处理

2. **first_page_handler.py** - 第一个页面处理（已完善）
   - 填写文档编号并等待页面加载
   - 滚动到页面底部
   - 填写所属项目阶段（B版）并选中浮动项
   - 填写交付物级别（项目级）并选中浮动项
   - 填写文件内容简要（使用文档名称）
   - 上传源文件
   - 删除自动转换的PDF
   - 上传正确的PDF文件
   - 点击"保存并发起评审"

3. **second_page_handler.py** - 第二个页面处理
   - 填写评审方案
   - 选择评审人员
   - 设置截止日期
   - 发起评审

4. **utils.py** - 通用工具
   - 文件处理和配对
   - 数据管理
   - 配置加载
   - 日志设置

5. **main_controller.py** - 主流程控制
   - 协调各模块
   - 执行完整流程

6. **config.py** - 配置文件
   - 系统参数配置

7. **run.py** - 启动脚本
   - 程序入口点

## 第一个页面流程完善

根据用户要求，第一个页面的自动化流程已完善为：

1. **填写文档编号** - 输入后等待页面加载
2. **等待页面加载** - 等待5秒让页面加载更多项
3. **滚动到底部** - 确保所有元素可见
4. **填写所属项目阶段** - 选择"B版"并点击浮动项确认
5. **填写交付物级别** - 选择"项目级"并点击浮动项确认
6. **填写文件内容简要** - 使用文档名称（去掉扩展名）
7. **上传源文件** - 上传第一个文件
8. **删除自动转换PDF** - 如果系统自动生成了PDF则删除
9. **上传正确PDF** - 如果有配对的PDF文件则上传
10. **保存并发起评审** - 点击按钮进入下一个页面

## 多种元素定位方式

每个关键操作都实现了多种定位方式：

- **XPath路径定位**
- **ID属性定位**
- **Class属性定位**
- **文本内容定位**
- **相对位置定位**

这确保了程序的稳定性，当某种定位方式失效时，会自动尝试其他方式。

## 使用方法

1. 确保所有依赖文件在同一目录下
2. 运行 `python run.py` 启动程序
3. 程序会自动处理 `Final_Approval_Documents` 文件夹中的文档

## 后续优化

等待用户提供控制台输出后，可以进一步：
- 去除无效的元素定位方式
- 优化异常处理
- 调整等待时间
- 完善错误日志

程序已按照JSON录制文件和HTML结构严格实现流程，并具备良好的容错性和可维护性。
