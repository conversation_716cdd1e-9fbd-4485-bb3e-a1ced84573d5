# 模板文件查找和复制问题修正总结

## 已解决的问题

### 1. 模板文件查找逻辑问题
**原问题**: 代码使用完整的硬编码文件名查找模板，导致找不到实际存在的文件
```
2025-06-24 14:25:59,718 - WARNING - 模板文件不存在: 软件设计验证计划.xlsx
```

**修正方案**: 
- 创建`find_template_file()`方法，使用关键字模式匹配
- 支持文件名的灵活匹配，避免因细微差异导致查找失败
- 保留原精确匹配作为后备方案

### 2. 文件名前缀替换
**需求**: 复制模板文件时，将文件名中的"XX"替换为实际车型代号
**实现**: 
- 在`copy_template_files()`中生成目标文件名时自动替换
- 支持文件内容中的"XX"替换（Excel文件）
- Word文件内容替换功能预留接口

## 关键修改

### 1. 更新文件映射配置
```python
self.file_mappings = {
    "DVP": {
        "template": "XX项目VSE软件设计验证计划（DVP）.xlsx",  # 原始映射
        "pattern": "软件设计验证计划",  # 关键字匹配
        "extension": ".xlsx"
    },
    "PPL": {
        "template": "XX项目VSE软件开发匹配测试计划.xlsx",
        "pattern": "软件开发匹配测试计划",
        "extension": ".xlsx"
    },
    # ... 其他文件类型
}
```

### 2. 智能模板文件查找
```python
def find_template_file(self, file_type):
    """根据文件类型查找模板文件"""
    # 1. 使用关键字在Templates文件夹中搜索
    for template_file in self.templates_path.glob(f"*{extension}"):
        if pattern in template_file.name:
            return template_file
    
    # 2. 后备方案：精确匹配原始文件名
    exact_file = self.templates_path / mapping["template"]
    if exact_file.exists():
        return exact_file
```

### 3. 文件名和内容替换
```python
def copy_template_files(self, vehicle_code, selected_files):
    # 生成目标文件名，将XX替换为车型代号
    target_name = f"{vehicle_code}项目{mapping['pattern']}{mapping['extension']}"
    
    # 复制文件后，替换文件内容中的XX
    self.replace_vehicle_code_in_file(target_path, vehicle_code)
```

## 测试结果

### ✅ 成功案例
- **DVP文件**: `XX项目VSE软件设计验证计划（DVP）.xlsx` → `TEST02项目软件设计验证计划.xlsx`
- **PPL文件**: `XX项目VSE软件开发匹配测试计划.xlsx` → `TEST02项目软件开发匹配测试计划.xlsx`
- **接口文件**: `XX项目VSE系统 to VCU系统接口定义通知单.docx` → `TEST02项目VCU系统接口定义通知单.docx`

### 📋 支持的文件类型
1. **DVP**: 软件设计验证计划
2. **PPL**: 软件开发匹配测试计划  
3. **FN_VCU**: VCU系统接口定义通知单
4. **FN_IPB**: IPB系统接口定义通知单
5. **FN_ESP_BWA**: ESP+BWA系统接口定义通知单
6. **FN_EPS**: EPS系统接口定义通知单
7. **FN_EPSA**: EPSA系统接口定义通知单
8. **FN_EPB**: EPB系统接口定义通知单
9. **FN_DISUS_A/C/P/X/M**: DiSus系列接口定义通知单
10. **FN_IMU**: 安全气囊节点接口定义通知单
11. **FN_域控**: 跨域计算平台接口定义通知单

## 优势

### 1. 灵活性提升
- 支持模板文件名的轻微变化
- 不再依赖完全匹配的文件名
- 容错性更好

### 2. 自动化程度提高
- 自动替换文件名前缀
- 自动更新File_Status表
- 支持文件内容的车型代号替换

### 3. 维护性改善
- 配置和查找逻辑分离
- 易于添加新的文件类型
- 清晰的日志输出

## 下一步改进

1. **完善Word文件内容替换**: 使用python-docx库实现
2. **优化Excel替换警告**: 处理数据类型兼容性问题
3. **增加文件验证**: 检查复制后的文件完整性
4. **支持批量操作**: 一次性处理多个车型

现在用户可以正常使用文件复制功能，系统会自动找到匹配的模板文件并正确复制和重命名！
