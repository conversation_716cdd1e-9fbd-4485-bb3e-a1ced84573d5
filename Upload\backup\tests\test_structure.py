"""
测试程序结构和导入
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        import config
        print("✅ config.py 导入成功")
    except Exception as e:
        print(f"❌ config.py 导入失败: {e}")
    
    try:
        import utils
        print("✅ utils.py 导入成功")
    except Exception as e:
        print(f"❌ utils.py 导入失败: {e}")
    
    try:
        import browser_manager
        print("✅ browser_manager.py 导入成功")
    except Exception as e:
        print(f"❌ browser_manager.py 导入失败: {e}")
    
    try:
        import first_page_handler
        print("✅ first_page_handler.py 导入成功")
    except Exception as e:
        print(f"❌ first_page_handler.py 导入失败: {e}")
    
    try:
        import second_page_handler
        print("✅ second_page_handler.py 导入成功")
    except Exception as e:
        print(f"❌ second_page_handler.py 导入失败: {e}")
    
    try:
        import main_controller
        print("✅ main_controller.py 导入成功")
    except Exception as e:
        print(f"❌ main_controller.py 导入失败: {e}")

def test_file_structure():
    """测试文件结构"""
    print("\n📁 检查文件结构...")
    
    required_files = [
        "config.py",
        "utils.py", 
        "browser_manager.py",
        "first_page_handler.py",
        "second_page_handler.py",
        "main_controller.py",
        "run.py"
    ]
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")

def test_directories():
    """测试目录结构"""
    print("\n📂 检查目录结构...")
    
    required_dirs = [
        "Data",
        "Final_Approval_Documents"
    ]
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✅ {dir_name}/ 存在")
        else:
            print(f"❌ {dir_name}/ 不存在")

if __name__ == "__main__":
    print("🔧 上传审批自动化程序 - 结构测试")
    print("=" * 50)
    
    test_file_structure()
    test_directories()
    test_imports()
    
    print("\n" + "=" * 50)
    print("测试完成")
    input("按回车键退出...")
